FROM genn-repository-cn-beijing.cr.volces.com/genn-ops/base/openjdk8:v1
USER root

WORKDIR /appdata

ENV JVM_ARGS="-server \
              -Xms512m \
              -Xmx1024m \
              -XX:+UseG1GC \
              -XX:MaxGCPauseMillis=200 \
              -XX:+UseContainerSupport \
              -XX:+HeapDumpOnOutOfMemoryError \
              -XX:HeapDumpPath=./heap-dump.hprof \
              -XX:+PrintGCDetails \
              -XX:+PrintGCDateStamps \
              -Xloggc:./gc.log \
              -XX:+UseGCLogFileRotation \
              -XX:NumberOfGCLogFiles=10 \
              -XX:GCLogFileSize=100M \
              -Djava.awt.headless=true \
              -XX:+ExitOnOutOfMemoryError \
           -Dspring.cloud.nacos.config.server-addr=http://nacos-trans.pro.genn.cn:8848 \
            -Dspring.cloud.nacos.config.namespace=pro \
            -Dserver.port=8080"
ENV SKY_AGENT="-javaagent:/appdata/skywalking-agent/skywalking-agent.jar -DSW_AGENT_NAME=pro-genn-trans-support-fms -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking-trans.pro.genn.cn:11800"

COPY genn-trans-support-fms-service/target/genn-trans-support-fms.jar ./

CMD java $SKY_AGENT $JVM_ARGS -jar genn-trans-support-fms.jar

RUN mkdir temp
