# Magic-Token 优化方案说明

## 问题分析

### 当前实现的问题
1. **代码重复**：在每个导出服务中都需要手动添加 `magic-token` 请求头
2. **业务逻辑污染**：magic-token 的处理逻辑嵌入到业务代码中
3. **维护困难**：如果 magic-token 的处理逻辑需要修改，需要改动多个地方
4. **容易遗漏**：新增导出服务时容易忘记添加 magic-token 处理

### 当前代码示例
```java
// 每个导出服务都需要这样的重复代码
MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
headers.add("Content-Type", "application/json");
HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
```

## 优化方案

### 方案1：基于 Feign 拦截器（推荐用于新项目）

**优点**：
- 完全自动化，业务代码无需关心 magic-token
- 类型安全，支持强类型接口定义
- 与 Spring Cloud 生态集成良好

**实现**：
```java
@Component
public class MagicTokenRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        String tokenData = MagicTokenContext.getTokenData();
        if (StrUtil.isNotBlank(tokenData)) {
            String magicToken = MagicTokenHandleUtil.handleMagicToken(tokenData);
            template.header("magic-token", magicToken);
        }
    }
}
```

### 方案2：基于 RestTemplate 拦截器（推荐用于现有项目）

**优点**：
- 与现有代码兼容性好
- 改动最小，只需要替换 RestTemplate 实例
- 自动处理所有 HTTP 请求

**实现**：
```java
@Component
public class MagicTokenRestTemplateInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        String tokenData = MagicTokenContext.getTokenData();
        if (StrUtil.isNotBlank(tokenData)) {
            String magicToken = MagicTokenHandleUtil.handleMagicToken(tokenData);
            request.getHeaders().add("magic-token", magicToken);
        }
        return execution.execute(request, body);
    }
}
```

### 方案3：改造基类 + 上下文管理（推荐）

**优点**：
- 利用现有的继承结构
- 自动管理 tokenData 的生命周期
- 对现有代码改动最小

**实现**：
```java
public abstract class ExcelExportAbstractServer implements IExcelExportServer {
    @Override
    public List<Object> selectListForExcelExport(Object queryParams, int pageNo) {
        FileExportTaskDTO taskInfo = (FileExportTaskDTO) queryParams;
        
        // 设置MagicToken上下文，确保在调用远程服务时能自动添加magic-token请求头
        return MagicTokenContext.runWithTokenData(taskInfo.getTokenData(), () -> {
            // 原有的业务逻辑
            return queryListForExcelExport(taskInfo, pageNo);
        });
    }
}
```

### 方案4：封装 HTTP 客户端工具类

**优点**：
- 提供统一的 HTTP 调用接口
- 自动处理 magic-token 和异常
- 支持类型安全的响应处理

**使用示例**：
```java
@Component
public class ExampleExportServer extends ExcelExportAbstractServer {
    @Resource
    private MagicTokenHttpClient magicTokenHttpClient;
    
    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        // 不需要手动处理 magic-token，工具类会自动处理
        PageResultDTO<Object> result = magicTokenHttpClient.postForObject(
            url, 
            query, 
            new TypeReference<ResponseResult<PageResultDTO<Object>>>() {}
        );
        return result.getList();
    }
}
```

## 核心组件说明

### 1. MagicTokenContext
- **作用**：管理 tokenData 的线程本地存储
- **特点**：自动管理生命周期，支持嵌套调用
- **使用**：在需要传递 tokenData 的地方设置上下文

### 2. MagicTokenRequestInterceptor
- **作用**：Feign 请求拦截器，自动添加 magic-token 请求头
- **配置**：在 FeignConfig 中注册

### 3. MagicTokenRestTemplateInterceptor
- **作用**：RestTemplate 请求拦截器，自动添加 magic-token 请求头
- **配置**：在 RestTemplateConfig 中注册

### 4. MagicTokenHttpClient
- **作用**：封装的 HTTP 客户端工具类
- **特点**：自动处理 magic-token、异常处理、类型安全

## 迁移建议

### 阶段1：基础设施搭建
1. 创建 `MagicTokenContext` 上下文管理器
2. 创建 `MagicTokenRestTemplateInterceptor` 拦截器
3. 配置 RestTemplate 使用拦截器

### 阶段2：改造基类
1. 修改 `ExcelExportAbstractServer` 基类
2. 在 `selectListForExcelExport` 方法中设置上下文

### 阶段3：逐步迁移导出服务
1. 移除手动添加 magic-token 的代码
2. 使用配置了拦截器的 RestTemplate
3. 或者使用 `MagicTokenHttpClient` 工具类

### 阶段4：新服务使用新方案
1. 新的导出服务直接继承改造后的基类
2. 使用 `MagicTokenHttpClient` 或配置了拦截器的 RestTemplate
3. 不需要关心 magic-token 的处理

## 优势总结

1. **代码简洁**：业务代码不再需要手动处理 magic-token
2. **维护性好**：magic-token 处理逻辑集中管理
3. **扩展性强**：可以轻松添加其他通用请求头
4. **向后兼容**：现有代码可以逐步迁移
5. **类型安全**：支持强类型的 HTTP 调用

## 注意事项

1. **线程安全**：MagicTokenContext 使用 ThreadLocal，确保线程安全
2. **内存泄漏**：注意及时清理 ThreadLocal，避免内存泄漏
3. **异常处理**：在 finally 块中清理上下文
4. **测试**：确保在单元测试中正确设置和清理上下文
