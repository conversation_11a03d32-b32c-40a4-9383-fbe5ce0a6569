FROM genn-repository-cn-beijing.cr.volces.com/genn-ops/base/openjdk8:v1
USER root

WORKDIR /appdata

ENV JVM_ARGS="-Xms128m -Xmx256m \
               -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./heap-dump.hprof -XX:+UseG1GC \
           -Dspring.cloud.nacos.config.server-addr=http://nacos.uat.genn.cn:8848 \
            -Dspring.cloud.nacos.config.namespace=uat \
            -Dserver.port=8080"
ENV SKY_AGENT="-javaagent:/appdata/skywalking-agent/skywalking-agent.jar -DSW_AGENT_NAME=uat-genn-trans-support-fms -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking.genn.cn:11800"

COPY genn-trans-support-fms-service/target/genn-trans-support-fms.jar ./

CMD java $SKY_AGENT $JVM_ARGS -jar genn-trans-support-fms.jar

RUN mkdir temp
