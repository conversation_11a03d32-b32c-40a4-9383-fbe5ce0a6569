package cn.genn.trans.support.fms.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitClientCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.command.SingleIdCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.query.FileExportTaskQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 文件导出接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-fms", contextId = "fmsFileExportFeignService", path = "/api/fms/export")
public interface IFmsFileExportFeignService {

    /**
     * 提交文件导出任务 需登录态
     *
     * @param command
     * @return
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交导出任务")
    FileExportTaskDTO submit(@RequestBody FileExportTaskSubmitCommand command);

    /**
     * 提交导出任务 RPC调用 无登录态
     *
     * @param command
     * @return
     */
    @PostMapping("/submitForClient")
    @ApiOperation(value = "提交导出任务 RPC调用 无登录态")
    FileExportTaskDTO submitForClient(@ApiParam(value = "任务参数") FileExportTaskSubmitClientCommand command);

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    PageResultDTO<FileExportTaskDTO> page(@ApiParam(value = "查询类") @RequestBody FileExportTaskQuery query);

    /**
     * 根据id查询
     *
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    FileExportTaskDTO get(@RequestBody SingleIdCommand command);

    @PostMapping("/retry")
    @ApiOperation(value = "重试")
    void retry(@RequestBody SingleIdCommand command);

    @PostMapping("/cancel")
    @ApiOperation(value = "取消")
    void cancel(@RequestBody SingleIdCommand command);

}
