package cn.genn.trans.support.fms.interfaces.api;

import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitClientCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.command.FileImportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportTaskDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 文件导入接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-fms", contextId = "fmsFileImportFeignService", path = "/api/fms/import")
public interface IFmsFileImportFeignService {

    /**
     * 提交导入任务 需登录态
     *
     * @param command
     * @return
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交导入任务")
    FileImportTaskDTO submit(@RequestBody FileImportTaskSubmitCommand command);

    /**
     * 提交导入任务 RPC调用 无登录态
     *
     * @param command
     * @return
     */
    @PostMapping("/submitForClient")
    @ApiOperation(value = "提交导出任务 RPC调用 无登录态")
    FileImportTaskDTO submitForClient(@RequestBody FileExportTaskSubmitClientCommand command);

}
