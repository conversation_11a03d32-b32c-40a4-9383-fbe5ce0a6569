package cn.genn.trans.support.fms.interfaces.api;

import cn.genn.trans.support.fms.interfaces.command.FileDetailQueryCommand;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExternalLinkTransferCommand;
import cn.genn.trans.support.fms.interfaces.command.FileUploadImageOfBase64Command;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;


/**
 * 文件上传下载支持相关接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-fms", contextId = "fmsFileSupportFeignService", path = "/api/fms/support")
public interface IFmsFileSupportFeignService {

    @PostMapping("/uploadBase64")
    FileInfoDTO uploadBase64(@RequestBody FileUploadImageOfBase64Command command);


    @PostMapping("/getFileInfoByKey")
    FileInfoDTO getFileInfoByKey(@RequestParam(value = "fileKey") String fileKey);

    @PostMapping("/getFileInfoByKeys")
    List<FileInfoDTO> getFileInfoByKeys(@RequestBody FileDetailQueryCommand command);

    @PostMapping("/getFileInfoByKeyWithoutLogin")
    FileInfoDTO getFileInfoByKeyWithoutLogin(@RequestBody FileDetailQuerySingleCommand command);

    @PostMapping(value = "/externalLinkFileTransfer")
    FileInfoDTO externalLinkFileTransfer(@Valid @RequestBody FileExternalLinkTransferCommand command);

    @PostMapping(value = "/uploadWithoutLogin", consumes = {"multipart/form-data"})
    FileInfoDTO uploadWithoutLogin(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "businessCode") String businessCode,
                                          @RequestParam(value = "objectId", required = false) String objectId,
                                          @RequestParam(value = "objectType", required = false) String objectType,
                                          @RequestParam(value = "tenantId") Long tenantId,
                                          @RequestParam(value = "systemId") Long systemId,
                                          @RequestParam(value = "userId", required = false) Long userId,
                                          @RequestParam(value = "userName", required = false) String userName);

    @PostMapping(value = "/appUpload", consumes = {"multipart/form-data"})
    FileInfoDTO appUpload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "fileName") String fileName,
                                 @RequestParam(value = "businessCode") String businessCode,
                                 @RequestParam(value = "domainName") String domainName,
                                 @RequestParam(value = "objectId", required = false) String objectId,
                                 @RequestParam(value = "objectType", required = false) String objectType,
                                 @RequestParam(value = "tenantId") Long tenantId);
}
