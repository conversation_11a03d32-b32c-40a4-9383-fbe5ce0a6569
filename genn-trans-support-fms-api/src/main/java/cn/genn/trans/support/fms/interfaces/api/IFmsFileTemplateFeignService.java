package cn.genn.trans.support.fms.interfaces.api;

import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 文件模板接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-fms", contextId = "fmsFileTemplateFeignService", path = "/api/fms/template")
public interface IFmsFileTemplateFeignService {

    /**
     * 文件模板下载 需登录态 支持简单的参数传递
     *
     * @param command
     * @return
     */
    @PostMapping("/simple")
    @ApiOperation(value = "文件模板下载")
    FileInfoDTO simple(@RequestBody FileTemplateCreateCommand command);


    /**
     * 图片的模板替换
     *
     * @return
     */
    @PostMapping(value = "/pictureTemplate")
    FileInfoDTO pictureTemplate(@RequestBody FileTemplateCreateCommand command);
}
