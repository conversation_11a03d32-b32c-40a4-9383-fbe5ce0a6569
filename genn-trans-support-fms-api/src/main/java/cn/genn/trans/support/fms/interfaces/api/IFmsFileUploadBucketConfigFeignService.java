package cn.genn.trans.support.fms.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.command.FileUploadBucketConfigInitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBucketConfigQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 文件模板接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-fms", contextId = "fmsFileUploadBucketConfigFeignService", path = "/api/fms/fileUploadBucketConfig")
public interface IFmsFileUploadBucketConfigFeignService {

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    PageResultDTO<FileUploadBucketConfigDTO> page(@ApiParam(value = "查询类") @RequestBody FileUploadBucketConfigQuery query);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    FileUploadBucketConfigDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id);

    @PostMapping("/getSelectInitConfig")
    @ApiOperation(value = "查询可供选择的初始化配置")
    List<FileUploadBucketConfigDTO> getSelectInitConfig();

    @PostMapping("/initConfig")
    @ApiOperation(value = "初始化bucket配置")
    Boolean initConfig(@RequestBody FileUploadBucketConfigInitCommand command);

}
