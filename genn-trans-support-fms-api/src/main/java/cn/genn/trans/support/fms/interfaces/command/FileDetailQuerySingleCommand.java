package cn.genn.trans.support.fms.interfaces.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * 查询单个文件 无登录态
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileDetailQuerySingleCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fileKey;

    private String operatorName;
}

