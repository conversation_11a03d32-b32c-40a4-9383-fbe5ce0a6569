package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.core.model.valid.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;


/**
 * FileExportConf操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileExportConfOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "")
    private String config;


}

