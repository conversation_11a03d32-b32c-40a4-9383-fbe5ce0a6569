package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 提交导出任务
 *
 * <AUTHOR>
 */
@Data
public class FileExportTaskSubmitClientCommand implements Serializable {

    @ApiModelProperty(value = "业务场景")
    private String businessCode;
    @ApiModelProperty("子业务场景")
    private String subBusinessCode;
    @ApiModelProperty(value = "任务参数")
    private ConfigGridDTO taskParam;

    private Long systemId;
    private Long tenantId;
    private Long operatorId;
    private Long carrierId;
    private Long driverId;
    private Long userId;
    private String username;
}

