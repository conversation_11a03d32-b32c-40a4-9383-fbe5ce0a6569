package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 提交导出任务
 *
 * <AUTHOR>
 */
@Data
public class FileExportTaskSubmitCommand implements Serializable {

    @ApiModelProperty(value = "业务场景 需求单导出:demand-order-export 运单导出:delivery-order-export 提箱单:delivery-container-export")
    private ExportTaskBusinessCodeEnum businessCode;
    @ApiModelProperty(value = "任务参数")
    private ConfigGridDTO taskParam;

    @ApiModelProperty("子业务场景")
    private String subBusinessCode;

}

