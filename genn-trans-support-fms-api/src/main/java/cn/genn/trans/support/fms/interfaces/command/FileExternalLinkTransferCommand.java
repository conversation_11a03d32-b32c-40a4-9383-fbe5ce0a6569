package cn.genn.trans.support.fms.interfaces.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 文件转存
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileExternalLinkTransferCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    private String url;
    /**
     * 文件后缀 可指定用于文件名生成
     */
    private String ext;
    @NotBlank
    private String businessCode;
    @NotBlank
    private String objectId;
    @NotBlank
    private String objectType;
//    @NotBlank
//    private String originalFilename;

    /**
     * 上传来源相关信息
     */
    @NotNull
    private Long tenantId;
    @NotNull
    private Long systemId;
    @NotNull
    private Long userId;
    @NotBlank
    private String username;

}

