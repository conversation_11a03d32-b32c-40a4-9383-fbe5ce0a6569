package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.core.model.valid.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;


/**
 * FileImportConf操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileImportConfOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "子业务场景")
    private String subBusinessCode;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "文件导入模板")
    private String templateFileKey;

    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @ApiModelProperty(value = "模板配置")
    private String templateConf;


}

