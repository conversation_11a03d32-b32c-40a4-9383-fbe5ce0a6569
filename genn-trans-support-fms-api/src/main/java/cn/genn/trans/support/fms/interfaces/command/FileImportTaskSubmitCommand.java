package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.ImportTaskBusinessCodeEnum;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;


/**
 * 提交导入任务
 *
 * <AUTHOR>
 */
@Data
public class FileImportTaskSubmitCommand implements Serializable {

    @ApiModelProperty(value = "导入任务业务场景")
    private ImportTaskBusinessCodeEnum businessCode;

    @ApiModelProperty("子业务场景")
    private String subBusinessCode;

    @ApiModelProperty("携带的额外的回调参数")
    private String callbackParam = "{}";

    @ApiModelProperty("导入文件key")
    private String importFileKey;


    public String getCallbackParam() {
        if (Objects.isNull(callbackParam) || StrUtil.isBlank(callbackParam)) {
            return "{}";
        }
        return callbackParam;
    }
}

