package cn.genn.trans.support.fms.interfaces.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 上传图片base64支持
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileQueryOfMiniCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fileKey;

    @NotNull(message = "小程序获取文件租户id不存在")
    private Long tenantId;

}

