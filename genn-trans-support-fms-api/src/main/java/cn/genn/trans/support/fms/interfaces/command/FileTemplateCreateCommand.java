package cn.genn.trans.support.fms.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 文件模板下载
 *
 * <AUTHOR>
 */
@Data
public class FileTemplateCreateCommand implements Serializable {

    @ApiModelProperty(value = "业务场景")
    private String businessCode;
    @ApiModelProperty(value = "占位符参数")
    private List<SimpleParam> simpleParams = new ArrayList<>();
    @ApiModelProperty(value = "下拉框替换参数")
    private List<CellRangeAddress> cellRangeAddressParam = new ArrayList<>();

    @ApiModelProperty("指定生成的文件名称")
    private String fileName;

    private Long tenantId;
    private Long systemId;
    private String userName;
    private Long userId;

    @Data
    public static class SimpleParam implements Serializable {
        @ApiModelProperty(value = "占位符")
        private String placeholder;
        @ApiModelProperty(value = "参数值")
        private String value;
    }

    @Data
    public static class CellRangeAddress implements Serializable {
        @ApiModelProperty(value = "列名")
        private String rowName;
        @ApiModelProperty(value = "下拉框数据")
        private List<String> options;
        @ApiModelProperty(value = "下拉框默认值")
        private String defaultValue;
        @ApiModelProperty(value = "开始行默认1")
        private int firstRow = 1;
        @ApiModelProperty(value = "结束行")
        private int lastRow = 10000;
    }
}

