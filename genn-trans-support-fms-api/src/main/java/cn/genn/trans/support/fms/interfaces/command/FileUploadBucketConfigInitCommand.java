package cn.genn.trans.support.fms.interfaces.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * FileUploadBucketConfig操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileUploadBucketConfigInitCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @NotBlank
    private String platform;

    @ApiModelProperty(value = "")
    @NotNull
    private Long tenantId;


}

