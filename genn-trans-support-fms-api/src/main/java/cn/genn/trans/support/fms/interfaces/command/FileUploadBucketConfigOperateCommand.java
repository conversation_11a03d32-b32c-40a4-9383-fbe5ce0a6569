package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.core.model.valid.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * FileUploadBucketConfig操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileUploadBucketConfigOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private Long tenantId;

    @ApiModelProperty(value = "")
    private String accessKey;

    @ApiModelProperty(value = "")
    private String secretKey;

    @ApiModelProperty(value = "")
    private String endPoint;

    @ApiModelProperty(value = "")
    private String bucketName;

    @ApiModelProperty(value = "")
    private String domain;

    @ApiModelProperty(value = "")
    private String basePath;

    @ApiModelProperty(value = "")
    private Long createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}

