package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.core.model.valid.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * FileUploadBusinessConfig操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileUploadBusinessConfigOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private Long tenantId;

    @ApiModelProperty(value = "")
    private Long systemId;

    @ApiModelProperty(value = "")
    private String limitContentType;

    @ApiModelProperty(value = "")
    private Integer limitFileSize;

    @ApiModelProperty(value = "")
    private String businessCode;

    @ApiModelProperty(value = "")
    private String path;

    @ApiModelProperty(value = "")
    private String filingConfig;

    @ApiModelProperty(value = "")
    private Byte dataLevel;

    @ApiModelProperty(value = "")
    private String waterMarkConfig;

    @ApiModelProperty(value = "")
    private Long preSignedUrlValid;

    @ApiModelProperty(value = "")
    private Long createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}

