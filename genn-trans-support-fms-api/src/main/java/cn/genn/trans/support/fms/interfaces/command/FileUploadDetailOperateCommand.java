package cn.genn.trans.support.fms.interfaces.command;

import cn.genn.core.model.valid.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * FileUploadDetail操作对象
 *
 * <AUTHOR>
 */
@Data
public class FileUploadDetailOperateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private String fileKey;

    @ApiModelProperty(value = "")
    private Long systemId;

    @ApiModelProperty(value = "")
    private Long tenantId;

    @ApiModelProperty(value = "")
    private String businessCode;

    @ApiModelProperty(value = "")
    private String url;

    @ApiModelProperty(value = "")
    private Long size;

    @ApiModelProperty(value = "")
    private String filename;

    @ApiModelProperty(value = "")
    private String originalFilename;

    @ApiModelProperty(value = "")
    private String basePath;

    @ApiModelProperty(value = "")
    private String path;

    @ApiModelProperty(value = "")
    private String ext;

    @ApiModelProperty(value = "")
    private String contentType;

    @ApiModelProperty(value = "")
    private String platform;

    @ApiModelProperty(value = "")
    private String thUrl;

    @ApiModelProperty(value = "")
    private String thFilename;

    @ApiModelProperty(value = "")
    private Long thSize;

    @ApiModelProperty(value = "")
    private String thContentType;

    @ApiModelProperty(value = "")
    private String objectId;

    @ApiModelProperty(value = "")
    private String objectType;

    @ApiModelProperty(value = "")
    private String metadata;

    @ApiModelProperty(value = "")
    private String userMetadata;

    @ApiModelProperty(value = "")
    private String thMetadata;

    @ApiModelProperty(value = "")
    private String thUserMetadata;

    @ApiModelProperty(value = "")
    private String attr;

    @ApiModelProperty(value = "")
    private String fileAcl;

    @ApiModelProperty(value = "")
    private String thFileAcl;

    @ApiModelProperty(value = "")
    private String hashInfo;

    @ApiModelProperty(value = "")
    private String uploadId;

    @ApiModelProperty(value = "")
    private Integer uploadStatus;

    @ApiModelProperty(value = "")
    private Long createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "")
    private Byte deleted;


}

