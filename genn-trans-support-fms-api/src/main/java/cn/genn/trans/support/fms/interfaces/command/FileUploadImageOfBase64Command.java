package cn.genn.trans.support.fms.interfaces.command;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 上传图片base64支持
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileUploadImageOfBase64Command implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    private String base64String;
    @NotBlank
    private String businessCode;
    @NotBlank
    private String objectId;
    @NotBlank
    private String objectType;
    @NotBlank
    private String originalFilename;

    /**
     * 上传来源相关信息
     */
    @NotNull
    private Long tenantId;
    @NotNull
    private Long systemId;
    @NotNull
    private Long userId;
    @NotBlank
    private String username;

}

