package cn.genn.trans.support.fms.interfaces.dto;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.interfaces.dto.config.FileExportConfConfig;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * FileExportConfDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileExportConfDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "子业务场景")
    private String subBusinessCode;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @ApiModelProperty(value = "")
    private String config;

    private FileExportConfConfig exportConfConfig;

    public FileExportConfConfig getExportConfConfig() {
        if (StrUtil.isNotBlank(config)) {
            return JsonUtils.parse(config, FileExportConfConfig.class);
        }
        return null;
    }


}

