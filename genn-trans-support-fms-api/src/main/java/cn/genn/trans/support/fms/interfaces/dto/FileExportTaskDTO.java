package cn.genn.trans.support.fms.interfaces.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.interfaces.dto.config.FileExportConfConfig;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * FileExportTaskDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileExportTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "导出文件名称")
    private String fileName;

    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @ApiModelProperty(value = "模板配置")
    private String templateConf;

    @ApiModelProperty(value = "任务状态 -1：FAIL-执行失败，0：WAIT-待执行，1：PROCESS-执行中，2：UPLOAD-上传中，10：SUCCESS-执行成功")
    private TaskStatusEnum taskStatus;

    @ApiModelProperty(value = "任务参数")
    private String taskParam;

    @ApiModelProperty(value = "token数据")
    private String tokenData;

    @ApiModelProperty(value = "执行次数")
    private Integer executeCount;

    @ApiModelProperty(value = "导出文件唯一标识")
    private String resultFileKey;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "逻辑删除")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private Long updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;

    private FileExportConfConfig exportConfConfig;

    public FileExportConfConfig getExportConfConfig() {
        if (StrUtil.isNotBlank(templateConf)) {
            return JsonUtils.parse(templateConf, FileExportConfConfig.class);
        }
        return null;
    }
}

