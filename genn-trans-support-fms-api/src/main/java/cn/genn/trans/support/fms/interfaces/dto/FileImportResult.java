package cn.genn.trans.support.fms.interfaces.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Date: 2024/12/24
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileImportResult {
    /**
     * 行号从1开始
     */
    private int rowIndex;
    /**
     * 执行结果
     */
    private boolean result;

    /**
     * 失败原因
     */
    private String message;
}
