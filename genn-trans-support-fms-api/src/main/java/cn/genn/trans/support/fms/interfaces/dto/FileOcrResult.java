package cn.genn.trans.support.fms.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * OCR识别营业执照的结果
 *
 * @Date: 2024/5/29
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileOcrResult {
    private List<FileOcrResultEntityDTO> entities;
}
