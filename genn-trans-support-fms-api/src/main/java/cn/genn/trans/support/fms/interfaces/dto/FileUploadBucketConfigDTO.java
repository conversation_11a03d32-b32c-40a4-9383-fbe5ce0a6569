package cn.genn.trans.support.fms.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * FileUploadBucketConfigDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadBucketConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private String platform;

    @ApiModelProperty(value = "")
    private Long tenantId;

    @ApiModelProperty(value = "")
    private String accessKey;

    @ApiModelProperty(value = "")
    private String secretKey;

    @ApiModelProperty(value = "")
    private String endPoint;

    @ApiModelProperty(value = "")
    private String bucketName;

    @ApiModelProperty(value = "")
    private String domain;

    @ApiModelProperty(value = "")
    private String basePath;

    @ApiModelProperty(value = "")
    private Long createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}

