package cn.genn.trans.support.fms.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 水印处理配置
 * @Date: 2024/5/13
 * @Author: kang<PERSON>an
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileUploadWaterMarkConfigDTO {
    /**
     * {
     *     "textPrefix": "文字前缀 营业执照",
     *     // 支持表达式 校验参数  YYYY-MM-DD hh:mm:ss 用户名 电话尾号
     *     "textExpression" : "getCurrentTime() username telephone",
     *     "size": 60,
     *     "color": "D5D5D5",
     *     "type": "", // 字体
     *     "fill": 1,
     *     "extra": "g_center,rotate_30"
     * }
     */
    /**
     * 固定文案
     */
    private String fixedText;
    private String textPrefix;
    private String textExpression;
    private Integer size;
    private String color;
    private String type;
    private Integer fill;
    private String extra;
}
