package cn.genn.trans.support.fms.interfaces.dto.config;

import cn.genn.core.model.enums.BooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ConfigGridColDTO {

    /**
     * 列id
     */
    @ApiModelProperty(value = "列id")
    private Long id;

    /**
     * 列名
     */
    @ApiModelProperty(value = "列名")
    private String colName;

    /**
     * 前端属性名称
     */
    @ApiModelProperty(value = "前端属性名称")
    private String frontendPropertyName;

    /**
     * 后端属性名称
     */
    @ApiModelProperty(value = "后端属性名称")
    private String backendPropertyName;

    /**
     * 父列id
     */
    @ApiModelProperty(value = "父列id")
    private Long colPid;

    /**
     * 排序，越小越靠前
     */
    @ApiModelProperty(value = "排序，越小越靠前")
    private int sortOrder;

    /**
     * 是否展示
     */
    @ApiModelProperty(value = "是否展示")
    private BooleanEnum display;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private BooleanEnum required;
}
