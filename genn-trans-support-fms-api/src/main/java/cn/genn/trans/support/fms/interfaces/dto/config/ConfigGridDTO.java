package cn.genn.trans.support.fms.interfaces.dto.config;

import cn.genn.trans.support.fms.interfaces.query.business.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ConfigGridDTO {

    /**
     * 表格code
     */
    @ApiModelProperty(value = "表格code")
    private String gridCode;

    /**
     * 表格类型
     */
    @ApiModelProperty(value = "表格类型")
    private GridType gridType;

    /**
     * 表格名称
     */
    @ApiModelProperty(value = "表格名称")
    private String gridName;

    /**
     * 表格描述
     */
    @ApiModelProperty(value = "表格描述")
    private String gridDesc;

    /**
     * 表格列
     */
    @ApiModelProperty(value = "表格列")
    private List<ConfigGridColDTO> colList;

    /**
     * 查询运单数据时传递的查询条件
     */
    @ApiModelProperty(value = "查询运单数据时传递的查询条件")
    private DeliveryOrderPageQuery deliveryOrderPageQuery;

    /**
     * 查询需求单数据时传递的查询条件
     */
    @ApiModelProperty(value = "查询需求单数据时传递的查询条件")
    private DemandOrderQuery demandOrderQuery;

    /**
     * 查询提箱单数据时传递的查询条件
     */
    @ApiModelProperty(value = "查询提箱单数据时传递的查询条件")
    private OrdDeliverContainerPageQuery deliverContainerQuery;

    /**
     * 随车费用结算查询条件
     */
    private VehicleExpensesSettleQuery vehicleExpensesSettleQuery;

    /**
     * 随车费用分页查询条件
     */
    @ApiModelProperty(value = "随车费用分页查询条件")
    private SifVehicleExpensesQuery sifVehicleExpensesQuery;

    /**
     * 查询拉运数据时传递的查询条件
     */
    private TransportOrderQuery transportOrderQuery;

    /**
     * 对账单查询条件
     */
    @ApiModelProperty(value = "对账单查询条件")
    private SifBillPageQuery sifBillPageQuery;
    /**
     * 结算单查询条件
     */
    @ApiModelProperty(value = "对账单查询条件")
    private SifBillSettlePageQuery sifBillSettlePageQuery;

    @ApiModelProperty(value = "补能记录查询条件")
    private CarrRefuelingRecordQuery carrRefuelingRecordQuery;

    /**
     * 磅单审核(tms)的查询条件
     */
    private PoundReviewTmsPageQuery poundReviewTmsPageQuery;

    /**
     * 磅单审核(ops)的查询条件
     */
    private PoundReviewOpsPageQuery poundReviewOpsPageQuery;

    /**
     * 回调参数 json字符串 数据平台使用
     */
    private String callbackParam;

    /**
     * 保理放款查询对象
     */
    private FspLoanPageQuery fspLoanPageQuery;

    /**
     * 司机绩效查询对象 待确认 待汇总
     */
    private DriverWaitConfirmQuery driverWaitConfirmQuery;

    /**
     * 司机绩效查询对象 待结算 已结算
     */
    private SifDriverSettlePageQuery driverSettleQuery;

    /**
     * 暂估单查询
     */
    private TempEstimateBillQuery tempEstimateBillQuery;

    /**
     * 暂估红冲单查询
     */
    private TempEstimateRedBillQuery tempEstimateRedBillQuery;
}
