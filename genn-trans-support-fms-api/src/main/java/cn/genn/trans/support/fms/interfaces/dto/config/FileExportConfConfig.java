package cn.genn.trans.support.fms.interfaces.dto.config;

import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * FileExportConfDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileExportConfConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导出最大条数")
    private Integer sum;

    @ApiModelProperty(value = "导出分页条数")
    private Integer pageSize;

    @ApiModelProperty(value = "下载模板")
    private String templateFileKey;

    @ApiModelProperty(value = "对账单下载模板")
    private Map<BillTypeEnum, String> templateFileKeyOfBill;

    @ApiModelProperty(value = "结算单下载模板")
    private Map<BillTypeEnum, String> templateFileKeyOfSettle;

    @ApiModelProperty("请求路径")
    private String queryUrl;

    @ApiModelProperty("文件名生成策略")
    private String fileNameGenerationStrategy;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件名生成所需参数选择")
    private String paramKey;

    @ApiModelProperty("文件名生成所需参数枚举描述")
    private Map<String, String> enumDesc;

    @ApiModelProperty("接口url查询生成策略")
    private String queryUrlGenerationStrategy;

    @ApiModelProperty("restTemplate lbRestTemplate")
    private String restTemplateStrategy = "restTemplate";

    @ApiModelProperty("lb方式请求服务地址")
    private String lbServiceName;

    @ApiModelProperty("数据映射策略")
    private String dataMappingStrategyClass;

    @ApiModelProperty("数据映射策略额外配置")
    private String dataMappingStrategyConfig;

    /**
     * 表格列
     */
    @ApiModelProperty(value = "固定表格列")
    private List<ConfigGridColDTO> colList;

    @ApiModelProperty(value = "指定输出格式为数字的列名")
    private List<String> numberStyleColNameList;

}

