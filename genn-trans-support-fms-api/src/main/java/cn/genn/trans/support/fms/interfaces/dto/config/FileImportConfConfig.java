package cn.genn.trans.support.fms.interfaces.dto.config;

import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * FileExportConfDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileImportConfConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导入最大条数")
    private Integer sum;

    @ApiModelProperty(value = "导入分页条数")
    private Integer pageSize;

    @ApiModelProperty("请求路径")
    private String queryUrl;

    @ApiModelProperty("接口url查询生成策略")
    private String queryUrlGenerationStrategy;

    @ApiModelProperty("restTemplate lbRestTemplate")
    private String restTemplateStrategy = "restTemplate";

    @ApiModelProperty("lb方式请求服务地址")
    private String lbServiceName;

    @ApiModelProperty("列名对应的字段名")
    private Map<String, String> columnFieldMap;

    /**
     * 从columnFieldMap中获取所有的导入列名
     */
    public List<String> getImportColumnNames() {
        if (columnFieldMap == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(columnFieldMap.keySet());
    }

}

