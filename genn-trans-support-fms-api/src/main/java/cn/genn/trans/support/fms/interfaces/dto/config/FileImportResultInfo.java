package cn.genn.trans.support.fms.interfaces.dto.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * FileExportConfDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileImportResultInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导入总条数")
    private Integer count;

    @ApiModelProperty(value = "导入成功条数")
    private Integer success;

    @ApiModelProperty(value = "导入失败条数")
    private Integer fail;


    public void addSuccess() {
        this.success++;
    }

    public void addFail() {
        this.fail++;
    }
}

