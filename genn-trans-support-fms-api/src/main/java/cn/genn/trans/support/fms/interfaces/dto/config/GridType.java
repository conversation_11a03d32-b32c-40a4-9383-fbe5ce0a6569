package cn.genn.trans.support.fms.interfaces.dto.config;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum GridType {

    SEARCH(1, "搜索"),
    TABLE(2, "表格"),
    EXPORT(3, "导出"),;

    @JsonValue
    @EnumValue
    private final int code;
    private final String desc;

    GridType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
