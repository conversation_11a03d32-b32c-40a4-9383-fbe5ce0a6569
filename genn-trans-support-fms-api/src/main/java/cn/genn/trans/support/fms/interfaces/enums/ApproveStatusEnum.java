package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum ApproveStatusEnum {

    APPROVE(0, "待审核"),
    PASS(1, "已通过"),
    REJECT(2, "已驳回"),
    ;

    @EnumValue
    @JsonValue
    @Getter
    private final int code;

    @Getter
    private final String description;

    ApproveStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, ApproveStatusEnum> VALUES = new HashMap<>();
    static {
        for (final ApproveStatusEnum item : ApproveStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ApproveStatusEnum of(int code) {
        return VALUES.get(code);
    }

}

