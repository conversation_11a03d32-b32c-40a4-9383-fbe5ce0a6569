package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum AuditStatusEnum {

    CACHE(-2, "已暂存"),
    NO_REVIEW(-1, "无需审核"),
    WAIT_REVIEW(0, "待审核"),
    APPROVED(1, "已通过"),
    REJECTED(2, "已驳回"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    AuditStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, AuditStatusEnum> VALUES = new HashMap<>();
    static {
        for (final AuditStatusEnum item : AuditStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AuditStatusEnum of(int code) {
        return VALUES.get(code);
    }

}
