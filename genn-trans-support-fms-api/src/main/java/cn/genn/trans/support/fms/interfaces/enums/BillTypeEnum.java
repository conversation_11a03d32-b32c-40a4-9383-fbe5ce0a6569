package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 运营商
 *
 * <AUTHOR>
 */
@Getter
public enum BillTypeEnum {

    CUSTOMER(1, "客户（应付）- 运营商（应收）"),
    CARRIER(2, "运营商（应付）- 承运商（应收）"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    BillTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, BillTypeEnum> VALUES = new HashMap<>();

    static {
        for (final BillTypeEnum item : BillTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static BillTypeEnum of(int code) {
        return VALUES.get(code);
    }

    public static boolean isCustomer(BillTypeEnum type) {
        return CUSTOMER.equals(type);
    }

    public static boolean isCarrier(BillTypeEnum type) {
        return CARRIER.equals(type);
    }
}

