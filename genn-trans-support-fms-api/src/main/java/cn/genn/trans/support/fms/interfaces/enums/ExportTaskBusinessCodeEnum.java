package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: liuyang
 * @date: 2024/6/27
 */
@Getter
public enum ExportTaskBusinessCodeEnum {

    DEMAND_ORDER_EXPORT("demand-order-export", "需求订单数据导出"),
    DELIVERY_ORDER_EXPORT("delivery-order-export", "运单数据导出"),
    DELIVERY_CONTAINER_EXPORT("delivery-container-export", "提箱单数据导出"),
    VEHICLE_EXPENSES_RECORD_EXPORT("vehicle-expenses-record-export", "随车费用记录数据导出"),
    VEHICLE_EXPENSES_SETTLE_EXPORT("vehicle-expenses-export", "随车费用结算数据导出"),
    TRANSPORT_ORDER_EXPORT("transport-order-export","拉运数据导出"),
    POUND_REVIEW_OPS_EXPORT("pound-review-ops-export","磅单审核导出"),
    POUND_REVIEW_TMS_EXPORT("pound-review-tms-export","磅单审核导出"),

    SIF_BILL_EXPORT("sif-bill-export", "对账单数据导出"),
    SIF_SETTLE_BILL_EXPORT("sif-settle-bill-export", "结算单数据导出"),
    VEHICLE_REFUEL_RECORD_EXPORT("vehicle-refuel-record-export", "补能记录数据导出"),
    FSP_LOAN_EXPORT("fsp-loan-export", "保理结算导出"),
    DATA_HUB_EXPORT("data-hub-export", "数据开放平台导出任务"),

    DRIVER_PERFORMANCE_WAIT_EXPORT("driver-performance-wait-export", "司机绩效待确认/待汇总数据导出任务"),
    DRIVER_PERFORMANCE_SETTLE_EXPORT("driver-performance-settle-export", "司机绩效待结算/已结算数据导出任务"),

    /**
     * 全局通用业务导出
     */
    COMMON_EXPORT("common-export", "通用业务导出任务"),

    /**
     * 补能业务导出
     */
    RETAIL_COMMON_EXPORT("retail-common-export", "补能业务导出任务"),

    TEMP_ESTIMATE_BILL_EXPORT("temp-estimate-bill-export", "暂估单导出任务"),
    TEMP_ESTIMATE_RED_BILL_EXPORT("temp-estimate-red-bill-export", "暂估红冲单导出任务"),

    ;

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    ExportTaskBusinessCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<String, ExportTaskBusinessCodeEnum> VALUES = new HashMap<>();
    static {
        for (final ExportTaskBusinessCodeEnum item : ExportTaskBusinessCodeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ExportTaskBusinessCodeEnum of(String code) {
        return VALUES.get(code);
    }

}
