package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: liuyang
 * @date: 2024/6/27
 */
@Getter
public enum FileBusinessCodeEnum {

    BUSINESS_LICENSE("BUSINESS_LICENSE", "营业执照", "ocrOfBusinessLicense"),
    ID_CARD_FRONT("ID_CARD_FRONT", "身份证正面", "ocrOfIdCard"),
    ID_CARD_BACK("ID_CARD_BACK", "身份证反面", "ocrOfIdCard"),
    DRIVING_LICENSE_ORIGINAL("DRIVING_LICENSE_ORIGINAL", "驾驶证原本", "ocrOfDrivingLicense"),
    DRIVING_LICENSE_BACK("DRIVING_LICENSE_BACK", "驾驶者副本", "ocrOfDrivingLicense"),
    VEHICLE_LICENSE("VEHICLE_LICENSE", "行驶证", "ocrOfVehicleLicense"),
    ;

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    private String ocrMethodName;

    FileBusinessCodeEnum(String code, String description, String ocrMethodName) {
        this.code = code;
        this.description = description;
        this.ocrMethodName = ocrMethodName;
    }

    private static final Map<String, FileBusinessCodeEnum> VALUES = new HashMap<>();
    static {
        for (final FileBusinessCodeEnum item : FileBusinessCodeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static FileBusinessCodeEnum of(String code) {
        return VALUES.get(code);
    }

}
