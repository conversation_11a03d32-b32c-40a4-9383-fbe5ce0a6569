package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum HasExpensesEnum {

    WITHOUT_FEE(0, "没有"),
    WITH_FEE(1, "有"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    HasExpensesEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, HasExpensesEnum> VALUES = new HashMap<>();

    static {
        for (final HasExpensesEnum item : HasExpensesEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static HasExpensesEnum of(int code) {
        return VALUES.get(code);
    }

}
