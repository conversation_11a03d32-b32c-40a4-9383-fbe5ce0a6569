package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 导入任务业务场景枚举
 *
 * @description:
 * @author: kang<PERSON><PERSON>
 * @date: 2024/12/4
 */
@Getter
public enum ImportTaskBusinessCodeEnum {


    /**
     * 全局通用业务导入
     */
    COMMON_IMPORT("common-import", "通用业务导入任务"),

    ;

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    ImportTaskBusinessCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<String, ImportTaskBusinessCodeEnum> VALUES = new HashMap<>();

    static {
        for (final ImportTaskBusinessCodeEnum item : ImportTaskBusinessCodeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ImportTaskBusinessCodeEnum of(String code) {
        return VALUES.get(code);
    }

}
