package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum SettleStatusEnum {

    WAIT_SUMMARIZE(0, "待汇总"),
    WAIT_SETTLE(1, "待结算"),
    SETTLED(2, "已结算"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    SettleStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, SettleStatusEnum> VALUES = new HashMap<>();
    static {
        for (final SettleStatusEnum item : SettleStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static SettleStatusEnum of(int code) {
        return VALUES.get(code);
    }


    public static boolean isWaitSettle(SettleStatusEnum settleStatus) {
        return WAIT_SETTLE.equals(settleStatus);
    }
    public static boolean isSettle(SettleStatusEnum settleStatus) {
        return SETTLED.equals(settleStatus);
    }
}

