package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统id
 *
 * @Date: 2024/8/20
 * @Author: kangjian
 */
public enum SystemEnum {
    OPS(2L, "运营商系统"),
    TMS(3L, "承运商系统"),
    ;

    @EnumValue
    @JsonValue
    @Getter
    private final Long code;

    @Getter
    private final String description;

    SystemEnum(Long code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Long, SystemEnum> VALUES = new HashMap<>();

    static {
        for (final SystemEnum item : SystemEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static SystemEnum of(Long code) {
        return VALUES.get(code);
    }
}
