package cn.genn.trans.support.fms.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum TaskStatusEnum {

    CANCEL(-2, "取消"),
    FAIL(-1, "执行失败"),
    WAIT(0, "待执行"),
    PROCESS(1, "执行中"),
    UPLOAD(2, "上传中"),
    SUCCESS(10, "执行成功"),
    ;

    @EnumValue
    @JsonValue
    @Getter
    private final int code;

    @Getter
    private final String description;

    TaskStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, TaskStatusEnum> VALUES = new HashMap<>();
    static {
        for (final TaskStatusEnum item : TaskStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskStatusEnum of(int code) {
        return VALUES.get(code);
    }

}

