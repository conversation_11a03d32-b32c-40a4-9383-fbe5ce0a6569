package cn.genn.trans.support.fms.interfaces.enums.pound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeliveryOrderStatusEnum {


    GENERATED_ORDER(100, "已生成运单"),
    LOADING_SIGN_IN(110, "已装车签到"),
    LOADING_IN_FACTORY(120, "已装车入厂"),
    UPLOADED_LOADING_POUND(130, "已上传装车磅单"),
    LOADED(200, "已装车"),
    DISCHARGE_SIGN_IN(210, "已卸车签到"),
    DISCHARGE_IN_FACTORY(220, "已卸车入厂"),
    DISCHARGE(300, "已卸车"),
    RECEIVED(400, "已签收"),
    CANCELLED(999, "已取消"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    private static final Map<Integer, DeliveryOrderStatusEnum> VALUES = new HashMap<>();
    static {
        for (final DeliveryOrderStatusEnum item : DeliveryOrderStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static DeliveryOrderStatusEnum of(int code) {
        return VALUES.get(code);
    }

    /**
     * 获取进行中的配送订单状态列表。
     * 这个方法定义了配送订单从生成到完成的各个状态，用于区分订单当前所处的阶段。
     * 包含的状态有：已生成运单、已装车签到、已装车入厂、已上传装车磅单、已装车、已卸车签到、已卸车入厂、已卸车。
     *
     * @return 配送订单进行中状态的列表，包含了所有从加载到卸载的过程中的状态。
     */
    public static List<DeliveryOrderStatusEnum> getOngoingStatusList() {
        // 返回一个包含所有进行中状态的列表
        return Arrays.asList(GENERATED_ORDER,
                                LOADING_SIGN_IN,
                                LOADING_IN_FACTORY,
                                UPLOADED_LOADING_POUND,
                                LOADED,
                                DISCHARGE_SIGN_IN,
                                DISCHARGE_IN_FACTORY,
                                DISCHARGE);
    }

}
