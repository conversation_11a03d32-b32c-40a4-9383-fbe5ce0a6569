package cn.genn.trans.support.fms.interfaces.enums.pound;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 磅单图来源枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PoundStatusEnum {


    WAIT(0, "待审核"),
    SUCCESS(1, "已通过"),
    REJECT(2, "已驳回"),
    /**
     * 无需运营商审核 需要大宗审核
     */
    NOT_APPROVE(3, "待审核"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    private static final Map<Integer, PoundStatusEnum> VALUES = new HashMap<>();
    static {
        for (final PoundStatusEnum item : PoundStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static PoundStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
