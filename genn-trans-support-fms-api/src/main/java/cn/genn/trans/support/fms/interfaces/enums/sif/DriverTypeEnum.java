package cn.genn.trans.support.fms.interfaces.enums.sif;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 司机类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriverTypeEnum {

    SELF(0, "自有"),
    OUTSOURCE(1, "外协");

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    private static final Map<Integer, DriverTypeEnum> VALUES = new HashMap<>();

    static {
        for (final DriverTypeEnum item : DriverTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static DriverTypeEnum of(int code) {
        return VALUES.get(code);
    }


    public static boolean isSelf(DriverTypeEnum driverType) {
        return SELF.equals(driverType);
    }

    public static boolean isOut(DriverTypeEnum driverType) {
        return OUTSOURCE.equals(driverType);
    }
}
