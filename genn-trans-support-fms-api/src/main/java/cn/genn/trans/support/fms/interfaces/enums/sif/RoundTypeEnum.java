package cn.genn.trans.support.fms.interfaces.enums.sif;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum RoundTypeEnum {

    NO(9, "不抹零"),
    ONE(1, "1元内抹零"),
    TEN(10, "10元内抹零"),
    HUNDRED(100, "100元内抹零"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    RoundTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static final Map<Integer, RoundTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RoundTypeEnum item : RoundTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RoundTypeEnum of(int code) {
        return VALUES.get(code);
    }

    public static RoundTypeEnum getRoundType(int roundType) {
        for (RoundTypeEnum value : values()) {
            if(value.getCode() == roundType){
                return value;
            }
        }
        return RoundTypeEnum.NO;
    }
}

