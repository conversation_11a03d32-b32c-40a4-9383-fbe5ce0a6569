package cn.genn.trans.support.fms.interfaces.enums.sif;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum StatStatusEnum {

    WAIT_CONFIRM(0, "待确认"),
    WAIT_CONFIRMING(1, "待汇总"),
    CONFIRM_DONE(10, "已确认"),
    CONFIRMING_DONE(20, "已汇总"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    StatStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, StatStatusEnum> VALUES = new HashMap<>();

    static {
        for (final StatStatusEnum item : StatStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static StatStatusEnum of(int code) {
        return VALUES.get(code);
    }

    public static boolean isWaitConfirm(StatStatusEnum statStatus) {
        return WAIT_CONFIRM.equals(statStatus);
    }
}

