package cn.genn.trans.support.fms.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import cn.genn.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * FileExportConf查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileExportConfQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "子业务场景")
    private String subBusinessCode;

    @ApiModelProperty(value = "")
    private String config;


}

