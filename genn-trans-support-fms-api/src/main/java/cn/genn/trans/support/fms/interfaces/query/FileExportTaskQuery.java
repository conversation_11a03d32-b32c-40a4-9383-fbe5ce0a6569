package cn.genn.trans.support.fms.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import cn.genn.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * FileExportTask查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileExportTaskQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "任务状态 -1：FAIL-执行失败，0：WAIT-待执行，1：PROCESS-执行中，2：UPLOAD-上传中，10：SUCCESS-执行成功")
    private TaskStatusEnum taskStatus;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private Long updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;


}

