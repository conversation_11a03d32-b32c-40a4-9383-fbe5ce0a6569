package cn.genn.trans.support.fms.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import cn.genn.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * FileUploadBusinessConfig查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileUploadBusinessConfigQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private Long tenantId;

    @ApiModelProperty(value = "")
    private Long systemId;

    @ApiModelProperty(value = "")
    private String limitContentType;

    @ApiModelProperty(value = "")
    private Integer limitFileSize;

    @ApiModelProperty(value = "")
    private String businessCode;

    @ApiModelProperty(value = "")
    private String path;

    @ApiModelProperty(value = "")
    private String filingConfig;

    @ApiModelProperty(value = "")
    private Byte dataLevel;

    @ApiModelProperty(value = "")
    private String waterMarkConfig;

    @ApiModelProperty(value = "")
    private Long preSignedUrlValid;

    @ApiModelProperty(value = "")
    private Long createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}

