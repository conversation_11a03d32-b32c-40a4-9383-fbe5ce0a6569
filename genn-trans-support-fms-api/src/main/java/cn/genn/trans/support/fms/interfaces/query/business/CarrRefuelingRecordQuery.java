package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.support.fms.interfaces.enums.ApproveStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * CarrVehicleRefuelingRecord查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarrRefuelingRecordQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "运营商id")
    private Long operatorId;

    @ApiModelProperty(value = "承运商id")
    private Long carrierId;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "补能站点")
    private String refuelStationName;

    @ApiModelProperty(value = "审核状态")
    private ApproveStatusEnum refuelStatus;

    @ApiModelProperty(value = "补能类型")
    private String refuelType;

    @ApiModelProperty(value = "时间范围起点")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refuelTimeBegin;

    @ApiModelProperty(value = "时间范围起点")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refuelTimeEnd;

    @ApiModelProperty(value = "车辆燃料类型")
    private String vehicleFuelType;
}

