package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运单查询条件
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeliveryOrderPageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "运单号")
    private String orderNo;

    @ApiModelProperty(value = "业务类型, 1:PURCHASE-采购运输, 2:SALES-销售运输, 3:ALLOT-厂间调拨, 4:EXTERNAL-外部运输")
    private String businessType;

    @ApiModelProperty(value = "业务类型集合")
    private List<String> businessTypeList;

    @ApiModelProperty(value = "运单状态")
    private Integer deliveryStatus;

    @ApiModelProperty(value = "运单状态集合")
    private List<Integer> deliveryStatusList;

    @ApiModelProperty(value = "运输任务号")
    private String taskNo;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "调度类型, 1:GRAB-抢单, 2:DISPATCH-派单")
    private Integer dispatchType;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "需求订单编号")
    private String demandOrderNo;

    @ApiModelProperty("承运商Id")
    private Long carrierId;

    @ApiModelProperty(value = "承运商name")
    private String carrierName;

    @ApiModelProperty(value = "承运订单号")
    private String carrierOrderNo;

    @ApiModelProperty(value = "通知单号")
    private String noticeOrderNo;
    @ApiModelProperty("发货方地址")
    private String noticeDeliveryAddress;
    @ApiModelProperty("收货方地址")
    private String noticeTakeAddress;
    @ApiModelProperty("发货公司名称")
    private String noticeDeliveryCompany;
    @ApiModelProperty("收货公司名称")
    private String noticeTakeCompany;
    @ApiModelProperty(value = "采购组织")
    private String noticeRecipientGroup;

    @ApiModelProperty(value = "装车地址简称")
    private String loadsAddrShort;

    @ApiModelProperty(value = "卸车地址简称")
    private String unloadAddrShort;

    @ApiModelProperty(value = "运输合同编号")
    private String demandContractNo;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "发货人手机号")
    private String demandDeliverMobile;

    @ApiModelProperty(value = "收货人手机号")
    private String demandTakeMobile;

    @ApiModelProperty(value = "承运合同编号")
    private String carrierContractNo;

    @ApiModelProperty(value = "创建时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeLeft;

    @ApiModelProperty(value = "创建时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeRight;

    @ApiModelProperty(value = "装货时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadsTimeLeft;

    @ApiModelProperty(value = "装货时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadsTimeRight;

    @ApiModelProperty(value = "卸货时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadTimeLeft;

    @ApiModelProperty(value = "卸货时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadTimeRight;

    @ApiModelProperty(value = "签收时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTimeLeft;

    @ApiModelProperty(value = "签收时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTimeRight;

    @ApiModelProperty(value = "承运商类型 1 网货 2 实体")
    private Integer category;

    @ApiModelProperty("客户类别")
    private Integer identity;

    @ApiModelProperty(value = "承运主体ID")
    private Long carrierSubjectId;

}
