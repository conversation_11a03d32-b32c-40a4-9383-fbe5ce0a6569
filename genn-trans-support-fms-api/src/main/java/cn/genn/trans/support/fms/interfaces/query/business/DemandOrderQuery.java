package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 需求订单查询对象
 * @date 2024-05-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DemandOrderQuery extends PageSortQuery {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("合同编号")
    private String contractNo;

    // todo: 发货地址、收货地址，页面如何呈现？
    // todo: 货物类目、货物名称

    @ApiModelProperty("发货人手机号")
    private String deliverMobile;

    @ApiModelProperty("收货人手机号")
    private String takeMobile;

    @ApiModelProperty(value = "开始时间开始")
    private LocalDateTime startTimeStart;

    @ApiModelProperty(value = "开始时间结束")
    private LocalDateTime startTimeEnd;

    @ApiModelProperty(value = "结束时间开始")
    private LocalDateTime endTimeStart;

    @ApiModelProperty(value = "结束时间结束")
    private LocalDateTime endTimeEnd;
}
