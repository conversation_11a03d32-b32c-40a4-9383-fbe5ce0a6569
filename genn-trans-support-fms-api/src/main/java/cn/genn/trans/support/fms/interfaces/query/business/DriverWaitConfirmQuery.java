package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.support.fms.interfaces.enums.sif.DriverTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.sif.StatStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SifChargeBill查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DriverWaitConfirmQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "统计状态 0-待确认，1-待汇总，10-已确认，20-已汇总")
    private StatStatusEnum statStatus;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;
    @ApiModelProperty(value = "司机类型")
    private DriverTypeEnum driverType;

    @ApiModelProperty(value = "承运线路id") // 应该是没用到
    private Long lineId;

    @ApiModelProperty(value = "货物编号")
    private String cargoNo;
    @ApiModelProperty(value = "货物名称")
    private String cargoName;
    @ApiModelProperty(value = "装车地址简称")
    private String loadingSubName;
    @ApiModelProperty(value = "卸车地址简称")
    private String unloadingSubName;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTimeLeft;
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTimeRight;

    @ApiModelProperty(value = "装车时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadingTimeLeft;
    @ApiModelProperty(value = "装车时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadingTimeRight;

    @ApiModelProperty(value = "卸车时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadingTimeLeft;
    @ApiModelProperty(value = "卸车时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadingTimeRight;

    @ApiModelProperty(value = "承运商id", hidden = true)
    private Long carrierId;

    @ApiModelProperty(value = "结算月份")
    private String settleMonth;

}

