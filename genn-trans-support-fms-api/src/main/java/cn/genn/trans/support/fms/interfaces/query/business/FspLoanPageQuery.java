package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.enums.BooleanEnum;
import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FspLoan查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FspLoanPageQuery extends PageSortQuery {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "承运商名称")
    private String carrierName;

    @ApiModelProperty(value = "运营商名称")
    private String operatorName;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "放款支付开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loanDateIndex;

    @ApiModelProperty(value = "放款支付结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loanDateEnd;

    @ApiModelProperty(value = "放款状态(0:待放款;100:待还款;200:部分还款;900-已结清)")
    private Integer loanStatus;

    @ApiModelProperty(value = "业务标识：settle-结算管理页面")
    private String businessFlag;

    private List<Integer> loanStatusList;

    @ApiModelProperty(value = "逾期状态,0:未逾期,1:已逾期")
    private BooleanEnum overdueStatus;

}
