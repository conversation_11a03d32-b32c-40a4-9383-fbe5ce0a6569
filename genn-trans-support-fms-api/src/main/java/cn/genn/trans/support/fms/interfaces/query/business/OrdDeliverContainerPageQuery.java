package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OrdDeliverContainer查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdDeliverContainerPageQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导出用的租户id")
    private Long tenantId;

    @ApiModelProperty(value = "提箱单编号")
    private String orderNo;
    @ApiModelProperty(value = "车辆id")
    private Long vehicleId;
    @ApiModelProperty(value = "司机id")
    private Long driverId;
    @ApiModelProperty(value = "提箱单状态")
    private Integer containerStatus;

    @ApiModelProperty(value = "提箱单状态列表")
    private List<Integer> containerStatusList;

    @ApiModelProperty(value = "提箱单状态列表反选")
    private List<Integer> noInContainerStatusList;

    @ApiModelProperty(value = "运输任务编号")
    private String taskNo;

    @ApiModelProperty(value = "承运商id")
    private Long carrierId;

    @ApiModelProperty(value = "运输任务状态")
    private Integer taskStatus;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "车牌号")
    private String vehiclePlateNo;

    @ApiModelProperty(value = "集装箱编号")
    private String containerNo;

    @ApiModelProperty(value = "站台简称")
    private String locationSubName;

    @ApiModelProperty(value = "过磅时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime poundBeginTime;

    @ApiModelProperty(value = "过磅时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime poundEndTime;

}

