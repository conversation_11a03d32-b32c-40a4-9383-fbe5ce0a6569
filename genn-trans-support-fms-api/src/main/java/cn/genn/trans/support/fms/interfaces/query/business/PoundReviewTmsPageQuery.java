package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.pound.DeliveryOrderStatusEnum;
import cn.genn.trans.support.fms.interfaces.enums.pound.PoundStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class PoundReviewTmsPageQuery  extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "审核状态(0:未审核 1:审核通过 2:驳回)")
    @NotNull(message = "审核状态不能为空")
    private PoundStatusEnum picStatus;

    @ApiModelProperty(value = "运单号")
    private String orderNo;

    @ApiModelProperty(value = "业务类型, 1:PURCHASE-采购运输, 2:SALES-销售运输, 3:ALLOT-厂间调拨, 4:EXTERNAL-外部运输")
    private OrderBusinessTypeEnum businessType;

    @ApiModelProperty(value = "运单状态")
    private DeliveryOrderStatusEnum deliveryStatus;

    // @ApiModelProperty(value = "客户名称")
    // private String customerName;
    //
    // @ApiModelProperty(value = "需求订单编号")
    // private String demandOrderNo;
    //
    // @ApiModelProperty(value = "承运商name")
    // private String carrierName;

    @ApiModelProperty(value = "承运订单编号")
    private String carrierOrderNo;

    @ApiModelProperty(value = "发货地址")
    private String loadsAddrShort;

    @ApiModelProperty(value = "收车地址")
    private String unloadAddrShort;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "过磅时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime weightTimeLeft;

    @ApiModelProperty(value = "过时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime weightTimeRight;
}
