package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 对账单查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SifBillPageQuery extends PageSortQuery implements Serializable {
    List<Long> ids;
    /**
     * 单号
     */
    private String billNo;
    /**
     * 应收还是应付
     */
    @ApiModelProperty("对于运营商 1应收 2应付 对于承运商 1应收")
    private BillTypeEnum billType;
}
