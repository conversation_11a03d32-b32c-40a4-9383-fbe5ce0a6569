package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 结算单查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SifBillSettlePageQuery extends PageSortQuery implements Serializable {
    private List<Long> ids;
    /**
     * 单号
     */
    @ApiModelProperty("单号")
    private String settleNo;
    /**
     * 应收还是应付
     */
    @ApiModelProperty("对于运营商 1应收 2应付 对于承运商 1应收")
    private BillTypeEnum billType;
}
