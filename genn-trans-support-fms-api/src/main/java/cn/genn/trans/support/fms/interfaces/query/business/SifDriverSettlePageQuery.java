package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.support.fms.interfaces.enums.sif.DriverTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.sif.SettleStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 司机绩效待结算分页查询类
 * @since 2024/8/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SifDriverSettlePageQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("结算状态")
    private SettleStatusEnum settleStatus;

    @ApiModelProperty("租户ID")
    private Long tenantId;

    @ApiModelProperty("运营商ID")
    private Long operatorId;

    @ApiModelProperty("承运商ID")
    private Long carrierId;

    @ApiModelProperty("结算IdList")
    private List<Long> settleIdList;

    @ApiModelProperty("结算编号")
    private String settleNo;

    @ApiModelProperty("司机姓名")
    private String driverName;

    @ApiModelProperty("司机手机号")
    private String driverMobile;

    @ApiModelProperty("司机类型")
    private DriverTypeEnum driverType;

    @ApiModelProperty("汇总开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime summaryTimeBegin;

    @ApiModelProperty("汇总结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime summaryTimeEnd;

    @ApiModelProperty("结算开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime settleTimeBegin;

    @ApiModelProperty("结算结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime settleTimeEnd;

    @ApiModelProperty(value = "结算月份")
    private String settleMonth;
}
