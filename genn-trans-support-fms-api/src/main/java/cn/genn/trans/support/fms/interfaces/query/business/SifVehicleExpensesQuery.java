package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.AuditStatusEnum;
import cn.genn.trans.support.fms.interfaces.enums.DriverTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.HasExpensesEnum;
import cn.genn.trans.support.fms.interfaces.enums.SettleStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SifVehicleExpenses查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SifVehicleExpensesQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "承运商id")
    private Long carrierId;

    @ApiModelProperty(value = "运单号")
    private String orderNo;

    @ApiModelProperty(value = "结算单号")
    private String settleNo;

    @ApiModelProperty(value = "业务类型")
    private OrderBusinessTypeEnum businessType;

    @ApiModelProperty(value = "是否有随车费用, 0:没有, 1:有")
    private HasExpensesEnum hasExpenses;

    @ApiModelProperty(value = "审核状态, 0:待审核,1:已通过,2:已驳回")
    private AuditStatusEnum auditStatus;

    @ApiModelProperty(value = "是否系统审核")
    private Boolean isSystemAudit;

    @ApiModelProperty(value = "货物编号")
    private String cargoNo;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "承运线路ID")
    private Long carrierLineId;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "司机类型, 0:自有, 1:外协")
    private DriverTypeEnum driverType;

    @ApiModelProperty(value = "车牌号")
    private String vehiclePlateNo;

    @ApiModelProperty(value = "挂车车牌号")
    private String trailerPlateNo;

    @ApiModelProperty(value = "结算状态")
    private SettleStatusEnum settleStatus;

    @ApiModelProperty(value = "装车地址id")
    private Long loadingAddrId;

    @ApiModelProperty(value = "卸车地址id")
    private Long unloadingAddrId;

    @ApiModelProperty(value = "提交开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitStartTime;

    @ApiModelProperty(value = "提交结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitEndTime;

    @ApiModelProperty(value = "审核开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditStartTime;

    @ApiModelProperty(value = "审核结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditEndTime;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signStartTime;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signEndTime;

    @ApiModelProperty(value = "装车开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadingStartTime;

    @ApiModelProperty(value = "装车结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadingEndTime;

    @ApiModelProperty(value = "卸车开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadingStartTime;

    @ApiModelProperty(value = "卸车结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadingEndTime;

    @ApiModelProperty(value = "装车费审核状态, 1:已通过,2:已驳回")
    private AuditStatusEnum loadingAuditStatus;

    @ApiModelProperty(value = "卸车费审核状态, 1:已通过,2:已驳回")
    private AuditStatusEnum unloadingAuditStatus;

    @ApiModelProperty(value = "其他费审核状态, 0:待审核,1:已通过,2:已驳回")
    private AuditStatusEnum otherAuditStatus;

    @ApiModelProperty(value = "过路费审核状态, 1:已通过,2:已驳回")
    private AuditStatusEnum tollAuditStatus;

}
