package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 暂估单查询
 * @date 2024-10-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TempEstimateBillQuery extends PageSortQuery {

    @ApiModelProperty("暂估单号")
    private String billNo;

    @ApiModelProperty("暂估单状态")
    private Integer billStatus;

    @ApiModelProperty(value = "付款方ID", required = true)
    private Long payId;

    @ApiModelProperty(value = "付款方税号")
    private String payCreditCode;

    @ApiModelProperty(value = "付款方名称")
    private String payName;

    @ApiModelProperty(value = "收款方ID")
    private Long payeeId;

    @ApiModelProperty(value = "收款方税号")
    private String payeeCreditCode;

    @ApiModelProperty(value = "收款方名称")
    private String payeeName;

    @ApiModelProperty(value = "货物编号")
    private String cargoNo;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "装车地址简称")
    private String loadingSubName;

    @ApiModelProperty(value = "卸车地址简称")
    private String unloadingSubName;

    @ApiModelProperty(value = "暂估开始时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTimeLeft;

    @ApiModelProperty(value = "暂估开始时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTimeRight;

    @ApiModelProperty(value = "暂估结束时间-左区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTimeLeft;

    @ApiModelProperty(value = "暂估结束时间-右区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTimeRight;

    @ApiModelProperty(value = "客户/承运商名称")
    private String relName;
}
