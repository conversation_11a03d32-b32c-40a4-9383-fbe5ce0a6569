package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TempEstimateRedBill查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TempEstimateRedBillQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应收暂估红冲单号")
    private String redBillNo;

    @ApiModelProperty(value = "付款方ID")
    private Long payId;

    @ApiModelProperty(value = "付款方名称")
    private String payName;

    @ApiModelProperty(value = "收款方ID")
    private Long payeeId;

    @ApiModelProperty(value = "收款方名称")
    private String payeeName;

    @ApiModelProperty(value = "装车地址简称")
    private String loadingSubName;

    @ApiModelProperty(value = "卸车地址简称")
    private String unloadingSubName;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "数据同步状态, 0:未同步,1:同步中, 2:同步成功, 3:同步失败")
    private Integer syncStatus;

    @ApiModelProperty(value = "暂估红冲周期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "暂估红冲周期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "客户/承运商名称")
    private String relName;

}

