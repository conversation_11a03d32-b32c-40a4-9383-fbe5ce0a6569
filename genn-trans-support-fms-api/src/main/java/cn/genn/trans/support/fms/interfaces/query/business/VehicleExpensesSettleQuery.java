package cn.genn.trans.support.fms.interfaces.query.business;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 随车费用结算单查询
 * @date 2024-07-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VehicleExpensesSettleQuery extends PageSortQuery {

    @ApiModelProperty(value = "承运商id")
    private Long carrierId;

    @ApiModelProperty(value = "结算单号")
    private String settleNo;

    @ApiModelProperty(value = "运单号")
    private String orderNo;

    @ApiModelProperty(value = "司机姓名")
    public String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "司机类型 0-自有 1-外协")
    private Integer driverType;

    @ApiModelProperty(value = "结算状态 0-待汇总 1-待结算 2-已结算")
    private Integer settleStatus;

    @ApiModelProperty(value = "汇总时间开始")
    private LocalDateTime createTimeStart;

    @ApiModelProperty(value = "汇总时间结束")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty(value = "结算时间开始")
    private LocalDateTime settleTimeStart;

    @ApiModelProperty(value = "结算时间结束")
    private LocalDateTime settleTimeEnd;

}
