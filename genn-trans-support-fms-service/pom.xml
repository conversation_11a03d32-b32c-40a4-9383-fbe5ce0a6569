<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-trans-support-fms</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-trans-support-fms-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <esdk-obs-java.version>3.22.12</esdk-obs-java.version>
        <aws-java-sdk-s3.version>1.12.429</aws-java-sdk-s3.version>
        <okhttp.version>4.10.0</okhttp.version>
        <guava.version>30.1.1-jre</guava.version>
        <groovy.version>3.0.21</groovy.version>
        <thumbnailator.version>0.4.20</thumbnailator.version>
        <tika-core.version>2.4.1</tika-core.version>
        <javax.servlet-api.version>4.0.1</javax.servlet-api.version>
        <jakarta.servlet-api.version>5.0.0</jakarta.servlet-api.version>
        <easypoi.version>4.4.0</easypoi.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <oapi-sdk.version>2.2.7</oapi-sdk.version>
        <docx4j-wisewe.version>1.7.0.RELEASE</docx4j-wisewe.version>
        <itextpdf.version>5.5.10</itextpdf.version>
        <itext-asian.version>5.2.0</itext-asian.version>
        <ve-tos-java-sdk.version>2.8.8</ve-tos-java-sdk.version>
        <volc-sdk-java.version>1.0.210</volc-sdk-java.version>
    </properties>

    <dependencies>
        <!-- ==================== genn 内部依赖   ===================      -->

        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-skywalking</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-xxl-job</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-event-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-monitor</artifactId>
        </dependency>



        <!-- ======================= 服务api依赖  ===========================      -->

        <!-- 自身api依赖        -->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-support-fms-api</artifactId>
            <version>${genn-trans-support-fms-api.version}</version>
        </dependency>
        <!-- 其他项目 api依赖       -->
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-support-upm-api</artifactId>
            <version>${genn-trans-support-upm-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-generic-agg-api</artifactId>
            <version>${genn-trans-generic-agg-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-core-order-api</artifactId>
            <version>${genn-trans-core-order-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-core-sif-api</artifactId>
            <version>${genn-trans-core-sif-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-fsp-api</artifactId>
            <version>${genn-trans-fsp-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.genn.app</groupId>
            <artifactId>genn-trans-api-common</artifactId>
            <version>${genn-trans-api-common.version}</version>
        </dependency>
        <!-- ======================= 其他三方依赖   =======================      -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 华为云 OBS -->
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
            <version>${esdk-obs-java.version}</version>
        </dependency>
        <!-- Amazon S3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>${aws-java-sdk-s3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>ve-tos-java-sdk</artifactId>
            <version>${ve-tos-java-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.volcengine</groupId>
            <artifactId>volc-sdk-java</artifactId>
            <version>${volc-sdk-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- Apache commons-pool2 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>
        <!-- 图片处理 https://github.com/coobird/thumbnailator -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>${thumbnailator.version}</version>
        </dependency>
        <!-- Tika -->
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>${tika-core.version}</version>
        </dependency>
        <!-- javax.servlet-api -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${javax.servlet-api.version}</version>
        </dependency>
        <!-- jakarta.servlet-api -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta.servlet-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${easypoi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>${oapi-sdk.version}</version>
        </dependency>
        <!--<dependency>
            <groupId>cn.wisewe</groupId>
            <artifactId>docx4j-wisewe-output</artifactId>
            <version>${docx4j-wisewe.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.wisewe</groupId>
            <artifactId>docx4j-wisewe-input</artifactId>
            <version>${docx4j-wisewe.version}</version>
        </dependency>-->
        <dependency>
            <groupId>cn.wisewe</groupId>
            <artifactId>docx4j-wisewe-convert</artifactId>
            <version>${docx4j-wisewe.version}</version>
        </dependency>

        <!-- itext pdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>${itext-asian.version}</version>
        </dependency>

        <!-- ftl模板依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
            <version>2.2.9.RELEASE</version>
        </dependency>

        <!-- html转图片 -->
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-core</artifactId>
            <version>9.1.22</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>genn-trans-support-fms</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <!--  自身和外部api包管理,每次依赖一个新的外部api,在这里定义版本号,dev和test保持 为 genn-service-api.version     -->
        <profile>
            <id>local</id>
            <properties>
                <genn-trans-support-fms-api.version>1.0.0-SNAPSHOT</genn-trans-support-fms-api.version>
                <genn-trans-support-upm-api.version>1.0.1-SNAPSHOT</genn-trans-support-upm-api.version>
                <genn-trans-generic-agg-api.version>1.0.0-SNAPSHOT</genn-trans-generic-agg-api.version>
                <genn-trans-core-order-api.version>1.0.0-SNAPSHOT</genn-trans-core-order-api.version>
                <genn-trans-core-sif-api.version>1.0.0-SNAPSHOT</genn-trans-core-sif-api.version>
                <genn-trans-fsp-api.version>1.0.0-SNAPSHOT</genn-trans-fsp-api.version>
                <genn-trans-api-common.version>1.0.0-SNAPSHOT</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <genn-trans-support-fms-api.version>1.2.4-RELEASE</genn-trans-support-fms-api.version>
                <genn-trans-support-upm-api.version>1.0.9-RELEASE</genn-trans-support-upm-api.version>
                <genn-trans-generic-agg-api.version>1.2.1-RELEASE</genn-trans-generic-agg-api.version>
                <genn-trans-core-order-api.version>1.3.9-RELEASE</genn-trans-core-order-api.version>
                <genn-trans-core-sif-api.version>1.2.9-RELEASE</genn-trans-core-sif-api.version>
                <genn-trans-fsp-api.version>1.0.2-RELEASE</genn-trans-fsp-api.version>
                <genn-trans-api-common.version>1.0.4-RELEASE</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <genn-trans-support-fms-api.version>${genn-service-api.version}</genn-trans-support-fms-api.version>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
                <genn-trans-generic-agg-api.version>${genn-service-api.version}</genn-trans-generic-agg-api.version>
                <genn-trans-core-order-api.version>${genn-service-api.version}</genn-trans-core-order-api.version>
                <genn-trans-core-sif-api.version>${genn-service-api.version}</genn-trans-core-sif-api.version>
                <genn-trans-fsp-api.version>${genn-service-api.version}</genn-trans-fsp-api.version>
                <genn-trans-api-common.version>${genn-service-api.version}</genn-trans-api-common.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <genn-trans-support-fms-api.version>${genn-service-api.version}</genn-trans-support-fms-api.version>
                <genn-trans-support-upm-api.version>${genn-service-api.version}</genn-trans-support-upm-api.version>
                <genn-trans-generic-agg-api.version>${genn-service-api.version}</genn-trans-generic-agg-api.version>
                <genn-trans-core-order-api.version>${genn-service-api.version}</genn-trans-core-order-api.version>
                <genn-trans-core-sif-api.version>${genn-service-api.version}</genn-trans-core-sif-api.version>
                <genn-trans-fsp-api.version>${genn-service-api.version}</genn-trans-fsp-api.version>
                <genn-trans-api-common.version>${genn-service-api.version}</genn-trans-api-common.version>
            </properties>
        </profile>
    </profiles>
</project>
