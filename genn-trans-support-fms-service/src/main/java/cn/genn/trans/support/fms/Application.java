package cn.genn.trans.support.fms;

import cn.genn.trans.support.fms.spring.EnableFileStorage;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * <AUTHOR>
 */
@MapperScan({"cn.genn.trans.support.fms.infrastructure.repository.mapper"})
@SpringBootApplication(scanBasePackages = {"cn.genn.trans.support.fms", "cn.genn.trans.core.order", "cn.genn.trans.upm.interfaces.base", "cn.genn.trans.generic.agg.interfaces"})
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"cn.genn.trans.core.order.interfaces", "cn.genn.trans.generic.agg.interfaces", "cn.genn.trans.upm.interfaces"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableFileStorage
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
