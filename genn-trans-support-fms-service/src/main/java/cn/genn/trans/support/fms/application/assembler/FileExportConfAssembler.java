package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportConfPO;
import cn.genn.trans.support.fms.interfaces.dto.FileExportConfDTO;
import cn.genn.trans.support.fms.interfaces.query.FileExportConfQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileExportConfAssembler extends QueryAssembler<FileExportConfQuery, FileExportConfPO, FileExportConfDTO>{
}

