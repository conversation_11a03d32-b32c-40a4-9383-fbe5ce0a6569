package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportTaskPO;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.query.FileExportTaskQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileExportTaskAssembler extends QueryAssembler<FileExportTaskQuery, FileExportTaskPO, FileExportTaskDTO> {

    FileExportTaskPO toPO(FileExportTaskDTO fileExportTaskDTO);

}

