package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileImportConfPO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportConfDTO;
import cn.genn.trans.support.fms.interfaces.query.FileImportConfQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileImportConfAssembler extends QueryAssembler<FileImportConfQuery, FileImportConfPO, FileImportConfDTO>{
}

