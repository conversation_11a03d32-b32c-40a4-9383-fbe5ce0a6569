package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.DTOAssembler;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Date: 2024/5/28
 * @Author: kang<PERSON><PERSON>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileInfoAssembler extends DTOAssembler<FileInfo, FileInfoDTO> {

}
