package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadBucketConfigPO;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBucketConfigQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileUploadBucketConfigAssembler extends QueryAssembler<FileUploadBucketConfigQuery, FileUploadBucketConfigPO, FileUploadBucketConfigDTO>{
}

