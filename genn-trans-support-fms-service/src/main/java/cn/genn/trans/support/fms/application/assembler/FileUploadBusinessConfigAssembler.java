package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadBusinessConfigPO;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBusinessConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBusinessConfigQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileUploadBusinessConfigAssembler extends QueryAssembler<FileUploadBusinessConfigQuery, FileUploadBusinessConfigPO, FileUploadBusinessConfigDTO>{
}

