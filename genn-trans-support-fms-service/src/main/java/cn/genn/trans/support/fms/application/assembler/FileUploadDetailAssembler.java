package cn.genn.trans.support.fms.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadDetailPO;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadDetailDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadDetailQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileUploadDetailAssembler extends QueryAssembler<FileUploadDetailQuery, FileUploadDetailPO, FileUploadDetailDTO>{
}

