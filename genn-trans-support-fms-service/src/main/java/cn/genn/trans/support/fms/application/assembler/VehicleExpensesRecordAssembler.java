package cn.genn.trans.support.fms.application.assembler;

import cn.genn.trans.core.sif.interfaces.dto.vehicleexpenses.VehicleExpensesPageDTO;
import cn.genn.trans.support.fms.application.dto.VehicleExpensesRecordDTO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleExpensesRecordAssembler {
    default VehicleExpensesRecordDTO toVehicleExpensesRecordDTO(VehicleExpensesPageDTO vehicleExpensesPageDTO) {
        VehicleExpensesRecordDTO vehicleExpensesRecordDTO = BeanUtil.copyProperties(vehicleExpensesPageDTO, VehicleExpensesRecordDTO.class);

        String unit = " (" + vehicleExpensesPageDTO.getCarrierChargeUnit().getDescription() +")";
        switch (vehicleExpensesPageDTO.getCarrierChargeUnit()){
            case TON:
                vehicleExpensesRecordDTO.setLoadingQuantity(vehicleExpensesPageDTO.getLoadingWeight() + unit);
                vehicleExpensesRecordDTO.setUnloadingQuantity(vehicleExpensesPageDTO.getUnloadingWeight() + unit);
                break;
            case SIDE:
                vehicleExpensesRecordDTO.setLoadingQuantity(vehicleExpensesPageDTO.getLoadingVolume() + unit);
                vehicleExpensesRecordDTO.setUnloadingQuantity(vehicleExpensesPageDTO.getUnloadingVolume() + unit);
                break;
            case UNIT:
                vehicleExpensesRecordDTO.setLoadingQuantity(vehicleExpensesPageDTO.getLoadingPackage() + unit);
                vehicleExpensesRecordDTO.setUnloadingQuantity(vehicleExpensesPageDTO.getUnloadingPackage() + unit);
                break;
            case TRUCK:
                vehicleExpensesRecordDTO.setLoadingQuantity("1" + unit);
                vehicleExpensesRecordDTO.setUnloadingQuantity("1" + unit);
                break;
            default:
                break;
        }

        if (ObjectUtil.isNotNull(vehicleExpensesPageDTO.getOtherFeeDetail())) {
            vehicleExpensesRecordDTO.setCardFee((Optional.ofNullable(vehicleExpensesPageDTO.getOtherFeeDetail().getCardFee()).orElse(BigDecimal.ZERO)).setScale(2));
            vehicleExpensesRecordDTO.setParkingFee((Optional.ofNullable(vehicleExpensesPageDTO.getOtherFeeDetail().getParkingFee()).orElse(BigDecimal.ZERO)).setScale(2));
            vehicleExpensesRecordDTO.setLabelFee((Optional.ofNullable(vehicleExpensesPageDTO.getOtherFeeDetail().getLabelFee()).orElse(BigDecimal.ZERO)).setScale(2));
            vehicleExpensesRecordDTO.setOtherFee((Optional.ofNullable(vehicleExpensesPageDTO.getOtherFeeDetail().getOtherFee()).orElse(BigDecimal.ZERO)).setScale(2));
        }else {
            vehicleExpensesRecordDTO.setCardFee(BigDecimal.ZERO.setScale(2));
            vehicleExpensesRecordDTO.setParkingFee(BigDecimal.ZERO.setScale(2));
            vehicleExpensesRecordDTO.setLabelFee(BigDecimal.ZERO.setScale(2));
            vehicleExpensesRecordDTO.setOtherFee(BigDecimal.ZERO.setScale(2));
        }
        return vehicleExpensesRecordDTO;
    }
}
