package cn.genn.trans.support.fms.application.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class Md2HtmlCommand {

    /**
     * md内容,会默认作为参数 {mdContent} 传入html模板
     */
    @ApiModelProperty(value = "md内容base64", required = true)
    @NotEmpty(message = "md内容不能为空")
    private String mdContent;

    /**
     * html模板
     */
    @ApiModelProperty(value = "html模板base64字符串", required = true)
    @NotEmpty(message = "html模板base64不能为空")
    private String htmlTemplate;

    /**
     * 可选,其他需要的参数,需要对应模板中的参数
     */
    @ApiModelProperty(value = "参数")
    private Map<String, Object> params;

}
