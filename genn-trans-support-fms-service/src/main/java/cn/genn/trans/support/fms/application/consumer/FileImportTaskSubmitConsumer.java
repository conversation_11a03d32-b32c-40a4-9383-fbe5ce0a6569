package cn.genn.trans.support.fms.application.consumer;

import cn.genn.spring.boot.starter.event.rocketmq.component.BaseConsumerListener;
import cn.genn.trans.support.fms.application.dto.FileExportTaskSubmitEvent;
import cn.genn.trans.support.fms.application.dto.FileImportTaskSubmitEvent;
import cn.genn.trans.support.fms.application.service.action.FileExportTaskActionService;
import cn.genn.trans.support.fms.application.service.action.FileImportTaskActionService;
import cn.genn.trans.support.fms.infrastructure.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@RocketMQMessageListener(topic = "${genn.event.rocketmq.consumer.default-topic}",
    selectorExpression = Constants.FILE_IMPORT_TASK_SUBMIT_MQ_TAG, maxReconsumeTimes = 3, consumeMode = ConsumeMode.CONCURRENTLY,
    consumerGroup = "${genn.event.rocketmq.consumer.import-consumer-group}")
@Slf4j
public class FileImportTaskSubmitConsumer extends BaseConsumerListener<FileImportTaskSubmitEvent> {

    @Resource
    private FileImportTaskActionService fileImportTaskActionService;

    /**
     * 处理消息
     *
     * @param event
     */
    @Override
    protected void process(FileImportTaskSubmitEvent event) {
        log.info("FileImportTaskSubmitConsumer process event:{}", event);
        if (Objects.isNull(event.getTaskId())) {
            return;
        }
        fileImportTaskActionService.executeTask(event.getTaskId());
    }
}
