package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.core.order.interfaces.enums.contract.ChargeUnitEnum;
import cn.genn.trans.core.order.interfaces.enums.contract.SettleTypeEnum;
import cn.genn.trans.core.sif.interfaces.dto.charge.BillStatDTO;
import cn.genn.trans.generic.agg.interfaces.enums.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOrderPageDTO {

    @ApiModelProperty(value = "运单id")
    private Long deliveryId;

    @ApiModelProperty(value = "运单号")
    private String orderNo;

    @ApiModelProperty(value = "运单状态")
    private DeliveryStatusEnum deliveryStatus;

    @ApiModelProperty(value = "运单状态名称")
    private String deliveryStatusName;

    @ApiModelProperty(value = "业务类型, 1:PURCHASE-采购运输, 2:SALES-销售运输, 3:ALLOT-厂间调拨, 4:EXTERNAL-外部运输）")
    private OrderBusinessTypeEnum businessType;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "货物编码")
    private String cargoNo;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    private String cargoSpec;

    @ApiModelProperty(value = "装车地址")
    private String loadsAddrShort;

    @ApiModelProperty(value = "装车详细地址")
    private String loadsAddr;

    @ApiModelProperty(value = "卸车地址")
    private String unloadAddrShort;

    @ApiModelProperty(value = "卸车详细地址")
    private String unloadAddr;

    //  ======== 司机 ========
    @ApiModelProperty(value = "司机id")
    private Long driverId;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;

    //  ======== 车辆 ========
    @ApiModelProperty(value = "车辆id")
    private Long vehicleId;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "挂车车辆id")
    private Long trailerId;

    @ApiModelProperty(value = "挂车车牌号")
    private String trailerPlateNumber;

    @ApiModelProperty(value = "挂车类型编码")
    private String trailerCode;

    @ApiModelProperty(value = "挂车类型名称")
    private String trailerTypeName;

    @ApiModelProperty(value = "车辆code")
    private String vehicleCode;

    @ApiModelProperty(value = "车辆code name,举例:燃油车")
    private String vehicleCodeName;

    @ApiModelProperty(value = "装货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loadsTime;

    @ApiModelProperty(value = "卸货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unloadTime;

    //  ======== 装卸量 ========
    @ApiModelProperty(value = "计费单位, 1:TON-吨, 2:SIDE-方, 3:UNIT-件, 4:TRUCK-车")
    private ChargeUnitEnum chargeUnit;

    @ApiModelProperty(value = "承运计费单位")
    private ChargeUnitEnum carrierChargeUnit;

    @ApiModelProperty(value = "装车皮重 单位:吨")
    private BigDecimal loadsTareWeight;

    @ApiModelProperty(value = "装车毛重 单位:吨")
    private BigDecimal loadsGrossWeight;

    @ApiModelProperty(value = "装车净重 单位:吨")
    private BigDecimal loadsNetWeight;

    @ApiModelProperty(value = "装货体积 单位:方")
    private BigDecimal loadsVolume;

    @ApiModelProperty(value = "卸车皮重 单位:吨")
    private BigDecimal unloadTareWeight;

    @ApiModelProperty(value = "卸车毛重 单位:吨")
    private BigDecimal unloadGrossWeight;

    @ApiModelProperty(value = "卸车净重 单位:吨")
    private BigDecimal unloadNetWeight;

    @ApiModelProperty(value = "卸货体积 单位:方")
    private BigDecimal unloadVolume;

    @ApiModelProperty(value = "装车审核状态, 0:NO_APPROVE-未审核, 1:PASS-审核通过, 2:REJECT-驳回")
    private ApproveStatusEnum loadsApproveStatus;

    @ApiModelProperty(value = "卸车审核状态, 0:NO_APPROVE-未审核, 1:PASS-审核通过, 2:REJECT-驳回")
    private ApproveStatusEnum unloadApproveStatus;

    // ======== 签收 ==========
    @ApiModelProperty(value = "签收方式, 0:AUTO-自动签收 1:MANUAL-手动签收)")
    private SignTypeEnum signType;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "客户计费详情")
    private BillStatDTO customerBill;

    @ApiModelProperty(value = "承运商计费详情")
    private BillStatDTO carrierBill;

    @ApiModelProperty(value = "客户类别")
    private CustomerIdentityEnum identity;
//    @RmbConverter
    @ApiModelProperty(value = "运价（含税）- 客户")
    private BigDecimal demandTaxPrice;

    @ApiModelProperty("运输线路id")
    private Long demandLineId;

    @ApiModelProperty(value = "（客户） 计费类型, 1:TRUCK_LOAD-以装车量计费, 2:UNLOAD-以卸车量计费, 3:MIX-以较小值计费")
    private ChargeTypeEnum demandChargeType;

//    @RmbConverter
    @ApiModelProperty(value = "运价（含税）-承运商")
    private BigDecimal carrierTaxPrice;

    @ApiModelProperty("承运线路id")
    private Long carrierLineId;

    @ApiModelProperty(value = "(承运商)计费类型, 1:TRUCK_LOAD-以装车量计费, 2:UNLOAD-以卸车量计费, 3:MIX-以较小值计费")
    private ChargeTypeEnum carrierChargeType;

    @ApiModelProperty(value = "结算方式, 10:UNLINE_TRANSFER_ACCOUNTS-对公转账")
    private SettleTypeEnum carrierSettleType;

    @ApiModelProperty("服务费率")
    private BigDecimal serviceFeeRate;

    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;


    @ApiModelProperty(value = "运单item id")
    private Long deliveryItemId;

    @ApiModelProperty("承运商ID")
    private Long carrierId;
    @ApiModelProperty("承运商name")
    private String carrierName;
    @ApiModelProperty("承运商类别")
    private CarrierCategoryEnum category;

    @ApiModelProperty("客户ID")
    private Long customerId;
    @ApiModelProperty("客户编号")
    private String customerNo;
    @ApiModelProperty("客户名称")
    private String customerName;


    @ApiModelProperty(value = "客户-服务费率")
    private BigDecimal customerFeeRate;
    @ApiModelProperty(value = "客户-服务费")
    private BigDecimal customerFee;

    @ApiModelProperty(value = "需求订单ID")
    private Long demandOrderId;
    @ApiModelProperty(value = "需求订单编号")
    private String demandOrderNo;

    @ApiModelProperty(value = "通知单号")
    private String noticeOrderNo;
    @ApiModelProperty("发货公司名称")
    private String noticeDeliveryCompany;
    @ApiModelProperty("收货公司名称")
    private String noticeTakeCompany;
    @ApiModelProperty("发货方地址")
    private String noticeDeliveryAddress;
    @ApiModelProperty("收货方地址")
    private String noticeTakeAddress;
    @ApiModelProperty(value = "采购组织")
    private String noticeRecipientGroup;

    @ApiModelProperty(value = "运输合同ID")
    private Long demandContractId;
    @ApiModelProperty(value = "运输合同编号")
    private String demandContractNo;

    @ApiModelProperty(value = "运输任务id")
    private Long taskId;
    @ApiModelProperty(value = "运输任务编号")
    private String taskNo;

    @ApiModelProperty(value = "承运订单ID")
    private Long carrierOrderId;
    @ApiModelProperty(value = "承运订单编号")
    private String carrierOrderNo;

    @ApiModelProperty(value = "承运合同ID")
    private Long carrierContractId;
    @ApiModelProperty(value = "承运合同编号")
    private String carrierContractNo;

    // ======= 发（收）货方信息

    @ApiModelProperty(value = "发货公司名称")
    private String demandDeliverCompanyName;

    @ApiModelProperty(value = "发货人姓名")
    private String demandDeliverName;

    @ApiModelProperty(value = "发货人手机号")
    private String demandDeliverMobile;

    @ApiModelProperty(value = "收货公司名称")
    private String demandTakeCompanyName;

    @ApiModelProperty(value = "收货人姓名")
    private String demandTakeName;

    @ApiModelProperty(value = "收货人手机号")
    private String demandTakeMobile;

    @ApiModelProperty(value = "调度类型, 1:GRAB-抢单, 2:DISPATCH-派单")
    private DispatchTypeEnum dispatchType;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "业务员")
    private String agent;

    @ApiModelProperty(value = "承运合同主体A")
    private Long carrierSubjectId;

    @ApiModelProperty(value = "承运合同主体A")
    private String carrierSubjectName;

    @ApiModelProperty(value = "承运合同主体A")
    private String carrierSubjectCode;

    @ApiModelProperty(value = "运输合同主体A")
    private Long demandSubjectId;

    @ApiModelProperty(value = "运输合同主体A")
    private String demandSubjectName;

    @ApiModelProperty(value = "运输合同主体A")
    private String demandSubjectCode;

    @ApiModelProperty("导航距离")
    private String carrierDistance;

    @ApiModelProperty("导航距离时间")
    private String carrierDistanceTime;

    @ApiModelProperty("装车磅单图")
    private String loadsPoundPic;

    @ApiModelProperty("卸车磅单图")
    private String unloadPoundPic;
}
