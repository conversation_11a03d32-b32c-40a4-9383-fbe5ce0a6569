package cn.genn.trans.support.fms.application.dto;

import cn.genn.spring.boot.starter.event.rocketmq.model.RocketMQBaseEvent;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileExportTaskSubmitEvent extends RocketMQBaseEvent<FileExportTaskSubmitEvent> {

    private Long taskId;

    @Override
    protected FileExportTaskSubmitEvent self() {
        return this;
    }
    public static class Builder extends RocketMQBaseEvent.Builder<Builder, FileExportTaskSubmitEvent> {
        @Override
        protected Builder self() {
            return this;
        }

        @Override
        protected FileExportTaskSubmitEvent buildEvent() {
            return new FileExportTaskSubmitEvent();
        }

        public static Builder builder() {
            return new Builder();
        }

    }
}
