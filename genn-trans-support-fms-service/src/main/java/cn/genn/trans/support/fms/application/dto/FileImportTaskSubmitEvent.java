package cn.genn.trans.support.fms.application.dto;

import cn.genn.spring.boot.starter.event.rocketmq.model.RocketMQBaseEvent;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileImportTaskSubmitEvent extends RocketMQBaseEvent<FileImportTaskSubmitEvent> {

    private Long taskId;

    @Override
    protected FileImportTaskSubmitEvent self() {
        return this;
    }
    public static class Builder extends RocketMQBaseEvent.Builder<Builder, FileImportTaskSubmitEvent> {
        @Override
        protected Builder self() {
            return this;
        }

        @Override
        protected FileImportTaskSubmitEvent buildEvent() {
            return new FileImportTaskSubmitEvent();
        }

        public static Builder builder() {
            return new Builder();
        }

    }
}
