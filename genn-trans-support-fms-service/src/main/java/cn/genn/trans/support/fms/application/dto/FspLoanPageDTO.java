package cn.genn.trans.support.fms.application.dto;

import cn.genn.core.model.enums.BooleanEnum;
import cn.genn.trans.fsp.interfaces.enums.LoanStatusEnum;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * FspLoanDTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FspLoanPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "承运商名称")
    @DataBeanPath(fieldName = "承运商名称", beanPath = "carrierName")
    private String carrierName;

    @ApiModelProperty(value = "运营商名称")
    @DataBeanPath(fieldName = "运营商名称", beanPath = "operatorName")
    private String operatorName;

    @ApiModelProperty(value = "保理商名称")
    @DataBeanPath(fieldName = "保理商名称", beanPath = "factoringName")
    private String factoringName;

    @ApiModelProperty(value = "合同编号")
    @DataBeanPath(fieldName = "合同编号", beanPath = "contractNo")
    private String contractNo;

    @ApiModelProperty(value = "转让债权金额(元)")
    @DataBeanPath(fieldName = "转让债权金额(元)", beanPath = "loanAmount")
    private BigDecimal loanAmount;

    @ApiModelProperty(value = "保理周期")
    @DataBeanPath(fieldName = "保理周期", beanPath = "loanPeriod")
    private Integer loanPeriod;

    @ApiModelProperty(value = "保理开始日")
    @DataBeanPath(fieldName = "保理开始日", beanPath = "signTime")
    private String signTime;

    @ApiModelProperty(value = "保理到期日")
    @DataBeanPath(fieldName = "保理到期日", beanPath = "dueDate")
    private String dueDate;

    @ApiModelProperty(value = "放款日期")
    @DataBeanPath(fieldName = "放款日期", beanPath = "loanDate")
    private String loanDate;

    @ApiModelProperty(value = "年利率")
    @DataBeanPath(fieldName = "年利率", beanPath = "annualInterestRate")
    private BigDecimal annualInterestRate;

    @ApiModelProperty(value = "手续费率")
    @DataBeanPath(fieldName = "手续费率", beanPath = "serviceChargeRate")
    private BigDecimal serviceChargeRate;

    @ApiModelProperty(value = "应还本金(元)")
    @DataBeanPath(fieldName = "应还本金(元)", beanPath = "loanAmount")
    private BigDecimal principal;

    @ApiModelProperty(value = "应还手续费(元)")
    @DataBeanPath(fieldName = "应还手续费(元)", beanPath = "serviceCharge")
    private BigDecimal serviceCharge;

    @ApiModelProperty(value = "应还利息(元)")
    @DataBeanPath(fieldName = "应还利息(元)", beanPath = "interestAmount")
    private BigDecimal interestAmount;

    @ApiModelProperty(value = "应还逾期(元)")
    @DataBeanPath(fieldName = "应还逾期(元)", beanPath = "overdueInterestAmount")
    private BigDecimal overdueInterestAmount;

    @ApiModelProperty(value = "累计还款小计(元)")
    @DataBeanPath(fieldName = "累计还款小计(元)", beanPath = "totalRepayAmount")
    private BigDecimal totalRepayAmount;

    @ApiModelProperty(value = "已还逾期(元)")
    @DataBeanPath(fieldName = "已还逾期(元)", beanPath = "repayOverdueAmount")
    private BigDecimal repayOverdueAmount;

    @ApiModelProperty(value = "已还手续费")
    @DataBeanPath(fieldName = "已还手续费(元)", beanPath = "repayServiceCharge")
    private BigDecimal repayServiceCharge;

    @ApiModelProperty(value = "已还利息")
    @DataBeanPath(fieldName = "已还利息(元)", beanPath = "repayInterestAmount")
    private BigDecimal repayInterestAmount;

    @ApiModelProperty(value = "已还本金")
    @DataBeanPath(fieldName = "已还本金(元)", beanPath = "repayPrincipalAmount")
    private BigDecimal repayPrincipalAmount;

    @ApiModelProperty(value = "未还本金")
    @DataBeanPath(fieldName = "未还本金(元)", beanPath = "leftLoanAmount")
    private BigDecimal leftLoanAmount;

    @ApiModelProperty(value = "未还手续费")
    @DataBeanPath(fieldName = "未还手续费(元)", beanPath = "leftServiceCharge")
    private BigDecimal leftServiceCharge;

    @ApiModelProperty(value = "未还利息")
    @DataBeanPath(fieldName = "未还利息(元)", beanPath = "leftInterestAmount")
    private BigDecimal leftInterestAmount;

    @ApiModelProperty(value = "未还逾期")
    @DataBeanPath(fieldName = "未还逾期(元)", beanPath = "leftOverdueAmount")
    private BigDecimal leftOverdueAmount;

    @ApiModelProperty(value = "资金状态(00:待放款;100:待还款;200:部分还款;900-已结清)")
    @DataBeanPath(fieldName = "资金状态", beanPath = "loanStatus.description")
    private LoanStatusEnum loanStatus;

    @ApiModelProperty(value = "逾期状态,0:未逾期,1:已逾期")
    @DataBeanPath(fieldName = "逾期状态", beanPath = "overdueStatus.description")
    private BooleanEnum overdueStatus;
}

