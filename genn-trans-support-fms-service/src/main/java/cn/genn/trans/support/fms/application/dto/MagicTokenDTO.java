package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Date: 2024/8/21
 * @Author: kangjian
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class MagicTokenDTO {

    private String username;

    private Long userId;
    /**
     * 租户信息
     */
    private Long tenantId;
    //    private String tenantName;
    //    private String tenantCode;
    //    private String vpcGroup;

    /**
     * 系统信息
     */
    private Long systemId;
    //    private String systemName;
    //    private String systemCode;

    /**
     * 运营商相关信息
     */
    private Long operatorId;
    //    private String operatorName;
    //    private String operatorCode;

    /**
     * 承运商相关信息
     */
    private Long carrierId;
    //    private String carrierName;
    //    private String carrierCode;

    /**
     * 司机相关信息
     */
    //    private Long driverId;
    //    private String driverName;

    /**
     * 站点id
     */
    private Long stationId;
    /**
     * 权限组
     */
    private String authKey;
    private AuthGroupEnum authGroup;
}
