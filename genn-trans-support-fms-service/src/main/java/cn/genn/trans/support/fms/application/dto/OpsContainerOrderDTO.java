package cn.genn.trans.support.fms.application.dto;

import cn.genn.state.log.model.EventLog;
import cn.genn.state.log.model.TransferLog;
import cn.genn.state.template.model.StateFlowNodeDTO;
import cn.genn.state.template.model.flow.StateNode;
import cn.genn.trans.core.order.interfaces.enums.container.ContainerStatusEnum;
import cn.genn.trans.core.order.interfaces.enums.contract.ChargeUnitEnum;
import cn.genn.trans.core.order.interfaces.enums.order.OrderStatusEnum;
import cn.genn.trans.core.order.interfaces.enums.pound.PoundPicSourceEnum;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运营商端-提箱单列表
 */
@Data
@Accessors(chain = true)
public class OpsContainerOrderDTO {

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("流程实例ID")
    private Long flowInstanceId;
    @ApiModelProperty("提箱单编号")
    private String orderNo;
    @ApiModelProperty("运输任务id")
    private Long taskId;
    @ApiModelProperty("运输任务编号")
    private String taskNo;
    @ApiModelProperty("运输任务状态")
    @DataBeanPath(beanPath = "taskStatus.description", fieldName = "运输任务状态")
    private OrderStatusEnum taskStatus;
    @ApiModelProperty("集装箱编号")
    private String containerNo;
    @ApiModelProperty("大宗订单编号")
    private String bulkOrderNo;
    @ApiModelProperty("提箱单状态")
    private ContainerStatusEnum containerStatus;
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehiclePlateNo;
    @ApiModelProperty("车辆code")
    private String vehicleTypeCode;
    @ApiModelProperty("车辆name")
    private String vehicleTypeName;
    @ApiModelProperty("挂车ID")
    private Long trailerId;
    @ApiModelProperty("挂车牌号")
    private String trailerPlateNo;
    @ApiModelProperty("挂车车辆code")
    private String trailerVehicleTypeCode;
    @ApiModelProperty("挂车车辆name")
    private String trailerVehicleTypeName;
    @ApiModelProperty("司机ID")
    private Long driverId;
    @ApiModelProperty("司机名称")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverMobile;
    @ApiModelProperty("承运商ID")
    private Long carrierId;
    @ApiModelProperty("承运商名称")
    private String carrierName;
    @ApiModelProperty("站台ID")
    private Long locationId;
    @ApiModelProperty("站台简称")
    private String locationSubName;
    @ApiModelProperty("站台简全称")
    private String locationFullName;
    @ApiModelProperty("站台地址")
    private String locationAddress;
    @ApiModelProperty("磅单主键")
    private Long poundId;
    @ApiModelProperty("净重")
    private BigDecimal netWeight;
    @ApiModelProperty("毛重")
    private BigDecimal roughWeight;
    @ApiModelProperty("皮重")
    private BigDecimal tareWeight;
    @ApiModelProperty("磅单来源")
    private PoundPicSourceEnum poundSource;
    @ApiModelProperty("抢单时间")
    @JsonFormat(
        pattern = "yyyy-MM-dd HH:mm:ss",
        timezone = "GMT+8"
    )
    private LocalDateTime grabOrderTime;
    @ApiModelProperty("过磅时间")
    @JsonFormat(
        pattern = "yyyy-MM-dd HH:mm:ss",
        timezone = "GMT+8"
    )
    private LocalDateTime poundTime;


    @ApiModelProperty(value = "计费单位, 1:TON-吨, 2:SIDE-方, 3:UNIT-件, 4:TRUCK-车")
    private ChargeUnitEnum chargeUnit;

    private StateNode stateNode;
    private List<EventLog> eventLogList;
    private List<TransferLog> transferLogList;
    private StateFlowNodeDTO stateFlowNodeDTO;
}
