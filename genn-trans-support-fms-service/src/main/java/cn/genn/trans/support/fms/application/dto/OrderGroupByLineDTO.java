package cn.genn.trans.support.fms.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 线路分组统计统计报表
 */
@Data
public class OrderGroupByLineDTO {

    @ApiModelProperty(value = "线路id")
    private Long lineId;

    @ApiModelProperty(value = "装货地简称")
    private String startAddress;

    @ApiModelProperty(value = "卸货地简称")
    private String destAddress;

    @ApiModelProperty(value = "计费单位")
    private String chargeUnit;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "卸车数")
    private Long unloadDriveSum;

    @ApiModelProperty(value = "卸车净重")
    private String unloadNetWeight;

    @ApiModelProperty(value = "卸车体积")
    private String unloadVolume;

    @ApiModelProperty(value = "卸车体积")
    private String agent;

}
