package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.core.order.interfaces.enums.contract.ChargeUnitEnum;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "磅单审核")
public class PoundReviewPageDTO {

    @ApiModelProperty(value = "货物取值位数")
    private String digits;

    @ApiModelProperty(value = "计费单位")
    private ChargeUnitEnum chargeUnit;

    @ApiModelProperty(value = "业务类型, 1:PURCHASE-采购运输, 2:SALES-销售运输, 3:ALLOT-厂间调拨, 4:EXTERNAL-外部运输）")
    private OrderBusinessTypeEnum businessType;

    @ApiModelProperty(value = "运单号")
    @DataBeanPath(fieldName = "运单号", beanPath = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "承运订单编号")
    @DataBeanPath(fieldName = "承运订单号", beanPath = "carrierOrderNo")
    private String carrierOrderNo;

    @ApiModelProperty(value = "业务类型名称")
    @DataBeanPath(fieldName = "业务类型", beanPath = "businessTypeName")
    private String businessTypeName;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;

    @ApiModelProperty(value = "货物编码")
    @DataBeanPath(fieldName = "货物编码", beanPath = "cargoNo")
    private String cargoNo;

    @ApiModelProperty(value = "装车净重 单位:吨")
    @DataBeanPath(fieldName = "装车净重(吨)", beanPath = "loadingNetWeight")
    private BigDecimal loadingNetWeight;

    @ApiModelProperty(value = "装车毛重 单位:吨")
    @DataBeanPath(fieldName = "装车毛重(吨)", beanPath = "loadingGrossWeight")
    private BigDecimal loadingGrossWeight;

    @ApiModelProperty(value = "装车皮重 单位:吨")
    @DataBeanPath(fieldName = "装车皮重(吨)", beanPath = "loadingTareWeight")
    private BigDecimal loadingTareWeight;

    @ApiModelProperty(value = "装车体积 单位:方")
    @DataBeanPath(fieldName = "装车体积(方)", beanPath = "loadingVolume")
    private BigDecimal loadingVolume;

    @ApiModelProperty(value = "卸车净重 单位:吨")
    @DataBeanPath(fieldName = "卸车净重(吨)", beanPath = "dischargeNetWeight")
    private BigDecimal dischargeNetWeight;

    @ApiModelProperty(value = "卸车毛重 单位:吨")
    @DataBeanPath(fieldName = "卸车毛重(吨)", beanPath = "dischargeGrossWeight")
    private BigDecimal dischargeGrossWeight;

    @ApiModelProperty(value = "卸车皮重 单位:吨")
    @DataBeanPath(fieldName = "卸车皮重(吨)", beanPath = "dischargeTareWeight")
    private BigDecimal dischargeTareWeight;

    @ApiModelProperty(value = "卸车体积 单位:方")
    @DataBeanPath(fieldName = "卸车体积(方)", beanPath = "dischargeVolume")
    private BigDecimal dischargeVolume;

    @ApiModelProperty(value = "卸车扣罚重量 单位:吨")
    @DataBeanPath(fieldName = "卸车扣罚(吨)", beanPath = "dischargeDeductWeight")
    private BigDecimal dischargeDeductWeight;

    @ApiModelProperty(value = "装车二次过磅时间")
    @DataBeanPath(fieldName = "装车时间", beanPath = "loadingTwoWeightTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime loadingTwoWeightTime;

    @ApiModelProperty(value = "卸车二次过磅时间")
    @DataBeanPath(fieldName = "卸车时间", beanPath = "dischargeTwoWeightTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime dischargeTwoWeightTime;

    @ApiModelProperty(value = "装车地址")
    @DataBeanPath(fieldName = "装车简称", beanPath = "loadsAddrShort")
    private String loadsAddrShort;

    @ApiModelProperty(value = "装车详细地址")
    @DataBeanPath(fieldName = "装车详细地址", beanPath = "loadsAddr")
    private String loadsAddr;

    @ApiModelProperty(value = "卸车地址")
    @DataBeanPath(fieldName = "卸车简称", beanPath = "unloadAddrShort")
    private String unloadAddrShort;

    @ApiModelProperty(value = "卸车详细地址")
    @DataBeanPath(fieldName = "卸车详细地址", beanPath = "unloadAddr")
    private String unloadAddr;

    @ApiModelProperty(value = "司机姓名")
    @DataBeanPath(fieldName = "司机姓名", beanPath = "driverName")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverPhone")
    private String driverPhone;

    @ApiModelProperty(value = "车牌号")
    @DataBeanPath(fieldName = "车牌号", beanPath = "plateNumber")
    private String plateNumber;

    @ApiModelProperty(value = "挂车车牌号")
    @DataBeanPath(fieldName = "挂车牌号", beanPath = "trailerPlateNumber")
    private String trailerPlateNumber;

    @ApiModelProperty(value = "装车审核状态名称")
    @DataBeanPath(fieldName = "装车审核状态", beanPath = "loadingPicStatusName")
    private String loadingPicStatusName;

    @ApiModelProperty(value = "装车磅单备注")
    @DataBeanPath(fieldName = "装车驳回原因", beanPath = "loadingPicRemark")
    private String loadingPicRemark;

    @ApiModelProperty(value = "装车审核用户名")
    @DataBeanPath(fieldName = "装车审核人", beanPath = "loadingPicUserName")
    private String loadingPicUserName;

    @ApiModelProperty(value = "装车审核时间")
    @DataBeanPath(fieldName = "装车审核时间", beanPath = "loadingPicTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime loadingPicTime;

    @ApiModelProperty(value = "卸车车审核状态名称")
    @DataBeanPath(fieldName = "卸车审核状态", beanPath = "dischargePicStatusName")
    private String dischargePicStatusName;

    @ApiModelProperty(value = "卸车磅单备注")
    @DataBeanPath(fieldName = "卸车驳回原因", beanPath = "dischargePicRemark")
    private String dischargePicRemark;

    @ApiModelProperty(value = "卸车审核用户名")
    @DataBeanPath(fieldName = "卸车审核人", beanPath = "dischargePicUserName")
    private String dischargePicUserName;

    @ApiModelProperty(value = "卸车审核时间")
    @DataBeanPath(fieldName = "卸车审核时间", beanPath = "dischargePicTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime dischargePicTime;
}
