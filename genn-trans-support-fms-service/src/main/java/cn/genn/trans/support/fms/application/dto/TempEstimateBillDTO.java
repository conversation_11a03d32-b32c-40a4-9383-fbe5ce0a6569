package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.core.sif.interfaces.enums.ChargeTypeEnum;
import cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum;
import cn.genn.trans.core.sif.interfaces.enums.TempEstimateBillStatusEnum;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 暂估单对象
 * @date 2024-11-05
 */
@Data
public class TempEstimateBillDTO {

    @DataBeanPath(fieldName = "应收暂估单号", beanPath = "billNo")
    private String billNo;

    @DataBeanPath(fieldName = "客户名称", beanPath = "payName")
    private String payName;

    @DataBeanPath(fieldName = "结算主体", beanPath = "payeeName")
    private String payeeName;

    @DataBeanPath(fieldName = "承运商", beanPath = "relName")
    private String relName;

    @DataBeanPath(fieldName = "装车地址简称", beanPath = "loadingSubName")
    private String loadingSubName;

    @DataBeanPath(fieldName = "卸车地址简称", beanPath = "unloadingSubName")
    private String unloadingSubName;

    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;

    @DataBeanPath(fieldName = "货物规格", beanPath = "cargoSpec")
    private String cargoSpec;

    @DataBeanPath(fieldName = "暂估单状态", beanPath = "billStatus.description")
    private TempEstimateBillStatusEnum billStatus;

    @DataBeanPath(fieldName = "含税运价", beanPath = "taxPrice")
    private BigDecimal taxPrice;

    @DataBeanPath(fieldName = "计费方式", beanPath = "chargeType.description")
    private ChargeTypeEnum chargeType;

    @DataBeanPath(fieldName = "运单数", beanPath = "orderCount")
    private Long orderCount;

    @DataBeanPath(fieldName = "装卸单位", beanPath = "chargeUnit.description")
    private ChargeUnitEnum chargeUnit;

    @DataBeanPath(fieldName = "装货量", beanPath = "loadingValue")
    private BigDecimal loadingValue;

    @DataBeanPath(fieldName = "卸货量", beanPath = "unloadingValue")
    private BigDecimal unloadingValue;

    @DataBeanPath(fieldName = "实际计费数量", beanPath = "chargeCount")
    private BigDecimal chargeCount;

    @DataBeanPath(fieldName = "含税金额(元)", beanPath = "taxTotalPrice")
    private BigDecimal taxTotalPrice;

    @DataBeanPath(fieldName = "不含税金额(元)", beanPath = "taxFreePrice")
    private BigDecimal taxFreePrice;

    @DataBeanPath(fieldName = "税额(元)", beanPath = "taxAmount")
    private BigDecimal taxAmount;

    @DataBeanPath(fieldName = "基础运费(元)", beanPath = "basePrice")
    private BigDecimal basePrice;

    @DataBeanPath(fieldName = "货损赔偿(元)", beanPath = "lossPrice")
    private BigDecimal lossPrice;

    @DataBeanPath(fieldName = "抹零金额(元)", beanPath = "roundPrice")
    private BigDecimal roundPrice;

    @DataBeanPath(fieldName = "暂估含税金额(元)", beanPath = "estimatedTaxTotalAmount")
    private BigDecimal estimatedTaxTotalAmount;

    @DataBeanPath(fieldName = "暂估不含税金额(元)", beanPath = "estimatedTaxFreeAmount")
    private BigDecimal estimatedTaxFreeAmount;

    @DataBeanPath(fieldName = "暂估税额(元)", beanPath = "estimatedTaxAmount")
    private BigDecimal estimatedTaxAmount;

    @DataBeanPath(fieldName = "暂估周期开始时间", beanPath = "beginTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime beginTime;

    @DataBeanPath(fieldName = "暂估周期结束时间", beanPath = "endTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime endTime;

    @DataBeanPath(fieldName = "创建时间", beanPath = "createTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime createTime;

    @DataBeanPath(fieldName = "审核时间", beanPath = "auditTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime auditTime;

    @DataBeanPath(fieldName = "审核人", beanPath = "auditUserName")
    private String auditUserName;

    @DataBeanPath(fieldName = "审核意见", beanPath = "auditRemark")
    private String auditRemark;

}
