package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.core.sif.interfaces.enums.ChargeTypeEnum;
import cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum;
import cn.genn.trans.core.sif.interfaces.enums.SyncStatusEnum;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 暂估红冲单对象
 * <AUTHOR>
 */
@Data
public class TempEstimateRedBillDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @DataBeanPath(fieldName = "应收暂估红冲单号", beanPath = "redBillNo")
    private String redBillNo;

    @DataBeanPath(fieldName = "客户名称", beanPath = "payName")
    private String payName;

    @DataBeanPath(fieldName = "结算主体", beanPath = "payeeName")
    private String payeeName;

    @DataBeanPath(fieldName = "承运商", beanPath = "relName")
    private String relName;

    @DataBeanPath(fieldName = "装车地址简称", beanPath = "loadingSubName")
    private String loadingSubName;

    @DataBeanPath(fieldName = "卸车地址简称", beanPath = "unloadingSubName")
    private String unloadingSubName;

    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;

    @DataBeanPath(fieldName = "货物规格", beanPath = "cargoSpec")
    private String cargoSpec;

    @DataBeanPath(fieldName = "含税运价", beanPath = "taxPrice")
    private BigDecimal taxPrice;

    @DataBeanPath(fieldName = "计费方式", beanPath = "chargeType.description")
    private ChargeTypeEnum chargeType;

    @DataBeanPath(fieldName = "运单数(单)", beanPath = "orderCount")
    private Long orderCount;

    @DataBeanPath(fieldName = "装卸单位", beanPath = "chargeUnit.description")
    private ChargeUnitEnum chargeUnit;

    @DataBeanPath(fieldName = "装货量", beanPath = "loadingValue")
    private BigDecimal loadingValue;

    @DataBeanPath(fieldName = "卸货量", beanPath = "unloadingValue")
    private BigDecimal unloadingValue;

    @DataBeanPath(fieldName = "含税金额(元)", beanPath = "taxTotalPrice")
    private BigDecimal taxTotalPrice;

    @DataBeanPath(fieldName = "不含税金额(元)", beanPath = "taxFreePrice")
    private BigDecimal taxFreePrice;

    @DataBeanPath(fieldName = "税额(元)", beanPath = "taxAmount")
    private BigDecimal taxAmount;

    @DataBeanPath(fieldName = "基础运费(元)", beanPath = "basePrice")
    private BigDecimal basePrice;

    @DataBeanPath(fieldName = "货损赔偿(元)", beanPath = "lossPrice")
    private BigDecimal lossPrice;

    @DataBeanPath(fieldName = "抹零金额(元)", beanPath = "roundPrice")
    private BigDecimal roundPrice;

    @DataBeanPath(fieldName = "暂估含税金额(元)", beanPath = "estimatedTaxTotalAmount")
    private BigDecimal estimatedTaxTotalAmount;

    @DataBeanPath(fieldName = "暂估不含税金额(元)", beanPath = "estimatedTaxFreeAmount")
    private BigDecimal estimatedTaxFreeAmount;

    @DataBeanPath(fieldName = "暂估税额(元)", beanPath = "estimatedTaxAmount")
    private BigDecimal estimatedTaxAmount;

    @DataBeanPath(fieldName = "暂估红冲周期开始时间", beanPath = "beginTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;

    @DataBeanPath(fieldName = "暂估红冲周期结束时间", beanPath = "endTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @DataBeanPath(fieldName = "暂估红冲创建时间", beanPath = "createTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @DataBeanPath(fieldName = "创建人", beanPath = "createUserName")
    private String createUserName;

    @DataBeanPath(fieldName = "nc单据号", beanPath = "ncBillNo")
    private String ncBillNo;

    @DataBeanPath(fieldName = "数据同步状态", beanPath = "syncStatus.description")
    private SyncStatusEnum syncStatus;

}
