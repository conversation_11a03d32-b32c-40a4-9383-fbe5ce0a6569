package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 随车费用记录数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "随车费用记录数据")
public class VehicleExpensesRecordDTO {

    @ApiModelProperty(value = "司机名称")
    @DataBeanPath(fieldName = "司机姓名", beanPath = "driverName")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverMobile")
    private String driverMobile;

    @ApiModelProperty(value = "车牌号")
    @DataBeanPath(fieldName = "车牌号", beanPath = "vehiclePlateNo")
    private String vehiclePlateNo;

    @ApiModelProperty(value = "挂车车牌号")
    @DataBeanPath(fieldName = "挂车车牌号", beanPath = "trailerPlateNo")
    private String trailerPlateNo;

    @ApiModelProperty(value = "车辆类型名称")
    @DataBeanPath(fieldName = "车辆类型", beanPath = "vehicleTypeName")
    private String vehicleTypeName;

    @ApiModelProperty(value = "运单号")
    @DataBeanPath(fieldName = "运单号", beanPath = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "业务类型")
    @DataBeanPath(fieldName = "业务类型", beanPath = "businessTypeDesc")
    private String businessTypeDesc;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    @DataBeanPath(fieldName = "货物规格", beanPath = "cargoSpec")
    private String cargoSpec;

    @ApiModelProperty(value = "装车地址简称")
    @DataBeanPath(fieldName = "装车简称", beanPath = "loadingSubName")
    private String loadingSubName;

    @ApiModelProperty(value = "卸车地址简称")
    @DataBeanPath(fieldName = "卸车简称", beanPath = "unloadingSubName")
    private String unloadingSubName;

    @ApiModelProperty(value = "装车地址")
    @DataBeanPath(fieldName = "装车地址", beanPath = "loadingAddress")
    private String loadingAddress;

    @ApiModelProperty(value = "卸车地址")
    @DataBeanPath(fieldName = "卸车地址", beanPath = "unloadingAddress")
    private String unloadingAddress;

    @ApiModelProperty(value = "总费用合计")
    @DataBeanPath(fieldName = "随车费用合计（元）", beanPath = "totalCost")
    private BigDecimal totalCost;

    @ApiModelProperty(value = "装卸费")
    @DataBeanPath(fieldName = "装卸费用合计（元）", beanPath = "loadingUnloadingFee")
    private BigDecimal loadingUnloadingFee;

    @ApiModelProperty(value = "装车费")
    @DataBeanPath(fieldName = "装车费（元）", beanPath = "loadingFee")
    private BigDecimal loadingFee;

    @ApiModelProperty(value = "卸车费")
    @DataBeanPath(fieldName = "卸车费（元）", beanPath = "unloadingFee")
    private BigDecimal unloadingFee;

    @ApiModelProperty("办卡费")
    @DataBeanPath(fieldName = "其他费-办卡费(元)", beanPath = "cardFee")
    private BigDecimal cardFee;

    @ApiModelProperty("停车费")
    @DataBeanPath(fieldName = "其他费-停车费(元)", beanPath = "parkingFee")
    private BigDecimal parkingFee;

    @ApiModelProperty("标签费")
    @DataBeanPath(fieldName = "其他费-标签费(元)", beanPath = "labelFee")
    private BigDecimal labelFee;

    @ApiModelProperty("其他费")
    @DataBeanPath(fieldName = "其他费-其他(元)", beanPath = "otherFee")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "过路费")
    @DataBeanPath(fieldName = "过路费(元)", beanPath = "tollFee")
    private BigDecimal tollFee;

    @ApiModelProperty("装车数量")
    @DataBeanPath(fieldName = "装车数量", beanPath = "loadingQuantity")
    private String loadingQuantity;

    @ApiModelProperty("卸车数量")
    @DataBeanPath(fieldName = "卸车数量", beanPath = "unloadingQuantity")
    private String unloadingQuantity;

    @ApiModelProperty(value = "装车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "装车时间", beanPath = "loadingTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime loadingTime;

    @ApiModelProperty(value = "卸车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "卸车时间", beanPath = "unloadingTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime unloadingTime;

    @ApiModelProperty(value = "签收时间")
    @DataBeanPath(fieldName = "签收时间", beanPath = "signTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "创建时间")
    @DataBeanPath(fieldName = "提交时间", beanPath = "createTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "审核人名称")
    @DataBeanPath(fieldName = "审核人", beanPath = "auditUserName")
    private String auditUserName;

    @ApiModelProperty(value = "审核时间")
    @DataBeanPath(fieldName = "审核时间", beanPath = "auditTime", wrapperClass = DateTimeWrapper.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;
}
