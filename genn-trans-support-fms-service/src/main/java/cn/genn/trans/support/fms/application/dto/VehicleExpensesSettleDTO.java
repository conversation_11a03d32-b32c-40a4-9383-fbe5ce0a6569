package cn.genn.trans.support.fms.application.dto;

import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 随车费用结算单对象
 * @date 2024-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "随车费用结算数据")
public class VehicleExpensesSettleDTO {

    @ApiModelProperty(value = "结算单号")
    @DataBeanPath(fieldName = "随车费结算单号", beanPath = "settleNo")
    private String settleNo;

    @ApiModelProperty(value = "司机姓名")
    @DataBeanPath(fieldName = "司机姓名", beanPath = "driverName")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverMobile")
    private String driverMobile;

    @ApiModelProperty(value = "开户名")
    @DataBeanPath(fieldName = "开户名", beanPath = "bankName")
    private String bankName;

    @ApiModelProperty(value = "开户行")
    @DataBeanPath(fieldName = "开户行", beanPath = "bankBranchName")
    private String bankBranchName;

    @ApiModelProperty(value = "银行卡号")
    @DataBeanPath(fieldName = "银行卡号", beanPath = "bankCardNo")
    private String bankCardNo;

    @ApiModelProperty(value = "运单数")
    @DataBeanPath(fieldName = "运单数(单)", beanPath = "orderCount")
    private Integer orderCount;

    @ApiModelProperty(value = "装车费")
    @DataBeanPath(fieldName = "装车费(元)", beanPath = "loadingFee")
    private BigDecimal loadingFee;

    @ApiModelProperty(value = "卸车费")
    @DataBeanPath(fieldName = "卸车费(元)", beanPath = "unloadingFee")
    private BigDecimal unloadingFee;

    @ApiModelProperty(value = "其他费")
    @DataBeanPath(fieldName = "其他费(元)", beanPath = "otherFee")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "过路费")
    @DataBeanPath(fieldName = "过路费(元)", beanPath = "tollFee")
    private BigDecimal tollFee;

    @ApiModelProperty(value = "随车费汇总时间")
    @DataBeanPath(fieldName = "随车费汇总时间", beanPath = "createTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "随车费汇总人")
    @DataBeanPath(fieldName = "随车费汇总人", beanPath = "createUserName")
    private String createUserName;

    @ApiModelProperty(value = "结算时间")
    @DataBeanPath(fieldName = "结算时间", beanPath = "settleTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime settleTime;

}
