package cn.genn.trans.support.fms.application.dto.bill;

import cn.genn.trans.support.fms.infrastructure.converter.Date2Wrapper;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应收对账单明细页数据
 *
 * <AUTHOR>
 * @description 对账单明细页数据
 * @since 2024/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(description = "对账单明细页数据")
public class PayeeBillDetailViewPageDTO {

    @ApiModelProperty(value = "运单号")
    @DataBeanPath(fieldName = "运单号", beanPath = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "起始地")
    @DataBeanPath(fieldName = "起始地", beanPath = "loadingAddress")
    private String loadingAddress;

    @ApiModelProperty(value = "到达地")
    @DataBeanPath(fieldName = "到达地", beanPath = "unloadingAddress")
    private String unloadingAddress;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    @DataBeanPath(fieldName = "货物规格", beanPath = "cargoSpec")
    private String cargoSpec;

    @ApiModelProperty(value = "车牌号")
    @DataBeanPath(fieldName = "车牌号", beanPath = "plateNumber")
    private String plateNumber;

    @ApiModelProperty(value = "司机")
    @DataBeanPath(fieldName = "司机", beanPath = "driverName")
    private String driverName;

    @ApiModelProperty(value = "原发数")
    @DataBeanPath(fieldName = "原发数", beanPath = "originQuantity")
    private BigDecimal originQuantity;

    @ApiModelProperty(value = "卸货数")
    @DataBeanPath(fieldName = "卸货数", beanPath = "actualQuantity")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "结算数")
    @DataBeanPath(fieldName = "结算数", beanPath = "chargeCount")
    private BigDecimal chargeCount;

    @ApiModelProperty(value = "单价")
    @DataBeanPath(fieldName = "网货运价/含税运价", beanPath = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "亏吨")
    @DataBeanPath(fieldName = "亏吨", beanPath = "chargeLossValue")
    private BigDecimal chargeLossValue;

    @ApiModelProperty(value = "货物价格")
    @DataBeanPath(fieldName = "货物价格", beanPath = "cargoPrice")
    private BigDecimal cargoPrice;

    @ApiModelProperty(value = "基础运费")
    @DataBeanPath(fieldName = "基础运费", beanPath = "basePrice")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "货损扣款")
    @DataBeanPath(fieldName = "货损扣款", beanPath = "lossPrice")
    private BigDecimal lossPrice;

    @ApiModelProperty(value = "抹零金额")
    @DataBeanPath(fieldName = "抹零金额", beanPath = "roundPrice")
    private BigDecimal roundPrice;

    @ApiModelProperty(value = "服务费")
    @DataBeanPath(fieldName = "服务费", beanPath = "serviceFee")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "税率")
    @DataBeanPath(fieldName = "税率", beanPath = "taxRate")
    private String taxRate;

    @ApiModelProperty(value = "含税金额")
    @DataBeanPath(fieldName = "含税金额", beanPath = "totalAmount")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "不含税金额")
    @DataBeanPath(fieldName = "不含税金额", beanPath = "noTaxAmount")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "垫付金额")
    @DataBeanPath(fieldName = "垫付金额", beanPath = "preFee")
    private BigDecimal preFee;

    @ApiModelProperty(value = "装车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "装车时间", beanPath = "loadingTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime loadingTime;

    @ApiModelProperty(value = "卸车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "卸车时间", beanPath = "dischargeTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime dischargeTime;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "签收时间", beanPath = "signTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime signTime;

}
