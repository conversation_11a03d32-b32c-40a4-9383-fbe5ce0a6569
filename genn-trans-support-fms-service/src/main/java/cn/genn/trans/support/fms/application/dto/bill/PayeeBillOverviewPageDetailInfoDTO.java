package cn.genn.trans.support.fms.application.dto.bill;

import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 应收对账单总览页明细数据
 *
 * <AUTHOR>
 * @description 应付对账单总览页明细数据
 * @since 2024/8/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class PayeeBillOverviewPageDetailInfoDTO {

    @ApiModelProperty(value = "起始地")
    @DataBeanPath(fieldName = "loadingAddress", beanPath = "loadingAddress")
    private String loadingAddress;

    @ApiModelProperty(value = "到达地")
    @DataBeanPath(fieldName = "unloadingAddress", beanPath = "unloadingAddress")
    private String unloadingAddress;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "cargoName", beanPath = "cargoName")
    private String cargoName;

    @ApiModelProperty(value = "车数")
    @DataBeanPath(fieldName = "vehicleCounts", beanPath = "vehicleCounts")
    private Integer vehicleCounts;

    @ApiModelProperty(value = "原发数")
    @DataBeanPath(fieldName = "originQuantity", beanPath = "originQuantity")
    private BigDecimal originQuantity;

    @ApiModelProperty(value = "卸货数")
    @DataBeanPath(fieldName = "actualQuantity", beanPath = "actualQuantity")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "结算数")
    @DataBeanPath(fieldName = "chargeCount", beanPath = "chargeCount")
    private BigDecimal chargeCount;

    @ApiModelProperty(value = "含税单价")
    @DataBeanPath(fieldName = "taxUnitPrice", beanPath = "taxUnitPrice")
    private BigDecimal taxUnitPrice;

    @ApiModelProperty(value = "不含税金额（元）")
    @DataBeanPath(fieldName = "noTaxAmount", beanPath = "noTaxAmount")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税额（元）")
    @DataBeanPath(fieldName = "taxQuotaAmount", beanPath = "taxQuotaAmount")
    private BigDecimal taxQuotaAmount;

    @ApiModelProperty(value = "价税合计金额（元）")
    @DataBeanPath(fieldName = "totalAmount", beanPath = "totalAmount")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "垫付金额")
    @DataBeanPath(fieldName = "preFee", beanPath = "preFee")
    private BigDecimal preFee;
}
