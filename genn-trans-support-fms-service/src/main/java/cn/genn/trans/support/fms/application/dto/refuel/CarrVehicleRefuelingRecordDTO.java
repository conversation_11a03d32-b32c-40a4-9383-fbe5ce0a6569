package cn.genn.trans.support.fms.application.dto.refuel;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.support.fms.interfaces.enums.ApproveStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * CarrVehicleRefuelingRecordDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarrVehicleRefuelingRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "补能单主键不能为空")
    private Long id;

    private Long tenantId;

    private Long operatorId;

    private Long carrierId;

    @ApiModelProperty(value = "承运商名称")
    private String carrierName;

    @ApiModelProperty(value = "车辆id")
    private Long vehicleId;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "司机id")
    private Long driverId;

    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @ApiModelProperty(value = "补能类型")
    private String refuelType;

    @ApiModelProperty(value = "补能站点id")
    private Long refuelStationId;

    @ApiModelProperty(value = "补能站点")
    private String refuelStationName;

    @ApiModelProperty(value = "补能量")
    private String refuelQuantity;

    @ApiModelProperty(value = "单位")
    private String refuelUnit;

    @ApiModelProperty(value = "单价(元)")
    private BigDecimal refuelUnitPrice;

    @ApiModelProperty(value = "支付费用")
    private BigDecimal refuelCost;

    @ApiModelProperty(value = "里程读表数")
    private BigDecimal refuelMileage;

    @ApiModelProperty(value = "补能时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refuelTime;

    @ApiModelProperty(value = "凭证文件")
    private String credentialFile;

    @ApiModelProperty(value = "审核状态, 0:APPROVE-审核中, 1:PASS-通过, 2:REJECT-驳回")
    private ApproveStatusEnum refuelStatus;

    @ApiModelProperty(value = "审核意见")
    private String remark;

    @ApiModelProperty(value = "车辆燃料类型")
    private String vehicleFuelType;

    private DeletedEnum deleted;


}

