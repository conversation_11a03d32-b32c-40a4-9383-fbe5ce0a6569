package cn.genn.trans.support.fms.application.dto.settle;

import cn.genn.trans.support.fms.infrastructure.converter.Date2Wrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应付结算单总览页导出
 *
 * <AUTHOR>
 * @description 对账单总览页数据
 * @since 2024/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class PaySettleBillOverViewPageDTO {

    @ApiModelProperty(value = "结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "settleDate", beanPath = "settleDate", wrapperClass = Date2Wrapper.class)
    private LocalDateTime settleDate;

    @ApiModelProperty(value = "结算单位")
    @DataBeanPath(fieldName = "settleCompanyName", beanPath = "settleCompanyName")
    private String settleCompanyName;

    @ApiModelProperty(value = "运价列名")
    @DataBeanPath(fieldName = "priceColumnName", beanPath = "priceColumnName")
    private String priceColumnName;

    @ApiModelProperty(value = "结算方印章")
    private String settleSeal;

    @ApiModelProperty(value = "结算单号")
    @DataBeanPath(fieldName = "settleNo", beanPath = "settleNo")
    private String settleNo;

    @ApiModelProperty(value = "承运单位")
    @DataBeanPath(fieldName = "carrierCompanyName", beanPath = "carrierCompanyName")
    private String carrierCompanyName;

    @ApiModelProperty(value = "承运方印章")
    private String carrierSeal;

    @ApiModelProperty(value = "托运单位")
    @DataBeanPath(fieldName = "entrustCompanyName", beanPath = "entrustCompanyName")
    private String entrustCompanyName;

    @ApiModelProperty(value = "发货单位")
    @DataBeanPath(fieldName = "shippingCompanyName", beanPath = "shippingCompanyName")
    private String shippingCompanyName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "settleBeginTime", beanPath = "settleBeginTime", wrapperClass = Date2Wrapper.class)
    private LocalDateTime settleBeginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "settleEndTime", beanPath = "settleEndTime", wrapperClass = Date2Wrapper.class)
    private LocalDateTime settleEndTime;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "cargoName", beanPath = "cargoName")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    @DataBeanPath(fieldName = "cargoSpec", beanPath = "cargoSpec")
    private String cargoSpec;

    @ApiModelProperty(value = "车数合计")
    @DataBeanPath(fieldName = "vehicleCounts", beanPath = "vehicleCounts")
    private Integer vehicleCounts;

    @ApiModelProperty(value = "原发数量")
    @DataBeanPath(fieldName = "originQuantity", beanPath = "originQuantity")
    private BigDecimal originQuantity;

    @ApiModelProperty(value = "实收数量")
    @DataBeanPath(fieldName = "actualQuantity", beanPath = "actualQuantity")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "结算数量")
    @DataBeanPath(fieldName = "settleQuantity", beanPath = "settleQuantity")
    private BigDecimal settleQuantity;

    @ApiModelProperty(value = "结算单价")
    @DataBeanPath(fieldName = "settleUnitPrice", beanPath = "settleUnitPrice")
    private BigDecimal settleUnitPrice;

    @ApiModelProperty(value = "结算金额")
    @DataBeanPath(fieldName = "settleAmount", beanPath = "settleAmount")
    private BigDecimal settleAmount;

    @ApiModelProperty(value = "税率")
    @DataBeanPath(fieldName = "taxRate", beanPath = "taxRate")
    private String taxRate;

    @ApiModelProperty(value = "不含税金额")
    @DataBeanPath(fieldName = "noTaxAmount", beanPath = "noTaxAmount")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税额")
    @DataBeanPath(fieldName = "taxAmount", beanPath = "taxAmount")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "结算金额大写")
    @DataBeanPath(fieldName = "settleAmountUpperCase", beanPath = "settleAmountUpperCase")
    private String settleAmountUpperCase;

    @ApiModelProperty(value = "结算说明 亏吨量 元")
    @DataBeanPath(fieldName = "lossPrice", beanPath = "lossPrice")
    private BigDecimal lossPrice;

    @ApiModelProperty(value = "结算说明 亏吨量 吨")
    @DataBeanPath(fieldName = "lossValue", beanPath = "lossValue")
    private BigDecimal lossValue;

    @ApiModelProperty(value = "制单人")
    @DataBeanPath(fieldName = "createPersonName", beanPath = "createPersonName")
    private String createPersonName;

    @ApiModelProperty(value = "审核人")
    @DataBeanPath(fieldName = "auditPersonName", beanPath = "auditPersonName")
    private String auditPersonName;
}
