package cn.genn.trans.support.fms.application.dto.settle;

import cn.genn.trans.support.fms.infrastructure.converter.Date3Wrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 应收结算单导出总览页数据
 *
 * <AUTHOR>
 * @description 应收对账单总览页
 * @since 2024/8/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class PayeeSettleBillOverViewPageDTO {

    @ApiModelProperty(value = "表头公司名称")
    @DataBeanPath(fieldName = "tableHeaderCompanyName", beanPath = "tableHeaderCompanyName")
    private String tableHeaderCompanyName;

    @ApiModelProperty(value = "单位名称")
    @DataBeanPath(fieldName = "companyName", beanPath = "companyName")
    private String companyName;

    @ApiModelProperty(value = "运价列名")
    @DataBeanPath(fieldName = "priceColumnName", beanPath = "priceColumnName")
    private String priceColumnName;

    @ApiModelProperty(value = "制表时间 yyyy.MM.dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "createTime", beanPath = "createTime", wrapperClass = Date3Wrapper.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结算开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "settleBeginTime", beanPath = "settleBeginTime", wrapperClass = Date3Wrapper.class)
    private LocalDateTime settleBeginTime;

    @ApiModelProperty(value = "结算结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "settleEndTime", beanPath = "settleEndTime", wrapperClass = Date3Wrapper.class)
    private LocalDateTime settleEndTime;


    @ApiModelProperty(value = "单位")
    @DataBeanPath(fieldName = "unit", beanPath = "unit")
    private String unit;

    @ApiModelProperty(value = "委托方名称")
    @DataBeanPath(fieldName = "entrustName", beanPath = "entrustName")
    private String entrustName;

    @ApiModelProperty(value = "委托方印章")
    private String entrustSeal;

    @ApiModelProperty(value = "制单人")
    @DataBeanPath(fieldName = "createTablePersonName", beanPath = "createTablePersonName")
    private String createTablePersonName;

    @ApiModelProperty(value = "承运方名称")
    @DataBeanPath(fieldName = "carrierName", beanPath = "carrierName")
    private String carrierName;

    @ApiModelProperty(value = "承运方印章")
    private String carrierSeal;

    @ApiModelProperty(value = "总明细车辆数量")
    @DataBeanPath(fieldName = "totalDetailVehicleCounts", beanPath = "totalDetailVehicleCounts")
    private Integer totalDetailVehicleCounts;

    @ApiModelProperty(value = "总明细原始重量")
    @DataBeanPath(fieldName = "totalDetailOriginQuantity", beanPath = "totalDetailOriginQuantity")
    private BigDecimal totalDetailOriginQuantity;

    @ApiModelProperty(value = "总明细实际重量")
    @DataBeanPath(fieldName = "totalDetailActualQuantity", beanPath = "totalDetailActualQuantity")
    private BigDecimal totalDetailActualQuantity;

    @ApiModelProperty(value = "计费总数量")
    @DataBeanPath(fieldName = "totalChargeCount", beanPath = "totalChargeCount")
    private BigDecimal totalChargeCount;

    @ApiModelProperty(value = "总明细无税金额")
    @DataBeanPath(fieldName = "totalDetailNoTaxAmount", beanPath = "totalDetailNoTaxAmount")
    private BigDecimal totalDetailNoTaxAmount;

    @ApiModelProperty(value = "总明细税额")
    @DataBeanPath(fieldName = "totalDetailTaxQuotaAmount", beanPath = "totalDetailTaxQuotaAmount")
    private BigDecimal totalDetailTaxQuotaAmount;

    @ApiModelProperty(value = "总明细金额")
    @DataBeanPath(fieldName = "totalDetailAmount", beanPath = "totalDetailAmount")
    private BigDecimal totalDetailAmount;

    @ApiModelProperty(value = "结算说明 亏吨量 元")
    @DataBeanPath(fieldName = "lossPrice", beanPath = "lossPrice")
    private BigDecimal lossPrice;

    @ApiModelProperty(value = "结算说明 亏吨量 吨")
    @DataBeanPath(fieldName = "lossValue", beanPath = "lossValue")
    private BigDecimal lossValue;

    @ApiModelProperty(value = "垫付总金额")
    @DataBeanPath(fieldName = "totalPreFee", beanPath = "totalPreFee")
    private BigDecimal totalPreFee;

    @ApiModelProperty(value = "应收对账单详情信息列表")
    private List<PayeeSettleBillOverviewPageDetailInfoDTO> payeeSettleBillOverviewPageDetailInfoDTOList;
}
