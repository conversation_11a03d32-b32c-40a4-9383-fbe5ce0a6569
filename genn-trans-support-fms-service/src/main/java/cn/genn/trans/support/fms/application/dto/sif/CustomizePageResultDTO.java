package cn.genn.trans.support.fms.application.dto.sif;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * 分页结果类
 */
@Data
@NoArgsConstructor
public class CustomizePageResultDTO<T> implements Serializable {

    private static final long serialVersionUID = 4028133477241928793L;
    /**
     * 合计运单数
     */
    private Integer totalOrderCount;

    /**
     * 统计金额
     */
    private String totalAmount;

    /**
     * 自有司机
     */
    private String selfDriverAmount;

    /**
     * 外协司机
     */
    private String outsourceDriverAmount;

    /**
     * 查询时间
     */
    private LocalDateTime queryTime = LocalDateTime.now();

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 记录总条数
     */
    private Long total;

    /**
     * 当前页数据对象列表
     */
    private List<T> list;

    /**
     * @param pageNo   当前页码
     * @param pageSize 每页条数
     * @param total    记录总条数
     * @param list     当前页数据对象列表
     */
    public CustomizePageResultDTO(int pageNo, int pageSize, long total, List<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list == null ? new ArrayList<>() : list;
        if (this.pageSize > 0) {
            this.totalPages = (int) (total / this.pageSize + (total % this.pageSize == 0L ? 0 : 1));
        } else {
            this.totalPages = 0;
        }
    }

    public void foreach(Consumer<? super T> action) {
        Objects.requireNonNull(action);
        for (T t : this.getList()) {
            action.accept(t);
        }
    }

}
