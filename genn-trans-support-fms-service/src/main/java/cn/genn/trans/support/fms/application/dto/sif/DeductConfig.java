package cn.genn.trans.support.fms.application.dto.sif;

import cn.genn.trans.core.sif.interfaces.sifconfig.SettleConfig;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.interfaces.enums.sif.RoundTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class DeductConfig implements Serializable {


    /**
     * 自有司机提成
     */
    @DataBeanPath(fieldName = "自有司机提成", beanPath = "deductAmount")
    private BigDecimal deductAmount;

    /**
     * 外协司机费用
     */
    @DataBeanPath(fieldName = "外协司机运价(元/吨)", beanPath = "feeConfig")
    private BigDecimal feeConfig;
    /**
     * RoundTypeEnum 抹零规则
     */
    @DataBeanPath(fieldName = "外协司机抹零规则", beanPath = "roundType.description")
    private RoundTypeEnum roundType;

}
