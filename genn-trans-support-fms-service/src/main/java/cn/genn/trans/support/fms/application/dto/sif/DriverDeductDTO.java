package cn.genn.trans.support.fms.application.dto.sif;

import cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanObject;
import cn.genn.trans.support.fms.interfaces.enums.sif.*;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SifChargeBillDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverDeductDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "承运合同id")
    private Long contractId;
    @ApiModelProperty(value = "承运合同号")
    @DataBeanPath(fieldName = "承运合同编号", beanPath = "contractNo")
    private String contractNo;

    @ApiModelProperty(value = "承运线路id")
    private Long lineId;
    @ApiModelProperty(value = "承运线路货物价格")
    private Integer carrierCargoPrice;
//    @ApiModelProperty(value = "承运线路货物价格确定时间")
//    private CargoPriceSourceEnum cargoPriceSource;
    @ApiModelProperty(value = "承运线路含税运价")
    private Integer carrierTaxPrice;
    @ApiModelProperty(value = "货损值 承运线路")
    private BigDecimal carrierLossValue;
//    @ApiModelProperty(value = "货损类型 承运线路")
//    private LossTypeEnum carrierLossType;
    @ApiModelProperty(value = "抹零 承运线路")
    private RoundTypeEnum carrierRoundType;

    @ApiModelProperty(value = "线路提成配置LOG-ID")
    private Long configLogId;
    @ApiModelProperty(value = "配置内容")
    private String config;

    @ApiModelProperty(value = "配置内容")
    private Boolean hasConfig;

    @ApiModelProperty(value = "装车地址简称")
    @DataBeanPath(fieldName = "装车地址简称", beanPath = "loadingSubName")
    private String loadingSubName;
    @ApiModelProperty(value = "卸车地址简称")
    @DataBeanPath(fieldName = "卸车地址简称", beanPath = "unloadingSubName")
    private String unloadingSubName;

    @ApiModelProperty(value = "装车地址")
    private String loadingAddress;
    @ApiModelProperty(value = "卸车地址")
    private String unloadingAddress;

    @ApiModelProperty(value = "货物名称")
    @DataBeanPath(fieldName = "货物名称", beanPath = "cargoName")
    private String cargoName;
    @ApiModelProperty(value = "货物id")
    private Long cargoId;
    @ApiModelProperty(value = "货物编号")
    @DataBeanPath(fieldName = "货物编号", beanPath = "cargoNo")
    private String cargoNo;

    @ApiModelProperty(value = "货物规格")
    private String cargoSpec;

    @ApiModelProperty(value = "统计状态 0-待确认，1-待汇总，10-已确认，20-已汇总")
    @DataBeanPath(fieldName = "状态", beanPath = "statStatus.description")
    private StatStatusEnum statStatus;

    @ApiModelProperty(value = "计费单位, 1:TON-吨, 2:SIDE-方, 3:UNIT-件, 4:TRUCK-车")
    private ChargeUnitEnum carrierChargeUnit;

    @ApiModelProperty(value = "运单数")
    @DataBeanPath(fieldName = "运单数", beanPath = "orderTotalCount")
    private Integer orderTotalCount;
    @ApiModelProperty(value = "绩效总数")
    @DataBeanPath(fieldName = "绩效费用合计", beanPath = "totalFee")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "自有司机运单数")
    @DataBeanPath(fieldName = "自有司机运单数", beanPath = "selfDriverOrderCount")
    private Integer selfDriverOrderCount;

    @ApiModelProperty(value = "自有司机绩效总额（司机提成）")
    @DataBeanPath(fieldName = "自有司机绩效总额", beanPath = "selfDriverTotalFee")
    private BigDecimal selfDriverTotalFee;

    @ApiModelProperty(value = "自有司机提成")
    @DataBeanPath(fieldName = "自有司机提成(元/单)", beanPath = "deductAmount")
    private BigDecimal deductAmount;

    @ApiModelProperty(value = "外协司机运单数")
    @DataBeanPath(fieldName = "外协司机运单数", beanPath = "outsourceDriverOrderCount")
    private Integer outsourceDriverOrderCount;
    @ApiModelProperty(value = "外协司机绩效总额（司机提成）")
    @DataBeanPath(fieldName = "外协司机绩效总额", beanPath = "outsourceDriverTotalFee")
    private BigDecimal outsourceDriverTotalFee;

//    @ApiModelProperty(value = "计费数量取值方式, 1:以装车量计费, 2:以卸车量计费(涨吨扣亏), 3:以较小值计费, 4:以卸车量计费")
//    private ChargeTypeEnum carrierChargeType;

    @ApiModelProperty(value = "外协司机计费数量")
    @DataBeanPath(fieldName = "外协司机计费数量", beanPath = "outsourceDriverBillCount")
    private BigDecimal outsourceDriverBillCount;

//    @ApiModelProperty(value = "配置内容")
//    @DataBeanObject
//    private DeductConfig deductConfig;

    @ApiModelProperty(value = "外协司机运价")
    @DataBeanPath(fieldName = "外协司机运价", beanPath = "feeConfig")
    private BigDecimal feeConfig;

    @ApiModelProperty(value = "外协司机抹零规则")
    @DataBeanPath(fieldName = "外协司机抹零规则", beanPath = "roundType.description")
    private RoundTypeEnum roundType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


    /*@Getter
    @Setter
    @ToString
    @NoArgsConstructor
    public class DeductConfig implements Serializable {


        *//**
         * 自有司机提成
         *//*
        @DataBeanPath(fieldName = "自有司机提成(元/单)", beanPath = "deductAmount")
        private BigDecimal deductAmount;

        *//**
         * 外协司机费用
         *//*
        @DataBeanPath(fieldName = "外协司机运价", beanPath = "feeConfig")
        private BigDecimal feeConfig;
        *//**
         * RoundTypeEnum 抹零规则
         *//*
        @DataBeanPath(fieldName = "外协司机抹零规则", beanPath = "roundType.description")
        private RoundTypeEnum roundType;

    }*/

}

