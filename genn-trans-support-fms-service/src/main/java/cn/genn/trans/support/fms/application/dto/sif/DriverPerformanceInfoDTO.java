package cn.genn.trans.support.fms.application.dto.sif;

import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.core.sif.interfaces.enums.ChargeTypeEnum;
import cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum;
import cn.genn.trans.core.sif.interfaces.enums.LossTypeEnum;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.interfaces.enums.sif.DriverTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.sif.RoundTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SifChargeBillDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverPerformanceInfoDTO implements Serializable {
    // 运单签收周期 签收时间倒排 第一个---最后一个
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("运单id")
    private Long orderId;

    @ApiModelProperty(value = "运单号")
    @DataBeanPath(fieldName = "运单号", beanPath = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "业务类型")
    @DataBeanPath(fieldName = "业务类型", beanPath = "businessType.description")
    private OrderBusinessTypeEnum businessType;

    @ApiModelProperty(value = "司机id")
    private Long driverId;

    @ApiModelProperty(value = "司机名称")
    @DataBeanPath(fieldName = "司机名称", beanPath = "driverName")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverMobile")
    private String driverMobile;

    @ApiModelProperty(value = "司机类型 (自有、外协)")
    @DataBeanPath(fieldName = "司机类型", beanPath = "driverType.description")
    private DriverTypeEnum driverType;

    @ApiModelProperty(value = "车辆id")
    private Long vehicleId;

    @ApiModelProperty(value = "车牌号")
    @DataBeanPath(fieldName = "车牌号", beanPath = "vehiclePlateNo")
    private String vehiclePlateNo;

    @ApiModelProperty(value = "挂车ID")
    private Long trailerId;

    @ApiModelProperty(value = "挂车车牌号")
    @DataBeanPath(fieldName = "挂车车牌号", beanPath = "trailerPlateNo")
    private String trailerPlateNo;

    @ApiModelProperty(value = "车辆类型名称")
    @DataBeanPath(fieldName = "车辆类型名称", beanPath = "vehicleTypeName")
    private String vehicleTypeName;

    @ApiModelProperty(value = "挂车名称")
    @DataBeanPath(fieldName = "挂车名称", beanPath = "trailerName")
    private String trailerName;


    @ApiModelProperty(value = "费用（付款方给收款方支付的费用）")
    @DataBeanPath(fieldName = "应付绩效费用(元)", beanPath = "fee")
    private BigDecimal fee;

/*    @ApiModelProperty(value = "配置内容")
    @DataBeanObject
    private DeductConfig deductConfig;*/

    @ApiModelProperty(value = "自有司机提成")
    @DataBeanPath(fieldName = "自有司机提成(元/单)", beanPath = "deductAmount")
    private BigDecimal deductAmount;

    @ApiModelProperty(value = "外协司机运价")
    @DataBeanPath(fieldName = "外协司机运价", beanPath = "feeConfig")
    private BigDecimal feeConfig;

    @ApiModelProperty(value = "外协司机抹零规则")
    @DataBeanPath(fieldName = "外协司机抹零规则", beanPath = "roundType.description")
    private RoundTypeEnum roundType;

    @ApiModelProperty(value = "计费单位, 1:TON-吨, 2:SIDE-方, 3:UNIT-件, 4:TRUCK-车")
    @DataBeanPath(fieldName = "计费单位", beanPath = "carrierChargeUnit.description")
    private ChargeUnitEnum carrierChargeUnit;

    @ApiModelProperty(value = "装车重量")
    @DataBeanPath(fieldName = "装车数量", beanPath = "loadingWeight")
    private BigDecimal loadingWeight;
    private BigDecimal loadingPackage;
    private BigDecimal loadingVolume;
    @ApiModelProperty(value = "卸车重量")
    @DataBeanPath(fieldName = "卸车数量", beanPath = "unloadingWeight")
    private BigDecimal unloadingWeight;
    private BigDecimal unloadingPackage;
    private BigDecimal unloadingVolume;

    @ApiModelProperty(value = "装车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "装车时间", beanPath = "loadingTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime loadingTime;

    @ApiModelProperty(value = "卸车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "卸车时间", beanPath = "unloadingTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime unloadingTime;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "签收时间", beanPath = "signTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime signTime;


    @ApiModelProperty(value = "含税运价")
    private BigDecimal taxPrice;
    @ApiModelProperty(value = "承运合同ID")
    private Long carrierContractId;

    @ApiModelProperty(value = "承运合同编号")
    private String carrierContractNo;

    @ApiModelProperty(value = "承运线路ID")
    private Long carrierLineId;

    @ApiModelProperty(value = "装车地址简称")
    private String loadingSubName;

    @ApiModelProperty(value = "装车地址")
    private String loadingAddress;

    @ApiModelProperty(value = "卸车地址简称")
    private String unloadingSubName;

    @ApiModelProperty(value = "卸车地址")
    private String unloadingAddress;

    @ApiModelProperty(value = "货物id")
    private Long cargoId;

    @ApiModelProperty(value = "货物编号")
    private String cargoNo;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "货物规格")
    private String cargoSpec;

    @ApiModelProperty(value = "线路提成配置LOG-ID")
    private Long configLogId;






    @ApiModelProperty(value = "计费数量取值方式, 1:以装车量计费, 2:以卸车量计费(涨吨扣亏), 3:以较小值计费, 4:以卸车量计费")
    private ChargeTypeEnum carrierChargeType;
    @ApiModelProperty(value = "确认时间-费用确认操作时间=bill updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime confirmTime;
    @ApiModelProperty(value = "货物价格")
    private BigDecimal cargoPrice;
    @ApiModelProperty(value = "合理途耗")
    private BigDecimal lossValue;
    @ApiModelProperty(value = "合理途耗")
    private LossTypeEnum lossType;
//    @ApiModelProperty(value = "抹零规则")
//    private RoundTypeEnum roundType;
    @ApiModelProperty(value = "运费总金额")
    private BigDecimal totalPrice;
    @ApiModelProperty(value = "含税金额")
    private BigDecimal taxTotalPrice;
    @ApiModelProperty(value = "基础运费")
    private BigDecimal basePrice;
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal taxFreePrice;
    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;
    @ApiModelProperty(value = "货损赔偿")
    private BigDecimal lossPrice;
    @ApiModelProperty(value = "抹零金额")
    private BigDecimal roundPrice;

}

