package cn.genn.trans.support.fms.application.dto.sif;

import cn.genn.trans.core.sif.interfaces.enums.DriverTypeEnum;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.interfaces.enums.sif.StatStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SifChargeBillDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverWaitSummaryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    @ApiModelProperty(value = "司机姓名")
    @DataBeanPath(fieldName = "司机姓名", beanPath = "driverName")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverMobile")
    private String driverMobile;
    @ApiModelProperty(value = "银行名称")
    @DataBeanPath(fieldName = "开户名", beanPath = "bankName")
    private String bankName;
    @ApiModelProperty(value = "银行卡号")
    @DataBeanPath(fieldName = "银行卡号", beanPath = "bankCardNo")
    private String bankCardNo;
    @ApiModelProperty(value = "司机类型")
    @DataBeanPath(fieldName = "司机类型", beanPath = "driverType.description")
    private DriverTypeEnum driverType;
    @ApiModelProperty(value = "统计状态 0-待确认，1-待汇总，10-已确认，20-已汇总")
    @DataBeanPath(fieldName = "状态", beanPath = "statStatus.description")
    private StatStatusEnum statStatus;
    @ApiModelProperty(value = "运单数")
    @DataBeanPath(fieldName = "运单数(单)", beanPath = "orderCount")
    private Integer orderCount;
    @ApiModelProperty(value = "总费用")
    @DataBeanPath(fieldName = "绩效/费用(元)", beanPath = "totalFee")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

}

