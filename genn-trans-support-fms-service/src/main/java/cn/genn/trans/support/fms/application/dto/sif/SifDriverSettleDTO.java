package cn.genn.trans.support.fms.application.dto.sif;

import cn.genn.trans.core.sif.interfaces.enums.DriverTypeEnum;
import cn.genn.trans.core.sif.interfaces.enums.SettleStatusEnum;
import cn.genn.trans.support.fms.infrastructure.converter.DateTimeWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 司机绩效结算单对象
 * @since 2024/8/8
 */
@Data
public class SifDriverSettleDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "结算月份")
    @DataBeanPath(fieldName = "结算月份", beanPath = "settleMonth")
    private String settleMonth;

    /**
     * 结算单号
     */
    @ApiModelProperty(value = "结算单号")
    @DataBeanPath(fieldName = "绩效结算单号", beanPath = "settleNo")
    private String settleNo;

    /**
     * 司机id
     */
    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @DataBeanPath(fieldName = "司机姓名", beanPath = "driverName")
    private String driverName;

    /**
     * 司机手机号
     */
    @ApiModelProperty(value = "司机手机号")
    @DataBeanPath(fieldName = "司机手机号", beanPath = "driverMobile")
    private String driverMobile;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    @DataBeanPath(fieldName = "开户行", beanPath = "bankBranchName")
    private String bankBranchName;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @DataBeanPath(fieldName = "开户名", beanPath = "bankName")
    private String bankName;


    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    @DataBeanPath(fieldName = "银行卡号", beanPath = "bankCardNo")
    private String bankCardNo;

    /**
     * 司机类型, 0.自有 1.外协
     */
    @ApiModelProperty(value = "司机类型")
    @DataBeanPath(fieldName = "司机类型", beanPath = "driverType.description")
    private DriverTypeEnum driverType;

    /**
     * 结算状态, 0: WAIT_SUMMARIZE-待汇总, 1:WAIT_SETTLE-待结算, 2:SETTLED-已结算
     */
    @ApiModelProperty(value = "结算状态")
    @DataBeanPath(fieldName = "状态", beanPath = "settleStatus.description")
    private SettleStatusEnum settleStatus;

    /**
     * 运单数
     */
    @ApiModelProperty(value = "运单数")
    @DataBeanPath(fieldName = "运单数(单)", beanPath = "orderCount")
    private Integer orderCount;

    /**
     * 应收费用
     */
    @ApiModelProperty(value = "应收费用")
    @DataBeanPath(fieldName = "绩效/费用(元)", beanPath = "totalCost")
    private BigDecimal totalCost;

    // 绩效汇总时间
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    @DataBeanPath(fieldName = "绩效汇总操作人", beanPath = "createUserName")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "绩效汇总时间", beanPath = "createTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime createTime;

    /**
     * 结算操作人
     */
    @ApiModelProperty(value = "结算操作人")
    @DataBeanPath(fieldName = "结算操作人", beanPath = "settleUserName")
    private String settleUserName;
    /**
     * 结算时间
     */
    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DataBeanPath(fieldName = "结算时间", beanPath = "settleTime", wrapperClass = DateTimeWrapper.class)
    private LocalDateTime settleTime;

    /**
     * 结算操作人id
     */
    @ApiModelProperty(value = "结算操作人ID")
    private Long settleUserId;






    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 承运商id
     */
    @ApiModelProperty(value = "承运商ID")
    private Long carrierId;

    /**
     * 承运商名称
     */
    @ApiModelProperty(value = "承运商名称")
    private String carrierName;




}
