package cn.genn.trans.support.fms.application.extension;

import cn.genn.trans.support.fms.application.extension.DataMappingStrategy;
import cn.genn.trans.support.fms.application.extension.DefaultDataMappingStrategy;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class DataMappingStrategyFactory {

    @Value("${genn.data.mapping.strategy.class:cn.genn.trans.support.fms.application.extension.DefaultDataMappingStrategy}")
    private String defaultStrategyClass;

    private final ApplicationContext applicationContext;
    private final Map<String, DataMappingStrategy> strategyCache = new ConcurrentHashMap<>();

    public DataMappingStrategyFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        // 初始化默认策略
        DataMappingStrategy defaultStrategy = createStrategy(defaultStrategyClass);
        if (defaultStrategy != null) {
            strategyCache.put(defaultStrategyClass, defaultStrategy);
        }
    }

    public DataMappingStrategy createStrategy(String strategyClass) {
        try {
            Class<?> clazz = Class.forName(strategyClass);
            if (DataMappingStrategy.class.isAssignableFrom(clazz)) {
                return (DataMappingStrategy) applicationContext.getBean(clazz);
            }
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Strategy class not found: " + strategyClass, e);
        }
        return null;
    }

    public DataMappingStrategy getStrategy(String strategyClass) {
        if (StrUtil.isBlank(strategyClass)) {
            return strategyCache.get(defaultStrategyClass);
        }
        return strategyCache.computeIfAbsent(strategyClass, this::createStrategy);
    }
}
