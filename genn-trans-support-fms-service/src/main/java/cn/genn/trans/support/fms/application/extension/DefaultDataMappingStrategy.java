package cn.genn.trans.support.fms.application.extension;

import cn.hutool.core.bean.BeanPath;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class DefaultDataMappingStrategy implements DataMappingStrategy {

    @Override
    public Map<String, Object> mapData(Map<String, String> dataMap, List<String> propertyNameList) {
        Map<String, Object> result = new HashMap<>();
        propertyNameList.forEach(propertyName -> {
            BeanPath resolver = new BeanPath(propertyName);
            Object value = resolver.get(dataMap);
            result.put(propertyName, Optional.ofNullable(value).orElse(""));
        });
        return result;
    }
}
