package cn.genn.trans.support.fms.application.extension;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileNameConfig {
    private String fileNameGenerationStrategy;
    private String fileName;
    private String collBackParam;
    private String paramKey;
    private Map<String, String> enumDesc;
    private String fileType;
}
