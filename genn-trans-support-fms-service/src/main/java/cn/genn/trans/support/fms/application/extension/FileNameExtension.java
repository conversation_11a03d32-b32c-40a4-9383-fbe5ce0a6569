package cn.genn.trans.support.fms.application.extension;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.FileExportConfConfig;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * @Date: 2024/11/11
 * @Author: kangjian
 */
public class FileNameExtension {


    public static FileNameConfig buildFileNameConfig(FileExportTaskDTO fileExportTaskDTO) {
        FileExportConfConfig config = fileExportTaskDTO.getExportConfConfig();
        return FileNameConfig.builder()
                .fileNameGenerationStrategy(config.getFileNameGenerationStrategy())
                .fileName(config.getFileName())
                .collBackParam(fileExportTaskDTO.getTaskParam())
                .paramKey(config.getParamKey())
                .enumDesc(config.getEnumDesc())
                .fileType(fileExportTaskDTO.getTemplateType())
                .build();
    }

    /**
     * 根据配置项生成文件名
     *
     * @param fileExportTaskDTO
     * @return
     */
    public static String generateFileName(FileExportTaskDTO fileExportTaskDTO) {
        FileNameConfig config = buildFileNameConfig(fileExportTaskDTO);
        if (StrUtil.isBlank(config.getFileNameGenerationStrategy())) {
            return getFileName0(fileExportTaskDTO);
        }
        switch (config.getFileNameGenerationStrategy()) {
            case "taskName_time":
                return getFileName0(fileExportTaskDTO);
            case "configFileName_time":
                return getFileName1(config.getFileName());
            case "configEnumDesc_time":
                return getFileName2(config.getFileName(), config.getCollBackParam(), config.getParamKey(), config.getEnumDesc(), config.getFileType());
            case "callBackParam_key":
                return getFileName3(config.getCollBackParam(), config.getParamKey(), config.getFileType());
            default:
                return fileExportTaskDTO.getFileName() + "." + fileExportTaskDTO.getTemplateType();
        }
    }

    /**
     * 默认情况 任务名+时间戳
     *
     * @param fileExportTaskDTO
     * @return
     */
    private static String getFileName0(FileExportTaskDTO fileExportTaskDTO) {
        return fileExportTaskDTO.getFileName() + "_" + LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "." + fileExportTaskDTO.getTemplateType();
    }

    /**
     * 指定文件名+时间戳
     *
     * @param fileName
     * @return
     */
    private static String getFileName1(String fileName) {
        return fileName + "_" + LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
    }

    /**
     * 根据回调参数中的属性值生成文件名
     *
     * @param fileName
     * @param collBackParam
     * @param fileType
     * @return
     */
    private static String getFileName2(String fileName, String collBackParam, String paramKey, Map<String, String> enumDesc, String fileType) {
        Map<String, Object> params = JsonUtils.parseToObjectMap(collBackParam);
        StringBuilder sb = new StringBuilder();
        sb.append(fileName);
        String queryDesc = enumDesc.get(params.get(paramKey).toString());
        if (StrUtil.isBlank(queryDesc)) {
            throw new BusinessException(MessageCode.FILE_NAME_GENERATION_ERROR);
        }
        sb.append("_")
                .append(queryDesc)
                .append("_")
                .append(LocalDateTime.now()
                        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))
                .append(".")
                .append(fileType);
        return sb.toString();
    }

    /**
     * 根据回调参数中的属性值生成文件名
     *
     * @param collBackParam
     * @param paramKey
     * @param fileType
     * @return
     */
    private static String getFileName3(String collBackParam, String paramKey, String fileType) {
        Map<String, Object> params = JsonUtils.parseToObjectMap(collBackParam);
        if (params.containsKey(paramKey)) {
            return params.get(paramKey).toString() + "." + fileType;
        }
        throw new BusinessException(MessageCode.FILE_NAME_GENERATION_ERROR);
    }
}
