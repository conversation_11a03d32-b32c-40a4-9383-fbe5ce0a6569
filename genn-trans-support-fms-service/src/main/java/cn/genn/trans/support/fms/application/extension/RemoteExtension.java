package cn.genn.trans.support.fms.application.extension;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.FileExportConfConfig;
import cn.genn.trans.support.fms.interfaces.dto.config.FileImportConfConfig;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.MultiValueMap;
import com.fasterxml.jackson.core.type.TypeReference;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class RemoteExtension {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RestTemplate lbRestTemplate;

    public static String handlePageParam(String callbackParam, int pageNo, int pageSize) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(StrUtil.isBlank(callbackParam) ? "{}" : callbackParam);
            if (jsonNode instanceof ObjectNode) {
                ObjectNode objectNode = (ObjectNode) jsonNode;
                objectNode.put("pageNo", pageNo);
                objectNode.put("pageSize", pageSize);
                return objectMapper.writeValueAsString(objectNode);
            }
        } catch (Exception e) {
            return callbackParam;
        }
        return callbackParam;
    }

    /**
     * 返回的是分页的结果的情况
     *
     * @param restTemplateStrategy
     * @param url
     * @param callbackParam
     * @param tokenData
     * @param responseType
     * @param <T>
     * @return
     */
    public <T> PageResultDTO<T> fetchPostPageResponse(String restTemplateStrategy, String url, String callbackParam, String tokenData, TypeToken<T> responseType) {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(tokenData));
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(callbackParam, headers);
        try {
            if (StrUtil.isBlank(restTemplateStrategy)) {
                ResponseEntity<String> responseEntity = sendPostRequest(restTemplate, url, httpEntity);
                return handlePageResponse(responseEntity, responseType);
            }
            ResponseEntity<String> responseEntity = null;
            switch (restTemplateStrategy) {
                case "lbRestTemplate":
                    responseEntity = sendPostRequest(lbRestTemplate, url, httpEntity);
                    return handlePageResponse(responseEntity, responseType);
                default:
                    responseEntity = sendPostRequest(restTemplate, url, httpEntity);
                    return handlePageResponse(responseEntity, responseType);
            }
        } catch (RuntimeException e) {
            handleException(e);
        }
        // 这里返回 null 是因为异常已经被处理，理论上不会到达这里
        return null;
    }

    public <T> ResponseResult<T> fetchPostResponse(String restTemplateStrategy, String url, String callbackParam, String tokenData, TypeToken<T> responseType) {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(tokenData));
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(callbackParam, headers);
        try {
            if (StrUtil.isBlank(restTemplateStrategy)) {
                ResponseEntity<String> responseEntity = sendPostRequest(restTemplate, url, httpEntity);
                return handleResponse(responseEntity, responseType);
            }
            ResponseEntity<String> responseEntity = null;
            switch (restTemplateStrategy) {
                case "lbRestTemplate":
                    responseEntity = sendPostRequest(lbRestTemplate, url, httpEntity);
                    return handleResponse(responseEntity, responseType);
                default:
                    responseEntity = sendPostRequest(restTemplate, url, httpEntity);
                    return handleResponse(responseEntity, responseType);
            }
        } catch (RuntimeException e) {
            handleException(e);
        }
        // 这里返回 null 是因为异常已经被处理，理论上不会到达这里
        return null;
    }

    private ResponseEntity<String> sendPostRequest(RestTemplate restTemplate, String url, HttpEntity<String> httpEntity) {
        log.info("远程调用, url: {}, body: {}", url, httpEntity.getBody());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
        log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
        return responseEntity;
    }

    private <T> PageResultDTO<T> handlePageResponse(ResponseEntity<String> responseEntity, TypeToken<T> responseType) {
        if (HttpStatus.OK != responseEntity.getStatusCode()) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
        log.info("远程调用, result: {}", responseEntity.getBody());
        TypeReference<ResponseResult<PageResultDTO<T>>> typeRef = new TypeReference<ResponseResult<PageResultDTO<T>>>() {
        };
        ResponseResult<PageResultDTO<T>> responseResult = JsonUtils.parse(responseEntity.getBody(), typeRef);
        if (Objects.isNull(responseResult)) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
        if (!responseResult.isSuccess()) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
        }
        return responseResult.getData();
    }

    private <T> ResponseResult<T> handleResponse(ResponseEntity<String> responseEntity, TypeToken<T> responseType) {
        if (HttpStatus.OK != responseEntity.getStatusCode()) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
        log.info("远程调用, result: {}", responseEntity.getBody());
        TypeReference<ResponseResult<T>> typeRef = new TypeReference<ResponseResult<T>>() {
        };
        ResponseResult<T> responseResult = JsonUtils.parse(responseEntity.getBody(), typeRef);
        if (Objects.isNull(responseResult)) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
        if (!responseResult.isSuccess()) {
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
        }
        return responseResult;
    }

    private void handleException(RuntimeException e) {
        if (e instanceof BusinessException) {
            throw e;
        } else {
            log.error("远程调用, error", e);
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
    }

    @Resource
    private InternalDomainUtil internalDomainUtil;

    public String generateQueryUrl(FileExportTaskDTO taskInfo) {
        FileExportConfConfig config = taskInfo.getExportConfConfig();
        if (StrUtil.isBlank(config.getQueryUrlGenerationStrategy())) {
            return handleQueryUrl(taskInfo);
        }
        switch (config.getQueryUrlGenerationStrategy()) {
            // lb调用可指定 "http://genn-data-hub"
            case "lb":
                return handleLbQueryUrl(taskInfo);
            case "http":
                return handleQueryUrl(taskInfo);
            case "url":
                return taskInfo.getExportConfConfig().getQueryUrl();
            default:
                return handleQueryUrl(taskInfo);
        }
    }

    public String generateQueryUrl(FileImportTaskDTO taskInfo) {
        FileImportConfConfig config = taskInfo.getImportConfConfig();
        if (StrUtil.isBlank(config.getQueryUrlGenerationStrategy())) {
            return handleQueryUrl(taskInfo);
        }
        switch (config.getQueryUrlGenerationStrategy()) {
            // lb调用可指定 "http://genn-data-hub"
            case "lb":
                return handleLbQueryUrl(taskInfo);
            case "http":
                return handleQueryUrl(taskInfo);
            case "url":
                return taskInfo.getImportConfConfig().getQueryUrl();
            default:
                return handleQueryUrl(taskInfo);
        }
    }

    public String handleQueryUrl(FileExportTaskDTO taskInfo) {
        String url = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String queryUrl = taskInfo.getExportConfConfig().getQueryUrl();
        return url + queryUrl;
    }

    public String handleLbQueryUrl(FileExportTaskDTO taskInfo) {
        String url = taskInfo.getExportConfConfig().getLbServiceName();
        String queryUrl = taskInfo.getExportConfConfig().getQueryUrl();
        return url + queryUrl;
    }

    public String handleQueryUrl(FileImportTaskDTO taskInfo) {
        String url = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String queryUrl = taskInfo.getImportConfConfig().getQueryUrl();
        return url + queryUrl;
    }

    public String handleLbQueryUrl(FileImportTaskDTO taskInfo) {
        String url = taskInfo.getImportConfConfig().getLbServiceName();
        String queryUrl = taskInfo.getImportConfConfig().getQueryUrl();
        return url + queryUrl;
    }
}

