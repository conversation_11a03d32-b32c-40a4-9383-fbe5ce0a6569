package cn.genn.trans.support.fms.application.factory;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileExportConfDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

/**
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Component
public class FileExportTaskFactory {

    public FileExportTaskDTO createSubmitTask(FileExportTaskSubmitCommand command, FileExportConfDTO fileExportConfDTO) {
        return FileExportTaskDTO.builder()
            .systemId(CurrentUserHolder.getSystemId())
            .tenantId(CurrentUserHolder.getTenantId())
            .businessCode(StrUtil.isNotBlank(command.getSubBusinessCode()) ? command.getSubBusinessCode() : command.getBusinessCode().getCode())
            .templateType(fileExportConfDTO.getTemplateType())
            .templateConf(fileExportConfDTO.getConfig())
            .taskName(fileExportConfDTO.getTaskName())
            .fileName(fileExportConfDTO.getFileName())
            .taskParam(JsonUtils.toJson(command.getTaskParam()))
            .tokenData(JsonUtils.toJson(CurrentUserHolder.getCurrentUser()))
            .taskStatus(TaskStatusEnum.WAIT)
            .executeCount(0)
            .createUserId(CurrentUserHolder.getUserId())
            .createUserName(CurrentUserHolder.getUserName())
            .build();
    }
}
