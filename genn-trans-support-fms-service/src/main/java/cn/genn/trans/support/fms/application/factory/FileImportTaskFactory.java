package cn.genn.trans.support.fms.application.factory;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.command.FileImportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.dto.*;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Component
public class FileImportTaskFactory {

    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;

    public FileImportTaskDTO createSubmitTask(FileImportTaskSubmitCommand command, FileImportConfDTO fileImportConfDTO) {
        // 根据fileKey查询文件
        FileDetailQuerySingleCommand querySingleCommand = FileDetailQuerySingleCommand.builder()
            .fileKey(command.getImportFileKey())
            .build();
        FileInfoDTO importFileInfo = fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(querySingleCommand);
        String originalFileName = importFileInfo.getOriginalFilename();
        // 在文件名后缀前增加"_导入结果"
        String fileNameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf("."));
        String fileExt = originalFileName.substring(originalFileName.lastIndexOf("."));
        String taskFileName = fileNameWithoutExt + "_导入结果" + fileExt;
        return FileImportTaskDTO.builder()
            .systemId(CurrentUserHolder.getSystemId())
            .tenantId(CurrentUserHolder.getTenantId())
            .businessCode(StrUtil.isNotBlank(command.getSubBusinessCode()) ? command.getSubBusinessCode() : command.getBusinessCode().getCode())
            .templateType(fileImportConfDTO.getTemplateType())
            .templateConf(fileImportConfDTO.getTemplateConf())
            .taskName(fileImportConfDTO.getTaskName())
            .fileName(taskFileName)
            .importFileKey(command.getImportFileKey())
            .taskParam(command.getCallbackParam())
            .tokenData(JsonUtils.toJson(CurrentUserHolder.getCurrentUser()))
            .taskStatus(TaskStatusEnum.WAIT)
            .createUserId(CurrentUserHolder.getUserId())
            .createUserName(CurrentUserHolder.getUserName())
            .build();
    }
}
