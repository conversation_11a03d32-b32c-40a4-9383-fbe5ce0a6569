package cn.genn.trans.support.fms.application.job;

import cn.genn.trans.support.fms.application.service.action.FileExportTaskActionService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 文件导出任务异步执行任务
 *
 * <AUTHOR>
 * @date 2024/5/28
 */
@Component
public class FileExportTaskExecuteJob extends IJobHandler {

    @Resource
    private FileExportTaskActionService fileExportTaskActionService;

    @Override
    @XxlJob("fileExportTaskOfExecuteJob")
    public void execute() throws Exception {
        XxlJobHelper.log("文件导出任务异步执行任务开始");
        fileExportTaskActionService.execute();
        XxlJobHelper.log("文件导出任务异步执行任务结束");
    }
}
