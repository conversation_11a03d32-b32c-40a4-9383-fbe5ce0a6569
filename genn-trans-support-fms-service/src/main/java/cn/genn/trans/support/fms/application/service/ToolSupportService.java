package cn.genn.trans.support.fms.application.service;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.application.command.Md2HtmlCommand;
import cn.genn.trans.support.fms.application.command.UploadHtmlCommand;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.application.service.query.FileUploadBucketConfigQueryService;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.constant.Constant;
import cn.genn.trans.support.fms.core.file.InputStreamFileWrapper;
import cn.genn.trans.support.fms.core.upload.FileUploadBusinessCommonData;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.UnicodeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ToolSupportService {

    private final FileStorageService fileStorageService;
    private final FileUploadSupportActionService fileUploadSupportActionService;
    private final FileUploadBucketConfigQueryService bucketConfigQueryService;


    public String md2html(Md2HtmlCommand command) {
        String htmlContent = Base64.decodeStr(command.getHtmlTemplate(), StandardCharsets.UTF_8);
        String mdContent = Base64.decodeStr(command.getMdContent(), StandardCharsets.UTF_8);

        if (htmlContent == null) {
            throw new BaseException("html模板获取失败");
        }
        if (mdContent == null) {
            throw new BaseException("md内容获取失败");
        }
        String result = htmlContent.replace("{mdContent}", mdContent);
        Map<String, Object> params = command.getParams();
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    result = result.replace("{" + key + "}", value.toString());
                }
            }
        }
        // 将result上传到对象存储
        try {
            ByteArrayInputStream stream = IoUtil.toStream(result, Charset.defaultCharset());
            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(stream);
            inputStreamFileWrapper.setName(UUID.fastUUID() + ".html");
            inputStreamFileWrapper.setContentType("");
            // 获取bucket配置
            FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(-10L);
            if (Objects.isNull(bucketConfig)) {
                throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
            }
            fileUploadSupportActionService.addPlatformConfig(bucketConfig);
            FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(-10L)
                .systemId(1L).businessCode("coze").createUserId(0L)
                .createUserName("admin").build();

            FileInfo fileInfo = fileStorageService.of(inputStreamFileWrapper)
                .setPlatform(bucketConfig.getPlatform())
                .setPath("md2html/")
                .setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
                .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
                .setHashCalculator(MessageDigest.getInstance("SHA-384"))
                .setBusinessCommonData(businessCommonData).upload();
            String url = "https://genn-app.gennergy.com" + "/"+ fileInfo.getPath()  + fileInfo.getFilename();
            log.info("md2html url:{}", url);
            return url;
        } catch (Exception e) {
            log.error("md2html error ", e);
        }
        return result;
    }

    public String uploadHtml(UploadHtmlCommand command) {
        // 将result上传到对象存储
        try {
            ByteArrayInputStream stream = IoUtil.toStream(command.getContent().getBytes());
            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(stream);
            inputStreamFileWrapper.setName(UUID.fastUUID() + ".html");
            inputStreamFileWrapper.setContentType("tzext/html");
            // 获取bucket配置
            FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(-10L);
            if (Objects.isNull(bucketConfig)) {
                throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
            }
            fileUploadSupportActionService.addPlatformConfig(bucketConfig);
            FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(-10L)
                .systemId(1L).businessCode("coze").createUserId(0L)
                .createUserName("admin").build();

            FileInfo fileInfo = fileStorageService.of(inputStreamFileWrapper)
                .setPlatform(bucketConfig.getPlatform())
                .setPath("html/")
                .setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
                .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
                .setHashCalculator(MessageDigest.getInstance("SHA-384"))
                .setBusinessCommonData(businessCommonData).upload();
            String url = "https://genn-app.gennergy.com" + "/"+ fileInfo.getPath()  + fileInfo.getFilename();
            log.info("md2html url:{}", url);
            return url;
        } catch (Exception e) {
            log.error("md2html error ", e);
        }
        return null;
    }
}
