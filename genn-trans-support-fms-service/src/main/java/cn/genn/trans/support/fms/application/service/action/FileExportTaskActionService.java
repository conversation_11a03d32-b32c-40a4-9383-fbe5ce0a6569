package cn.genn.trans.support.fms.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.lock.base.LockException;
import cn.genn.spring.boot.starter.event.rocketmq.component.RocketMQEventPublish;
import cn.genn.trans.support.fms.application.assembler.FileExportTaskAssembler;
import cn.genn.trans.support.fms.application.dto.FileExportTaskSubmitEvent;
import cn.genn.trans.support.fms.application.factory.FileExportTaskFactory;
import cn.genn.trans.support.fms.application.service.query.FileExportConfQueryService;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.constant.Constants;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileExportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportTaskPO;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileExportConfDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 文件导出任务操作业务逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileExportTaskActionService {

    @Resource
    private FileExportConfQueryService fileExportConfQueryService;
    @Resource
    private FileExportTaskFactory fileExportTaskFactory;
    @Resource
    private FileExportTaskMapper fileExportTaskMapper;
    @Resource
    private FileExportTaskAssembler fileExportTaskAssembler;
    @Resource
    private RocketMQEventPublish rocketMQEventPublish;

    ExecutorService executor = Executors.newFixedThreadPool(10);

    /**
     * 提交一个导出任务 异步执行
     *
     * @param command
     * @return
     */
    public FileExportTaskDTO submit(FileExportTaskSubmitCommand command) {
        Long systemId = CurrentUserHolder.getSystemId();
        Long tenantId = CurrentUserHolder.getTenantId();
        // 校验业务场景是否存在
        String businessCode = command.getBusinessCode().getCode();
        FileExportConfDTO fileExportConfDTO = fileExportConfQueryService.queryByBusinessCodeWithSub(systemId, tenantId, businessCode, command.getSubBusinessCode());
        if (Objects.isNull(fileExportConfDTO)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            fileExportConfDTO = fileExportConfQueryService.queryByBusinessCodeWithSub(systemId, 1L, businessCode, command.getSubBusinessCode());
            if (Objects.isNull(fileExportConfDTO)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        // TODO 配置中模板的特殊处理 目前还没有涉及 比如说有些是指定字段导出

        // 提交任务
        FileExportTaskDTO fileExportTaskDTO = fileExportTaskFactory.createSubmitTask(command, fileExportConfDTO);
        insertTask(fileExportTaskDTO);

        FileExportTaskSubmitEvent event = new FileExportTaskSubmitEvent();
        event.setTaskId(fileExportTaskDTO.getId());
        event.setTags(Constants.FILE_EXPORT_TASK_SUBMIT_MQ_TAG);
        event.setTenantId(String.valueOf(fileExportTaskDTO.getTenantId()));
        event.setBizKey(String.valueOf(fileExportTaskDTO.getId()));

        rocketMQEventPublish.publish(event);
        return fileExportTaskDTO;
    }

    private void insertTask(FileExportTaskDTO fileExportTaskDTO) {
        FileExportTaskPO po = fileExportTaskAssembler.toPO(fileExportTaskDTO);
        fileExportTaskMapper.insert(po);
        fileExportTaskDTO.setId(po.getId());
    }

    @IgnoreTenant
    public void execute() {
        // 查询待执行任务并且小于执行3次 未删除的任务
        QueryWrapper<FileExportTaskPO> qw = new QueryWrapper<FileExportTaskPO>();
        qw.lambda()
            .in(FileExportTaskPO::getTaskStatus, TaskStatusEnum.WAIT.getCode(), TaskStatusEnum.FAIL.getCode())
            .lt(FileExportTaskPO::getExecuteCount, 3)
            .eq(FileExportTaskPO::getDeleted, DeletedEnum.NOT_DELETED.getCode());
        List<FileExportTaskPO> taskList = fileExportTaskMapper.selectList(qw);
        List<Long> waitTaskIds = taskList.stream().map(FileExportTaskPO::getId).collect(Collectors.toList());
        XxlJobHelper.log("待执行任务的id列表 waitTaskIds={}", waitTaskIds);
        if (CollectionUtil.isEmpty(waitTaskIds)) {
            return;
        }
        // 丢入线程池进行执行
        taskList.forEach(task -> executor.execute(() ->
            SpringUtil.getBean(FileExportTaskActionService.class).executeTask(task.getId())));
    }

    @IgnoreTenant
    @Trace
    public void executeTask(Long id) {
        XxlJobHelper.log("执行任务开始 id={}", id);
        log.info("执行任务开始 id={}", id);
        FileExportTaskDTO fileExportTaskDTO = fileExportTaskAssembler.PO2DTO(fileExportTaskMapper.selectById(id));
        if (Objects.isNull(fileExportTaskDTO)) {
            log.info("任务不存在 id={}", id);
            XxlJobHelper.log("任务不存在 id={}", id);
            return;
        }
        try {
            Map<String, CommonAbstractExportStrategy> businessStrategyMap = SpringUtil.getBeansOfType(CommonAbstractExportStrategy.class);
            // 遍历businessCode一致的处理类进行处理
            // 查询下businessCode 是否是子业务场景 子业务场景拿父业务场景的businessCode
            String compareBusinessCode = fileExportTaskDTO.getBusinessCode();
            FileExportConfDTO fileExportConfDTO = fileExportConfQueryService.queryBySubBusinessCode(fileExportTaskDTO.getSystemId(), 1L, fileExportTaskDTO.getBusinessCode());
            if (Objects.nonNull(fileExportConfDTO)) {
                compareBusinessCode = fileExportConfDTO.getBusinessCode();
            }
            String finalCompareBusinessCode = compareBusinessCode;
            CommonAbstractExportStrategy businessStrategy = businessStrategyMap.values()
                .stream()
                .filter(strategy -> strategy.getBusinessCode().getCode().equals(finalCompareBusinessCode))
                .findFirst()
                .orElseThrow(() -> new BusinessException(MessageCode.TASK_ERROR_NOT_STRATEGY));
            businessStrategy.export(fileExportTaskDTO);
            XxlJobHelper.log("执行任务成功 id={}", id);
            log.info("执行任务成功 id={}", id);
        } catch (LockException e) {
            log.error("执行任务失败,锁异常");
        } catch (Exception e) {
            // 更新任务为失败状态 增加执行次数
            log.error("执行任务失败", e);
            XxlJobHelper.log("执行任务失败 id={}", id);
            LambdaUpdateWrapper lambdaUpdateWrapper = new UpdateWrapper<FileExportTaskPO>().lambda()
                .eq(FileExportTaskPO::getId, id)
                .set(FileExportTaskPO::getTaskStatus, TaskStatusEnum.FAIL.getCode())
                .set(FileExportTaskPO::getExecuteCount, fileExportTaskDTO.getExecuteCount() + 1)
                .set(FileExportTaskPO::getRemark, StrUtil.sub(e.getMessage(), 0, 2048));
            fileExportTaskMapper.update(lambdaUpdateWrapper);
        }
    }

    @IgnoreTenant
    public void retry(Long id) {
        fileExportTaskMapper.update(null, new UpdateWrapper<FileExportTaskPO>()
            .lambda()
            .eq(FileExportTaskPO::getId, id)
            .set(FileExportTaskPO::getTaskStatus, TaskStatusEnum.WAIT.getCode())
            .set(FileExportTaskPO::getExecuteCount, 0));
        executeTask(id);
    }

    @IgnoreTenant
    public void cancel(Long id) {
        fileExportTaskMapper.update(null, new UpdateWrapper<FileExportTaskPO>()
            .lambda()
            .eq(FileExportTaskPO::getId, id)
            .set(FileExportTaskPO::getTaskStatus, TaskStatusEnum.CANCEL.getCode())
            .set(FileExportTaskPO::getExecuteCount, 0));
    }
}

