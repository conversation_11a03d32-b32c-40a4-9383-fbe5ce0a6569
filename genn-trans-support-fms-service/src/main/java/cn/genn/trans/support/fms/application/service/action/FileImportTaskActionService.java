package cn.genn.trans.support.fms.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.lock.base.LockException;
import cn.genn.spring.boot.starter.event.rocketmq.component.RocketMQEventPublish;
import cn.genn.trans.support.fms.application.assembler.FileImportTaskAssembler;
import cn.genn.trans.support.fms.application.dto.FileImportTaskSubmitEvent;
import cn.genn.trans.support.fms.application.factory.FileImportTaskFactory;
import cn.genn.trans.support.fms.application.service.query.FileImportConfQueryService;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractImportStrategy;
import cn.genn.trans.support.fms.infrastructure.constant.Constants;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileImportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileImportTaskPO;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.command.FileImportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileImportConfDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Map;
import java.util.Objects;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileImportTaskActionService {
    @Resource
    private FileImportConfQueryService fileImportConfQueryService;
    @Resource
    private FileImportTaskFactory fileImportTaskFactory;
    @Resource
    private RocketMQEventPublish rocketMQEventPublish;
    @Resource
    private FileImportTaskAssembler fileImportTaskAssembler;
    @Resource
    private FileImportTaskMapper fileImportTaskMapper;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    public FileImportTaskDTO submit(FileImportTaskSubmitCommand command) {
        Long systemId = CurrentUserHolder.getSystemId();
        Long tenantId = CurrentUserHolder.getTenantId();
        // 校验业务场景是否存在
        String businessCode = command.getBusinessCode().getCode();
        FileImportConfDTO fileExportConfDTO = fileImportConfQueryService.queryByBusinessCodeWithSub(systemId, tenantId, businessCode, command.getSubBusinessCode());
        if (Objects.isNull(fileExportConfDTO)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            fileExportConfDTO = fileImportConfQueryService.queryByBusinessCodeWithSub(systemId, 1L, businessCode, command.getSubBusinessCode());
            if (Objects.isNull(fileExportConfDTO)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        // TODO 配置中模板的特殊处理 目前还没有涉及 比如说有些是指定字段导出

        // 提交任务
        FileImportTaskDTO fileImportTaskDTO = fileImportTaskFactory.createSubmitTask(command, fileExportConfDTO);
        // 提交导入任务前 需要增加对导入文件第一行表头数据的校验
        FileDetailQuerySingleCommand querySingleCommand = FileDetailQuerySingleCommand.builder()
            .fileKey(command.getImportFileKey())
            .build();
        FileInfoDTO importfileInfoDTO = fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(querySingleCommand);
        if (importfileInfoDTO == null) {
            throw new BusinessException(MessageCode.EXPORT_TEMPLATE_NOT_EXIST);
        }
        // 替换前的文件
        File saveFile = FileUploadSupportActionService.downloadFile(importfileInfoDTO.getUrl(), importfileInfoDTO.getExt(), this.octTempFilePath);
        try {
            boolean checked = ExcelHandleUtil.validateFirstRow(saveFile, fileImportTaskDTO.getImportConfConfig().getImportColumnNames());
            if (!checked) {
                throw new BusinessException(MessageCode.IMPORT_EXCEL_HEAD_NOT_MATCH);
            }
            insertTask(fileImportTaskDTO);

            FileImportTaskSubmitEvent event = new FileImportTaskSubmitEvent();
            event.setTaskId(fileImportTaskDTO.getId());
            event.setTags(Constants.FILE_IMPORT_TASK_SUBMIT_MQ_TAG);
            event.setTenantId(String.valueOf(fileImportTaskDTO.getTenantId()));
            event.setBizKey(String.valueOf(fileImportTaskDTO.getId()));

            rocketMQEventPublish.publish(event);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        } finally {
            if (saveFile != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(saveFile);
            }
        }
        return fileImportTaskDTO;
    }

    private void insertTask(FileImportTaskDTO fileImportTaskDTO) {
        FileImportTaskPO po = fileImportTaskAssembler.toPO(fileImportTaskDTO);
        fileImportTaskMapper.insert(po);
        fileImportTaskDTO.setId(po.getId());
    }


    @IgnoreTenant
    public void executeTask(Long id) {
        XxlJobHelper.log("执行任务开始 id={}", id);
        log.info("执行任务开始 id={}", id);
        FileImportTaskDTO fileImportTaskDTO = fileImportTaskAssembler.PO2DTO(fileImportTaskMapper.selectById(id));
        if (Objects.isNull(fileImportTaskDTO)) {
            log.info("任务不存在 id={}", id);
            XxlJobHelper.log("任务不存在 id={}", id);
            return;
        }
        try {
            Map<String, CommonAbstractImportStrategy> businessStrategyMap = SpringUtil.getBeansOfType(CommonAbstractImportStrategy.class);
            // 遍历businessCode一致的处理类进行处理
            // 查询下businessCode 是否是子业务场景 子业务场景拿父业务场景的businessCode
            String compareBusinessCode = fileImportTaskDTO.getBusinessCode();
            FileImportConfDTO fileImportConfDTO = fileImportConfQueryService.queryBySubBusinessCode(fileImportTaskDTO.getSystemId(), 1L, fileImportTaskDTO.getBusinessCode());
            if (Objects.nonNull(fileImportConfDTO)) {
                compareBusinessCode = fileImportConfDTO.getBusinessCode();
            }
            String finalCompareBusinessCode = compareBusinessCode;
            CommonAbstractImportStrategy businessStrategy = businessStrategyMap.values()
                .stream()
                .filter(strategy -> strategy.getBusinessCode().getCode().equals(finalCompareBusinessCode))
                .findFirst()
                .orElseThrow(() -> new BusinessException(MessageCode.TASK_ERROR_NOT_STRATEGY));
            businessStrategy.export(fileImportTaskDTO);
            XxlJobHelper.log("执行任务成功 id={}", id);
            log.info("执行任务成功 id={}", id);
        } catch (LockException e) {
            log.error("执行任务失败,锁异常");
        } catch (Exception e) {
            // 更新任务为失败状态 增加执行次数
            log.error("执行任务失败", e);
            XxlJobHelper.log("执行任务失败 id={}", id);
            LambdaUpdateWrapper lambdaUpdateWrapper = new UpdateWrapper<FileImportTaskPO>().lambda()
                .eq(FileImportTaskPO::getId, id)
                .set(FileImportTaskPO::getTaskStatus, TaskStatusEnum.FAIL.getCode())
                .set(FileImportTaskPO::getRemark, StrUtil.sub(e.getMessage(), 0, 2048));
            fileImportTaskMapper.update(lambdaUpdateWrapper);
        }
    }

    @IgnoreTenant
    public void retry(Long id) {
        log.info("重试导入任务,任务id:{}", id);
        executeTask(id);
    }
}

