package cn.genn.trans.support.fms.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.FeiShuOpenApiClient;
import cn.genn.trans.support.fms.interfaces.dto.FileOcrResult;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.interfaces.enums.FileBusinessCodeEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Date: 2024/5/29
 * @Author: kangjian
 */
@Service
@Slf4j
public class FileOcrService {

    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;

    // 创建一个每秒放入10个令牌的限流器
    private RateLimiter rateLimiter = RateLimiter.create(10);

    /**
     * 新增Ocr识别文件类型，开发内容如下：
     * 1. FeiShuOpenApiClient 新增识别文件的方法 例如 ocrOfIdCard
     * 2. FileBusinessCodeEnum枚举中配置新增的文件类型，和第一步新增的识别方法名称，以下方法中反射获取对应文件的识别方法
     *
     * @param fileKey
     * @param businessCode
     * @return
     */
    public FileOcrResult ocrRecognize(String fileKey, FileBusinessCodeEnum businessCode) {
        // 限流10QPS 获取令牌
        boolean acquire = rateLimiter.tryAcquire(500, TimeUnit.MILLISECONDS);
        if (!acquire) {
            throw new BusinessException(MessageCode.OCR_LIMIT_ERROR);
        }
        FileInfo fileInfo = fileStorageService.getFileInfoByFileKey(fileKey);
        if (Objects.isNull(fileInfo)) {
            fileInfo = new FileInfo();
            FileUploadBucketConfigDTO bucketConfig = fileUploadSupportActionService.initPlatformConfig(9999L);
            fileInfo.setPlatform(bucketConfig.getPlatform());
            fileInfo.setFilename(fileKey);
            String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetSecond(new Date(), 60), null);
            fileInfo.setUrl(presignedUrl);
            if (fileKey.indexOf("/") < 0) {
                throw new BusinessException(MessageCode.FILE_NOT_EXIST);
            }
            fileInfo.setFilename(fileKey.substring(fileKey.indexOf('/') + 1));
        } else {
            // 获取外链
            fileUploadSupportActionService.addPlatformConfig(CurrentUserHolder.getTenantId());
            String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetSecond(new Date(), 60), null);
            fileInfo.setUrl(presignedUrl);
        }
        try {
            Class<FeiShuOpenApiClient> feiShuOpenApiClientClass = FeiShuOpenApiClient.class;
            FeiShuOpenApiClient feiShuOpenApiClient = feiShuOpenApiClientClass.newInstance();
            Object invoke = feiShuOpenApiClientClass.getMethod(businessCode.getOcrMethodName(), FileInfo.class)
                .invoke(feiShuOpenApiClient, fileInfo);
            return JSONUtil.toBean(JSONUtil.toJsonStr(invoke), FileOcrResult.class);
        } catch (Exception e) {
            log.error("ocr recognize error!", e);
        }
        throw new BusinessException(MessageCode.OCR_ERROR);
    }
}
