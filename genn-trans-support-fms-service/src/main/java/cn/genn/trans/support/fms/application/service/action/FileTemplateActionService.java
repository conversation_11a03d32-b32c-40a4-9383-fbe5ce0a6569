package cn.genn.trans.support.fms.application.service.action;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.application.assembler.FileInfoAssembler;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.file.InputStreamFileWrapper;
import cn.genn.trans.support.fms.infrastructure.config.FileBusinessConfigProperties;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.FreemarkerUtil;
import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.command.FileUploadImageOfBase64Command;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Date: 2024/5/22
 * @Author: kangjian
 */
@Service
@Slf4j
public class FileTemplateActionService {

    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileInfoAssembler fileInfoAssembler;

    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    private FileBusinessConfigProperties fileBusinessConfigProperties;

    public FileTemplateActionService(FileBusinessConfigProperties fileBusinessConfigProperties) {
        this.fileBusinessConfigProperties = fileBusinessConfigProperties;
    }


    /**
     * 没有集合的导出
     *
     * @param ssoUserAuthInfo
     * @param fileKey
     * @param simpleParams
     * @param cellRangeAddressParam
     * @return
     */
    public FileInfoDTO generateFileTemplateOfSimpleParam(SsoUserAuthInfoDTO ssoUserAuthInfo, String fileKey, List<FileTemplateCreateCommand.SimpleParam> simpleParams, List<FileTemplateCreateCommand.CellRangeAddress> cellRangeAddressParam) {
        FileInfoDTO fileInfo = fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);
        // 无需进行占位符替换
        if (CollectionUtil.isEmpty(simpleParams) && CollectionUtil.isEmpty(cellRangeAddressParam)) {
            return fileInfo;
        }
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(simpleParams)) {
            simpleParams.forEach(param -> map.put(param.getPlaceholder(), param.getValue()));
        }
        // 区分word excel pdf的处理
        fileInfo = templateGenerate(fileInfo, ssoUserAuthInfo, map, cellRangeAddressParam);
        // 获取外链
        fileInfo = fileUploadSupportActionService.getFileInfoByFileKey(fileInfo.getFileKey(), ssoUserAuthInfo);
        return fileInfo;
    }

    /**
     * 有集合的导出
     * 模板文件上传在 template-generate 下
     *
     * @param ssoUserAuthInfo
     * @param fileKey
     * @return
     */
    public FileInfoDTO generateFileTemplateOfMultipleParam(SsoUserAuthInfoDTO ssoUserAuthInfo, String fileKey, Map<Integer, Map<String, Object>> map) {
        FileInfoDTO fileInfo = fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);
        // 区分word excel pdf的处理
        fileInfo = templateGenerateOfMultipleParam(fileInfo, ssoUserAuthInfo, map);
        // 获取外链
        fileInfo = fileUploadSupportActionService.getFileInfoByFileKey(fileInfo.getFileKey(), ssoUserAuthInfo);
        return fileInfo;
    }

    private FileInfoDTO templateGenerateOfMultipleParam(FileInfoDTO fileTemplateInfo, SsoUserAuthInfoDTO ssoUserAuthInfo, Map<Integer, Map<String, Object>> map) {
        FileInfoDTO fileInfo = null;
        // 替换参数后输出的文件目录
        String outputFileUrl = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileTemplateInfo.getExt();
        String templateFile = fileTemplateInfo.getUrl();
        // 替换前的文件
        File saveFile = FileUploadSupportActionService.downloadFile(templateFile, fileTemplateInfo.getExt(), this.octTempFilePath);
        try (InputStream templateInputStream = FileUtils.openInputStream(saveFile);
             FileOutputStream fos = new FileOutputStream(outputFileUrl)) {
            // 创建文件
            FileUtils.forceMkdirParent(new File(outputFileUrl));

            // 将模板输入流保存到临时文件
            File tempTemplateFile = new File(outputFileUrl);
            FileUtils.copyInputStreamToFile(templateInputStream, tempTemplateFile);

            // 读取模板并替换占位符 模板的路径
            // 获取map的key有几个 就替换几个sheet
            int size = map.keySet().size();
            Integer[] sheetNum = new Integer[size];
            for (int i = 0; i < size; i++) {
                sheetNum[i] = i;
            }
            TemplateExportParams params = new TemplateExportParams(outputFileUrl, sheetNum);
            Workbook workbook = ExcelExportUtil.exportExcel(map, params);
            workbook.write(fos);
            // 上传文件
            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(toInputStream(workbook));
            inputStreamFileWrapper.setName(fileTemplateInfo.getOriginalFilename());
            fileInfo = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "template-generate", null, null);

        } catch (Exception e) {
            log.error("handle template error: " + e.getMessage(), e);
            throw new BusinessException(MessageCode.FILE_STREAM_READ_ERROR);
        } finally {
            if (saveFile != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(saveFile);
            }
            if (outputFileUrl != null) {
                // 删除临时模板文件
                FileUtils.deleteQuietly(new File(outputFileUrl));
            }
        }
        return fileInfo;
    }


    public FileInfoDTO handleWord(SsoUserAuthInfoDTO ssoUserAuthInfo, FileInfoDTO fileTemplateInfo, Map<String, Object> map) {
        FileInfoDTO fileInfo = null;
        String fileUrl = null;
        try (XWPFDocument doc = WordExportUtil.exportWord07(fileTemplateInfo.getUrl(), map);
             FileOutputStream fos = new FileOutputStream(fileUrl = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileTemplateInfo.getExt());
             InputStream inputStream = FileUtils.openInputStream(new File(fileUrl))) {
            // 创建文件
            FileUtils.forceMkdirParent(new File(fileUrl));
            doc.write(fos);

            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(inputStream);
            inputStreamFileWrapper.setName(fileTemplateInfo.getOriginalFilename());
            String fileExtension = fileInfo.getExt() != null ? fileInfo.getExt().toLowerCase() : "";
            inputStreamFileWrapper.setContentType("doc".equals(fileExtension) ? "application/msword" : "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            fileInfo = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "template-generate", null, null);

        } catch (Exception e) {
            log.error("handle template error: " + e.getMessage(), e);
            throw new BusinessException(MessageCode.FILE_STREAM_READ_ERROR);
        } finally {
            if (fileUrl != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(new File(fileUrl));
            }
        }
        return fileInfo;
    }

    public FileInfoDTO handlePdf(SsoUserAuthInfoDTO ssoUserAuthInfo, FileInfoDTO fileTemplateInfo, Map<String, Object> dataMap) {
        //表单域
        PdfStamper ps = null;
        PdfReader reader = null;
        FileInfoDTO fileInfo = null;
        String outputFileUrl = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileTemplateInfo.getExt();
        String templateFile = fileTemplateInfo.getUrl();
        File saveFile = FileUploadSupportActionService.downloadFile(templateFile, fileTemplateInfo.getExt(), this.octTempFilePath);
        try (OutputStream os = new FileOutputStream(outputFileUrl);
             InputStream ins = FileUtils.openInputStream(saveFile);
             InputStream inputStream = FileUtils.openInputStream(new File(outputFileUrl))) {
            //读取pdf表单
            reader = new PdfReader(ins);
            //根据表单生成一个新的pdf文件
            ps = new PdfStamper(reader, os);
            //获取pdf表单
            AcroFields form = ps.getAcroFields();
            //给表单中添加中文字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            form.addSubstitutionFont(bf);
            //处理文本
            if (dataMap != null && !dataMap.isEmpty()) {
                for (String key : dataMap.keySet()) {
                    //处理空值
                    if (dataMap.get(key) == null || StringUtils.isEmpty(dataMap.get(key).toString())) {
                        dataMap.put(key, "");
                    }
                    //处理文本
                    form.setField(key, dataMap.get(key).toString());
                }
            }

            ps.setFormFlattening(true);
            ps.close();

            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(inputStream);
            inputStreamFileWrapper.setName(fileTemplateInfo.getOriginalFilename());
            inputStreamFileWrapper.setContentType("application/pdf");
            fileInfo = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "template-generate", null, null);
        } catch (Exception e) {
            log.error("handle pdf template error: ", e);
            throw new BusinessException(MessageCode.FILE_STREAM_READ_ERROR);
        } finally {
            if (outputFileUrl != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(new File(outputFileUrl));
            }
            try {
                if (reader != null) {
                    reader.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                log.error("PdfStamper or PdfReader close error ", e);
            }
        }
        return fileInfo;
    }

    public FileInfoDTO templateGenerate(FileInfoDTO fileInfo, SsoUserAuthInfoDTO ssoUserAuthInfo, Map<String, Object> map, List<FileTemplateCreateCommand.CellRangeAddress> cellRangeAddressParam) {
        String fileExtension = fileInfo.getExt() != null ? fileInfo.getExt().toLowerCase() : "";
        switch (fileExtension) {
            case "docx":
            case "doc":
                try {
                    // 对word文档的处理
                    return handleWord(ssoUserAuthInfo, fileInfo, map);
                } catch (Exception e) {
                    // 异常处理逻辑
                    log.error("处理Word文档时发生错误", e);
                }
                break;
            case "xlsx":
            case "xls":
                // 对excel文档的处理
                try {
                    // TODO: 实现excel处理逻辑
                    return handleExcel(ssoUserAuthInfo, fileInfo, map, cellRangeAddressParam);
                } catch (Exception e) {
                    // 异常处理逻辑
                    log.error("处理Excel文档时发生错误", e);
                }
                break;
            case "pdf":
                // 对pdf文档的处理
                try {
                    // TODO: 实现PDF处理逻辑
                    return handlePdf(ssoUserAuthInfo, fileInfo, map);
                } catch (Exception e) {
                    // 异常处理逻辑
                    log.error("处理PDF文档时发生错误", e);
                }
                break;
            default:
                log.warn("不支持的文件类型: " + fileExtension);
                break;
        }
        return fileInfo;
    }

    public FileInfoDTO handleExcel(SsoUserAuthInfoDTO ssoUserAuthInfo, FileInfoDTO fileTemplateInfo, Map<String, Object> map, List<FileTemplateCreateCommand.CellRangeAddress> cellRangeAddressParam) {
        FileInfoDTO fileInfo = null;
        // 替换参数后输出的文件目录
        String outputFileUrl = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileTemplateInfo.getExt();
        String templateFile = fileTemplateInfo.getUrl();
        // 替换前的文件
        File saveFile = FileUploadSupportActionService.downloadFile(templateFile,
            fileTemplateInfo.getExt(), this.octTempFilePath);
        try (InputStream templateInputStream = FileUtils.openInputStream(saveFile);
             FileOutputStream fos = new FileOutputStream(outputFileUrl)) {
            // 创建文件
            FileUtils.forceMkdirParent(new File(outputFileUrl));

            // 将模板输入流保存到临时文件
            File tempTemplateFile = new File(outputFileUrl);
            FileUtils.copyInputStreamToFile(templateInputStream, tempTemplateFile);

            // 读取模板并替换占位符 模板的路径
            log.info("处理占位符 start");
            TemplateExportParams params = new TemplateExportParams(outputFileUrl, true);
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);
            log.info("处理占位符 end");
            // 处理完占位符替换后 处理列的选项填充
            if (CollectionUtils.isNotEmpty(cellRangeAddressParam)) {
                log.info("处理列的选项填充 start");
                handleCellRangeAddress(workbook, cellRangeAddressParam);
                log.info("处理列的选项填充 end");
            }
            // 导出 Excel 文件（具体文件路径可修改）
            /*try (FileOutputStream fileOut = new FileOutputStream("output.xlsx")) {
                workbook.write(fileOut);
            } catch (Exception e) {
                e.printStackTrace();
            }*/
            workbook.write(fos);
            // 上传文件
            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(toInputStream(workbook));
            inputStreamFileWrapper.setName(fileTemplateInfo.getOriginalFilename());
            inputStreamFileWrapper.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            fileInfo = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "template-generate", null, null);

        } catch (Exception e) {
            log.error("handle template error: " + e.getMessage(), e);
            throw new BusinessException(MessageCode.FILE_STREAM_READ_ERROR);
        } finally {
            if (saveFile != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(saveFile);
            }
            if (outputFileUrl != null) {
                // 删除临时模板文件
                FileUtils.deleteQuietly(new File(outputFileUrl));
            }
        }
        return fileInfo;
    }

    private void handleCellRangeAddress(Workbook workbook, List<FileTemplateCreateCommand.CellRangeAddress> cellRangeAddressParam) {
        // 遍历所有的sheet
        for (FileTemplateCreateCommand.CellRangeAddress cellRangeAddress : cellRangeAddressParam) {
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                // 查找包含列名的行
                Row headerRow = sheet.getRow(0);
                if (headerRow == null) {
                    continue;
                }

                int columnIdx = -1;
                // 遍历标题行的所有单元格，查找目标列名
                for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
                    Cell headerCell = headerRow.getCell(cellIndex);
                    if (headerCell != null && headerCell.getStringCellValue().equals(cellRangeAddress.getRowName())) {
                        columnIdx = cellIndex;
                        break;
                    }
                }

                // 如果找到了目标列名，则设置数据验证（下拉框）
                if (columnIdx != -1) {
                    DataValidationHelper helper = sheet.getDataValidationHelper();
                    DataValidationConstraint constraint = helper.createExplicitListConstraint(cellRangeAddress.getOptions().toArray(new String[0]));
                    CellRangeAddressList addressList = new CellRangeAddressList(cellRangeAddress.getFirstRow(), cellRangeAddress.getLastRow(), columnIdx, columnIdx);
                    DataValidation validation = helper.createValidation(constraint, addressList);
                    sheet.addValidationData(validation);
                }
            }
        }

    }

    private InputStream toInputStream(Workbook workbook) {
        InputStream in = null;
        ByteArrayOutputStream out = null;
        try {
            out = new ByteArrayOutputStream();
            workbook.write(out);
            byte[] bookByteAry = out.toByteArray();
            in = new ByteArrayInputStream(bookByteAry);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return in;
    }

    /**
     * 按模板生成图片
     *
     * @param ssoUserAuthInfo
     * @param simpleParams
     * @return
     */
    public FileInfoDTO generateFileTemplateOfPictureTemplate(SsoUserAuthInfoDTO ssoUserAuthInfo, String businessCode, String fileName, List<FileTemplateCreateCommand.SimpleParam> simpleParams) {
        // 无需进行占位符替换
        if (CollectionUtil.isEmpty(simpleParams)) {
            throw new BusinessException(MessageCode.FILE_TEMPLATE_SIMPLE_PARAM_EMPTY);
        }
        Map<String, Object> map = new HashMap<>();
        simpleParams.forEach(param -> map.put(param.getPlaceholder(), param.getValue()));
        // 区分word excel pdf的处理
        FileInfoDTO fileInfo = templatePicGenerateByMap(fileName, businessCode, ssoUserAuthInfo, map);
        return fileInfo;
    }

    @SneakyThrows
    private FileInfoDTO templatePicGenerateByMap(String fileName, String businessCode, SsoUserAuthInfoDTO ssoUserAuthInfo, Map<String, Object> map) {
        // 大宗磅单图片 pound.ftl
        String ftlTemp = fileBusinessConfigProperties.getImageTemplate()
            .getConfig().get(businessCode);
        if (Objects.isNull(ftlTemp)) {
            throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
        }
        String baseCode = FreemarkerUtil.ftiToHtmlToImg(ftlTemp, map, 16, 1400);
        //            ImageUtil.base64ToImage2(baseCode);
        FileUploadImageOfBase64Command command = FileUploadImageOfBase64Command.builder()
            .base64String(baseCode)
            .businessCode(businessCode)
            .objectId(null)
            .objectType(null)
            .originalFilename(fileName)
            .tenantId(ssoUserAuthInfo.getTenantId())
            .systemId(ssoUserAuthInfo.getSystemId())
            .userId(ssoUserAuthInfo.getUserId())
            .username(ssoUserAuthInfo.getUsername())
            .build();
        return fileUploadSupportActionService.handleImageOfBase64(ssoUserAuthInfo, command);
    }

}
