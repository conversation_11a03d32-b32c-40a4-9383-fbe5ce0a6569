package cn.genn.trans.support.fms.application.service.action;

import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileUploadDetailMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadDetailPO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.hash.HashInfo;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.FilePartInfo;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 上传文件记录 自动触发
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileUploadDetailSupportActionService extends ServiceImpl<FileUploadDetailMapper, FileUploadDetailPO> implements FileRecorder {


    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 保存文件信息到数据库
     */
    @SneakyThrows
    @Override
    public boolean save(FileInfo info) {
        // 生成uuid
        info.setFileKey(UUID.fastUUID().toString());
        FileUploadDetailPO detail = toFileUploadDetailPO(info);
        boolean b = save(detail);
        if (b) {
            info.setId(detail.getId());
        }
        return b;
    }

    /**
     * 更新文件记录，可以根据文件 ID 或 URL 来更新文件记录，
     * 主要用在手动分片上传文件-完成上传，作用是更新文件信息
     */
    @SneakyThrows
    @Override
    public void update(FileInfo info) {
        FileUploadDetailPO detail = toFileUploadDetailPO(info);
        QueryWrapper<FileUploadDetailPO> qw = new QueryWrapper<FileUploadDetailPO>()
            .eq(detail.getUrl() != null, FileUploadDetailPO.COL_URL, detail.getUrl())
            .eq(detail.getId() != null, FileUploadDetailPO.COL_ID, detail.getId());
        update(detail, qw);
    }

    /**
     * 根据 url 查询文件信息
     */
    @SneakyThrows
    @Override
    public FileInfo getByUrl(String url) {
        FileUploadDetailPO po = getOne(new QueryWrapper<FileUploadDetailPO>().eq(FileUploadDetailPO.COL_URL, url));
        if (Objects.isNull(po)) {
            return null;
        }
        return toFileInfo(po);
    }

    /**
     * 根据 url 查询文件信息
     */
    @SneakyThrows
    @Override
    @IgnoreTenant
    public FileInfo getByFileKey(String fileKey) {
        FileUploadDetailPO po = getOne(new QueryWrapper<FileUploadDetailPO>().eq(FileUploadDetailPO.COL_FILE_KEY, fileKey));
        if (Objects.isNull(po)) {
            return null;
        }
        return toFileInfo(po);
    }

    /**
     * 根据 url 删除文件信息
     */
    @Override
    public boolean delete(String url) {
        remove(new QueryWrapper<FileUploadDetailPO>().eq(FileUploadDetailPO.COL_URL, url));
        return true;
    }

    /**
     * 保存文件分片信息
     *
     * @param filePartInfo 文件分片信息
     */
    @Override
    public void saveFilePart(FilePartInfo filePartInfo) {

    }

    /**
     * 删除文件分片信息
     *
     * @param uploadId
     */
    @Override
    public void deleteFilePartByUploadId(String uploadId) {

    }

    /**
     * 将 FileInfoDTO 转为 FileUploadDetailPO
     */
    public FileUploadDetailPO toFileUploadDetailPO(FileInfo info) throws JsonProcessingException {
        FileUploadDetailPO detail = BeanUtil.copyProperties(
            info, FileUploadDetailPO.class, "metadata", "userMetadata", "thMetadata", "thUserMetadata", "attr", "hashInfo");
        // 这里手动获 元数据 并转成 json 字符串，方便存储在数据库中
        detail.setMetadata(valueToJson(info.getMetadata()));
        detail.setUserMetadata(valueToJson(info.getUserMetadata()));
        detail.setThMetadata(valueToJson(info.getThMetadata()));
        detail.setThUserMetadata(valueToJson(info.getThUserMetadata()));
        // 这里手动获 取附加属性字典 并转成 json 字符串，方便存储在数据库中
        detail.setAttr(valueToJson(info.getAttr()));
        // 这里手动获 哈希信息 并转成 json 字符串，方便存储在数据库中
        detail.setHashInfo(valueToJson(info.getHashInfo()));
        return detail;
    }

    /**
     * 将 FileUploadDetailPO 转为 FileInfoDTO
     */
    public FileInfo toFileInfo(FileUploadDetailPO detail) throws JsonProcessingException {
        FileInfo info = BeanUtil.copyProperties(
            detail, FileInfo.class, "metadata", "userMetadata", "thMetadata", "thUserMetadata", "attr", "hashInfo");

        // 这里手动获取数据库中的 json 字符串 并转成 元数据，方便使用
        info.setMetadata(jsonToMetadata(detail.getMetadata()));
        info.setUserMetadata(jsonToMetadata(detail.getUserMetadata()));
        info.setThMetadata(jsonToMetadata(detail.getThMetadata()));
        info.setThUserMetadata(jsonToMetadata(detail.getThUserMetadata()));
        // 这里手动获取数据库中的 json 字符串 并转成 附加属性字典，方便使用
        info.setAttr(jsonToDict(detail.getAttr()));
        // 这里手动获取数据库中的 json 字符串 并转成 哈希信息，方便使用
        info.setHashInfo(jsonToHashInfo(detail.getHashInfo()));
        return info;
    }

    /**
     * 将指定值转换成 json 字符串
     */
    public String valueToJson(Object value) throws JsonProcessingException {
        if (value == null) return null;
        return objectMapper.writeValueAsString(value);
    }

    /**
     * 将 json 字符串转换成元数据对象
     */
    public Map<String, String> jsonToMetadata(String json) throws JsonProcessingException {
        if (StrUtil.isBlank(json)) return null;
        return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {
        });
    }

    /**
     * 将 json 字符串转换成字典对象
     */
    public Dict jsonToDict(String json) throws JsonProcessingException {
        if (StrUtil.isBlank(json)) return null;
        return objectMapper.readValue(json, Dict.class);
    }

    /**
     * 将 json 字符串转换成哈希信息对象
     */
    public HashInfo jsonToHashInfo(String json) throws JsonProcessingException {
        if (StrUtil.isBlank(json)) return null;
        return objectMapper.readValue(json, HashInfo.class);
    }
}

