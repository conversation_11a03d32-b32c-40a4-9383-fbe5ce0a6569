package cn.genn.trans.support.fms.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.application.assembler.FileInfoAssembler;
import cn.genn.trans.support.fms.application.service.query.FileUploadBucketConfigQueryService;
import cn.genn.trans.support.fms.application.service.query.FileUploadBusinessConfigQueryService;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageProperties;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.FileStorageServiceBuilder;
import cn.genn.trans.support.fms.core.constant.Constant;
import cn.genn.trans.support.fms.core.file.InputStreamFileWrapper;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.tika.DefaultTikaFactory;
import cn.genn.trans.support.fms.core.upload.FileUploadBusinessCommonData;
import cn.genn.trans.support.fms.infrastructure.config.FileBusinessConfigProperties;
import cn.genn.trans.support.fms.infrastructure.config.TosBucketConfigProperties;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.FileSizeUtil;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExternalLinkTransferCommand;
import cn.genn.trans.support.fms.interfaces.command.FileUploadImageOfBase64Command;
import cn.genn.trans.support.fms.interfaces.command.TosTempStsConfigCreateCommand;
import cn.genn.trans.support.fms.interfaces.dto.*;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.wisewe.docx4j.convert.builder.document.DocumentConvertType;
import cn.wisewe.docx4j.convert.builder.document.DocumentConverter;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.volcengine.model.request.AssumeRoleRequest;
import com.volcengine.model.response.AssumeRoleResponse;
import com.volcengine.service.sts.ISTSService;
import com.volcengine.service.sts.impl.STSServiceImpl;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

import static feign.Util.UTF_8;

/**
 * 上传文件初始化相关配置的处理
 *
 * @Date: 2024/5/13
 * @Author: kangjian
 */
@Service
@Slf4j
public class FileUploadSupportActionService {

    public static final String WATERMARK_PREFIX = "image/watermark,";
    public static final String IMAGE = "image";
    public static final String TEXT_KEY = "text_";
    public static final String SIZE_KEY = "size_";
    public static final String FILL_KEY = "fill_";
    public static final String COLOR_KEY = "color_";
    public static final String TYPE_KEY = "type_";
    public static final String X_IMAGE_PROCESS = "x-image-process";

    @Resource
    private FileUploadBucketConfigQueryService bucketConfigQueryService;

    @Resource
    private FileUploadBusinessConfigQueryService businessConfigQueryService;

    @Resource
    private FileStorageService fileStorageService;

    private FileBusinessConfigProperties fileBusinessConfigProperties;

    @Resource
    private FileInfoAssembler fileInfoAssembler;
    @Resource
    private TosBucketConfigProperties tosBucketConfigProperties;

    public FileUploadSupportActionService(FileBusinessConfigProperties fileBusinessConfigProperties) {
        this.fileBusinessConfigProperties = fileBusinessConfigProperties;
    }

    @SneakyThrows
    public FileInfoDTO uploadFile(MultipartFile file, SsoUserAuthInfoDTO ssoUserAuthInfo, String businessCode, String objectId, String objectType) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(ssoUserAuthInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), ssoUserAuthInfo.getTenantId(), businessCode);
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), 1L, businessCode);
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        // 校验文件类型 文件大小
        checkFileLimitRule(businessConfig, file);
        addPlatformConfig(bucketConfig);

        FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(ssoUserAuthInfo.getTenantId())
            .systemId(ssoUserAuthInfo.getSystemId()).businessCode(businessCode).createUserId(ssoUserAuthInfo.getUserId())
            .createUserName(ssoUserAuthInfo.getUsername()).build();

        FileInfo fileInfo = fileStorageService.of(file).setPlatform(bucketConfig.getPlatform())
            .setPath(genatePath(ssoUserAuthInfo.getTenantId(), businessConfig)).setObjectId(objectId).setObjectType(objectType)
            .setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
            .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
            .setHashCalculator(MessageDigest.getInstance("SHA-384")).setBusinessCommonData(businessCommonData).upload();
        // 水印处理 根据数据分级对外链进行处理
        fileProcessing(fileInfo, businessConfig, ssoUserAuthInfo);
        return fileInfoAssembler.entity2DTO(fileInfo);
    }

    @SneakyThrows
    public FileInfoDTO uploadFile(MultipartFile file, String fileName, String domainName, SsoUserAuthInfoDTO ssoUserAuthInfo, String businessCode,
                                  String objectId, String objectType) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(ssoUserAuthInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), ssoUserAuthInfo.getTenantId(), businessCode);
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), 1L, businessCode);
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        // 校验文件类型 文件大小
        checkFileLimitRule(businessConfig, file);
        addPlatformConfig(bucketConfig);

        FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(ssoUserAuthInfo.getTenantId())
            .systemId(ssoUserAuthInfo.getSystemId()).businessCode(businessCode)
            .createUserId(ssoUserAuthInfo.getUserId())
            .createUserName(ssoUserAuthInfo.getUsername()).build();

        FileInfo fileInfo = fileStorageService.of(file)
            .setPlatform(bucketConfig.getPlatform())
            .setName(fileName)
            .setSaveFilename(fileName)
            .setPath(genatePath(businessConfig))
            .setObjectId(objectId)
            .setObjectType(objectType)
            .setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
            .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
            .setHashCalculator(MessageDigest.getInstance("SHA-384")).setBusinessCommonData(businessCommonData)
            .upload();
        fileInfo.setUrl(domainName + "/" + fileInfo.getPath() + fileName);
        return fileInfoAssembler.entity2DTO(fileInfo);
    }

    @SneakyThrows
    public FileInfoDTO uploadFile(InputStreamFileWrapper inputStreamFileWrapper, SsoUserAuthInfoDTO ssoUserAuthInfo, String businessCode, String objectId, String objectType) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(ssoUserAuthInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), ssoUserAuthInfo.getTenantId(), businessCode);
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), 1L, businessCode);
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        addPlatformConfig(bucketConfig);
        FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(ssoUserAuthInfo.getTenantId())
            .systemId(ssoUserAuthInfo.getSystemId()).businessCode(businessCode).createUserId(ssoUserAuthInfo.getUserId())
            .createUserName(ssoUserAuthInfo.getUsername()).build();

        FileInfo fileInfo = fileStorageService.of(inputStreamFileWrapper).setPlatform(bucketConfig.getPlatform())
            .setPath(genatePath(ssoUserAuthInfo.getTenantId(), businessConfig)).setObjectId(objectId).setObjectType(objectType)
            .setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
            .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
            .setHashCalculator(MessageDigest.getInstance("SHA-384")).setBusinessCommonData(businessCommonData).upload();
        // 水印处理 根据数据分级对外链进行处理
        fileProcessing(fileInfo, businessConfig, ssoUserAuthInfo);
        return fileInfoAssembler.entity2DTO(fileInfo);
    }

    @SneakyThrows
    public FileInfo uploadFileForExport(InputStream inputStream, Long taskId, String businessCode, Long tenantId, Long systemId, String fileName, String templateType) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(tenantId);
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(systemId, tenantId, businessCode);
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(systemId, 1L, businessCode);
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        addPlatformConfig(bucketConfig);

        FileUploadBusinessCommonData businessCommonData = FileUploadBusinessCommonData.builder().tenantId(tenantId).systemId(systemId)
            .businessCode(businessCode).createUserId(0L).createUserName("system").build();
        // 文件名-yyyy_mmd_dd_hh_mm_ss.xlsx
        FileInfo fileInfo = fileStorageService.of(inputStream).setSaveFilename(null).setOriginalFilename(fileName)
            .setPlatform(bucketConfig.getPlatform()).setPath(genatePath(tenantId, businessConfig)).setObjectId(taskId)
            .setObjectType("ExportTaskId").setPlatform(bucketConfig.getPlatform()).setHashCalculatorMd5().setHashCalculatorSha256()
            .setHashCalculator(Constant.Hash.MessageDigest.MD2).setHashCalculator("SHA-512")
            .setHashCalculator(MessageDigest.getInstance("SHA-384")).setBusinessCommonData(businessCommonData).upload();
        // 水印处理 根据数据分级对外链进行处理
        fileProcessing(fileInfo, businessConfig, null);
        return fileInfo;
    }

    private void checkFileLimitRule(FileUploadBusinessConfigDTO businessConfig, MultipartFile file) {
        // 校验文件类型 文件大小
        Tika tika = new DefaultTikaFactory().getTika();
        try (InputStream inputStream = file.getInputStream()) {
            // 使用 Tika 检测 MIME 类型
            String mimeType = tika.detect(inputStream, file.getOriginalFilename());
            log.info("mimeType={}", mimeType);
            if (StrUtil.isNotBlank(businessConfig.getLimitContentType())) {
                // 按逗号分隔 如果mimeType存在在配置中则通过 不存在抛出异常
                Splitter.on(",").splitToList(businessConfig.getLimitContentType()).stream()
                    .filter(contentType -> contentType.equals(mimeType)).findAny()
                    .orElseThrow(() -> new BusinessException(MessageCode.MIME_TYPE_NOT_ALLOWED));
            }
            // 校验文件大小
            if (Objects.nonNull(businessConfig.getLimitFileSize()) && businessConfig.getLimitFileSize() > 0 && file.getSize() > businessConfig.getLimitFileSize()) {
                throw new BusinessException("上传文件大小不允许超过" + FileSizeUtil.getHumanReadableFileSize(businessConfig.getLimitFileSize()));
            }
        } catch (IOException e) {
            log.error("文件流读取异常 error={}", e.getMessage(), e);
            throw new BusinessException(MessageCode.FILE_STREAM_READ_ERROR);
        }

    }

    public void addPlatformConfig(FileUploadBucketConfigDTO bucketConfig) {
        //获得存储平台 List
        CopyOnWriteArrayList<FileStorage> list = fileStorageService.getFileStorageList();
        // 判断platform是否存在 存在无需增加 不存在进行增加存储平台
        if (list.stream().anyMatch(fileStorage -> fileStorage.getPlatform().equals(bucketConfig.getPlatform()))) {
            return;
        }

        // 兼容华为云和火山云
        if (bucketConfig.getEndPoint().startsWith("obs")) {
            FileStorageProperties.HuaweiObsConfig config = new FileStorageProperties.HuaweiObsConfig();
            config.setPlatform(bucketConfig.getPlatform());
            config.setAccessKey(bucketConfig.getAccessKey());
            config.setSecretKey(bucketConfig.getSecretKey());
            config.setEndPoint(bucketConfig.getEndPoint());
            config.setBucketName(bucketConfig.getBucketName());
            config.setDomain(bucketConfig.getDomain());
            config.setBasePath(bucketConfig.getBasePath());
            list.addAll(FileStorageServiceBuilder.buildHuaweiObsFileStorage(Collections.singletonList(config), null));
        } else if (bucketConfig.getEndPoint().startsWith("tos")) {
            FileStorageProperties.TosConfig config = new FileStorageProperties.TosConfig();
            config.setPlatform(bucketConfig.getPlatform());
            config.setAccessKey(bucketConfig.getAccessKey());
            config.setSecretKey(bucketConfig.getSecretKey());
            config.setEndPoint(bucketConfig.getEndPoint());
            config.setBucketName(bucketConfig.getBucketName());
            config.setDomain(bucketConfig.getDomain());
            config.setBasePath(bucketConfig.getBasePath());
            list.addAll(FileStorageServiceBuilder.buildTosFileStorage(Collections.singletonList(config), null));
        } else {
            throw new BusinessException(MessageCode.PLATFORM_NOT_SUPPORT);
        }

    }

    private String genatePath(Long tenantId, FileUploadBusinessConfigDTO businessConfig) {
        StringBuilder path = new StringBuilder();
        path.append("tenant_").append(tenantId).append("/");
        if (StrUtil.isNotBlank(businessConfig.getPath())) {
            path.append(businessConfig.getPath());
        }
        // 年月日归档
        if (StrUtil.isNotBlank(businessConfig.getFilingConfig())) {
            // yyyy/MM/dd/  yyyy/MM/  yyyy/
            path.append(DateUtil.format(new Date(), businessConfig.getFilingConfig()));
        }
        return path.toString();
    }

    private String genatePath(FileUploadBusinessConfigDTO businessConfig) {
        StringBuilder path = new StringBuilder();
        if (StrUtil.isNotBlank(businessConfig.getPath())) {
            path.append(businessConfig.getPath());
        }
        return path.toString();
    }

    public void fileProcessing(FileInfo fileInfo, FileUploadBusinessConfigDTO businessConfig, SsoUserAuthInfoDTO ssoUserAuthInfo) {
        Map<String, Object> map = new HashMap<>();
        // 判断是否是图片类型 进行水印处理
        if (StrUtil.isNotBlank(fileInfo.getContentType()) && fileInfo.getContentType()
            .startsWith(IMAGE) && StrUtil.isNotBlank(businessConfig.getWaterMarkConfig())) {
            StringBuilder urlContent = new StringBuilder();
            urlContent.append(WATERMARK_PREFIX);
            FileUploadWaterMarkConfigDTO waterMarkConfig = JSONUtil.toBean(businessConfig.getWaterMarkConfig(), FileUploadWaterMarkConfigDTO.class);
            urlContent.append(TEXT_KEY).append(getText(fileInfo, ssoUserAuthInfo, waterMarkConfig)).append(",");
            urlContent.append(SIZE_KEY).append(waterMarkConfig.getSize()).append(",");
            urlContent.append(FILL_KEY).append(waterMarkConfig.getFill()).append(",");
            urlContent.append(COLOR_KEY).append(waterMarkConfig.getColor()).append(",");
            urlContent.append(TYPE_KEY).append(waterMarkConfig.getType()).append(",");
            urlContent.append(waterMarkConfig.getExtra());
            map.put(X_IMAGE_PROCESS, urlContent.toString());
        }
        //生成 URL ，有效期为1 2级永久 3-5级限制时间
        // 火山云只支持7天最长
        Integer validTime = businessConfig.getPreSignedUrlValid();
        String presignedUrl;
        if (Objects.nonNull(validTime) && validTime > 0) {
            presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetSecond(new Date(), validTime), map);
        } else {
            // TODO 根据数据分级
            presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMonth(new Date(), 36), map);
        }
        fileInfo.setUrl(presignedUrl);
    }

    public FileInfoDTO getFileInfoByFileKey(String fileKey, SsoUserAuthInfoDTO ssoUserAuthInfo) {
        // TODO 数据分级可以增加数据权限的ACL控制
        FileInfo fileInfo = fileStorageService.getFileInfoByFileKey(fileKey);
        if (Objects.isNull(fileInfo)) {
            //物界文件适配展示
            FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(9999L);
            addPlatformConfig(bucketConfig);
            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            fileInfo = new FileInfo();
            fileInfo.setFilename(fileKey);
            fileInfo.setPlatform(bucketConfig.getPlatform());
            String outUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMonth(new Date(), 36), new HashMap<>());
            fileInfoDTO.setId(fileInfo.getId());
            fileInfoDTO.setFileKey(fileKey);
            fileInfoDTO.setUrl(outUrl);
            fileInfoDTO.setExt("jpeg");
            fileInfoDTO.setContentType("image/jpeg");
            return fileInfoDTO;
            //            throw new BusinessException(MessageCode.FILE_NOT_EXIST);
        }
        List<String> noteNeedTenantCheckOfbusinessCode = fileBusinessConfigProperties.getNoteNeedTenantCheckOfbusinessCode();
        if (CollectionUtil.isNotEmpty(noteNeedTenantCheckOfbusinessCode) && !noteNeedTenantCheckOfbusinessCode.contains(fileInfo.getBusinessCode())) {
            if (!Objects.equals(fileInfo.getTenantId(), ssoUserAuthInfo.getTenantId())) {
                throw new BusinessException(MessageCode.TENANT_NOT_MATCH);
            }
        }
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(fileInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        addPlatformConfig(bucketConfig);

        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(fileInfo.getSystemId(), fileInfo.getTenantId(), fileInfo.getBusinessCode());
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), 1L, fileInfo.getBusinessCode());
            if (Objects.isNull(businessConfig)) {
                log.info("租户的默认平台的业务场景配置不存在 取默认系统的业务场景配置");
                businessConfig = businessConfigQueryService.acquireBusinessConfig(1L, 1L, fileInfo.getBusinessCode());
                if (Objects.isNull(businessConfig)) {
                    log.info("业务场景配置不存在 需报错返回不了外链值 systemId={},tenantId={},businessCode={}", fileInfo.getSystemId(), fileInfo.getTenantId(), fileInfo.getBusinessCode());
                    throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
                }
            }
        }
        // 水印处理 根据数据分级对外链进行处理
        fileProcessing(fileInfo, businessConfig, ssoUserAuthInfo);
        return fileInfoAssembler.entity2DTO(fileInfo);
    }

    public List<FileInfo> getFileInfoByFileKeys(List<String> fileKeys, SsoUserAuthInfoDTO ssoUserAuthInfo) {
        return null;
    }


    public void addPlatformConfig(Long tenantId) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(tenantId);
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        addPlatformConfig(bucketConfig);
    }

    @SneakyThrows
    private String getText(FileInfo fileInfo, SsoUserAuthInfoDTO ssoUserAuthInfo, FileUploadWaterMarkConfigDTO waterMarkConfig) {
        // 如果配置中是固定值 直接返回
        if (StrUtil.isNotBlank(waterMarkConfig.getFixedText())) {
            return cn.hutool.core.codec.Base64.encodeUrlSafe(waterMarkConfig.getFixedText());
        }
        // 年-月-日-时-分-秒 操作人 手机尾号后四位；
        StringBuilder result = new StringBuilder();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timestamp = fileInfo.getCreateTime().format(format);
        String username = "";
        if (StrUtil.isNotBlank(ssoUserAuthInfo.getUsername())) {
            username = ssoUserAuthInfo.getUsername();
        }
        String phoneTail = "";
        if (StrUtil.isNotBlank(ssoUserAuthInfo.getTelephone())) {
            phoneTail = ssoUserAuthInfo.getTelephone().substring(ssoUserAuthInfo.getTelephone().length() - 4);
        }
        result.append(timestamp).append(" ").append(username).append(" ").append(phoneTail);
        return cn.hutool.core.codec.Base64.encodeUrlSafe(result.toString());
    }

    /**
     * 支持上传base64编码字符串的图片
     *
     * @return
     */
    public FileInfoDTO handleImageOfBase64(SsoUserAuthInfoDTO ssoUserAuthInfo, FileUploadImageOfBase64Command command) {
        FileInfoDTO fileInfo = null;
        try {
            ByteArrayInputStream stream = base64ToStream(command.getBase64String());
            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            inputStreamFileWrapper.setInputStream(stream);
            inputStreamFileWrapper.setName(command.getOriginalFilename());
            inputStreamFileWrapper.setContentType("image/*");
            fileInfo = this.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, command.getBusinessCode(), command.getObjectId(), command.getObjectType());
        } catch (Exception e) {
            log.error("handle image of base64 error ", e);
        }
        return fileInfo;
    }

    public static ByteArrayInputStream base64ToStream(String base64String) {
        byte[] decodedBytes = Base64.getDecoder().decode(base64String);
        return new ByteArrayInputStream(decodedBytes);
    }


    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    public FileInfoDTO externalLinkFileTransfer(SsoUserAuthInfoDTO ssoUserAuthInfo, FileExternalLinkTransferCommand command) {
        String savePath = this.octTempFilePath;
        // 对url文件进行下载到临时目录
        FileInfoDTO fileInfo = null;
        try {
            File saveFile = downloadFile(command.getUrl(), command.getExt(), savePath);
            if (Objects.isNull(saveFile)) {
                throw new BusinessException(MessageCode.FILE_TRANSFER_ERROR);
            }
            // 尝试从文件名中获取后缀名
            String fileName = saveFile.getName();
            int lastIndexOfDot = fileName.lastIndexOf('.');
            if (lastIndexOfDot > 0) {
                String extension = fileName.substring(lastIndexOfDot + 1);
                log.info("文件后缀名: " + extension);
            }
            log.info("文件已下载至: " + saveFile.getAbsolutePath());

            InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
            FileInputStream fileInputStream = new FileInputStream(saveFile);
            inputStreamFileWrapper.setInputStream(fileInputStream);
            inputStreamFileWrapper.setName(fileName);
            fileInfo = this.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, command.getBusinessCode(), command.getObjectId(), command.getObjectType());

            // 如果是word文档 转换为pdf进行存储
            try {
                if (fileInfo.getExt().equals("doc") || fileInfo.getExt().equals("docx")) {
                    String outputFilePath = this.octTempFilePath + fileName + ".pdf";
                    DocumentConverter.create()
                        .input(new FileInputStream(saveFile))
                        .output(new FileOutputStream(outputFilePath))
                        .convert(DocumentConvertType.PDF);
                    InputStreamFileWrapper inputStreamFileWrapperAfter = new InputStreamFileWrapper();
                    FileInputStream fileInputStreamAfter = new FileInputStream(outputFilePath);
                    inputStreamFileWrapperAfter.setInputStream(fileInputStreamAfter);
                    inputStreamFileWrapperAfter.setName(fileName + ".pdf");
                    inputStreamFileWrapperAfter.setContentType("application/pdf");
                    fileInfo = this.uploadFile(inputStreamFileWrapperAfter, ssoUserAuthInfo, command.getBusinessCode(), command.getObjectId(), command.getObjectType());
                    FileUtil.del(outputFilePath);
                }
            } catch (Exception e) {
                log.error("文件转存word转换为pdf失败 error ", e);
            }
            // 删除临时文件
            FileUtil.del(saveFile);
        } catch (Exception e) {
            log.error("handle externalLinkFileTransfer error ", e);
        }
        return fileInfo;
    }

    public static File downloadFile(String fileURL, String ext, String saveDir) {
        File file = null;
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                URL url = new URL(fileURL);
                HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
                // 设置请求方法
                httpURLConnection.setRequestMethod("GET");
                // 连接到服务器
                int responseCode = httpURLConnection.getResponseCode();
                // 检查响应码
                log.info("Response Code: " + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 获取文件大小
                    String fileName = "";
                    String disposition = httpURLConnection.getHeaderField("Content-Disposition");
                    String contentType = httpURLConnection.getContentType();
                    int contentLength = httpURLConnection.getContentLength();
                    if (disposition != null) {
                        // 尝试从Content-Disposition获取文件名
                        String[] names = disposition.split(";");
                        for (String name : names) {
                            if (name.trim().startsWith("filename")) {
                                fileName = URLDecoder.decode(name.substring(name.indexOf('=') + 1).trim().replace("\"", ""), UTF_8);
                                break;
                            }
                        }
                    }
                    // 如果没有从Content-Disposition获取文件名，则尝试从URL中获取
                    if (fileName.isEmpty() && Objects.nonNull(ext)) {
                        fileName = UUID.randomUUID() + "." + ext;
                    }
                    if (fileName.isEmpty()) {
                        fileName = url.getFile().substring(url.getFile().lastIndexOf('/') + 1);
                    }

                    log.info("原文件名: " + fileName);
                    // 判断下这个文件名同样的名字是否已经存在 如果存在则加个原文件名最后加个uuid
                    File sameNameFile = new File(saveDir + File.separator + fileName);
                    if (sameNameFile.exists()) {
                        fileName = fileName.substring(0, fileName.lastIndexOf('.')) + "_" + UUID.randomUUID() + fileName.substring(fileName.lastIndexOf('.'));
                        log.info("文件名重复，重命名文件为：" + fileName);
                    }
                    // 创建文件
                    file = new File(saveDir + File.separator + fileName);
                    // 读取数据到文件
                    InputStream inputStream = httpURLConnection.getInputStream();
                    FileOutputStream fileOutputStream = new FileOutputStream(file);
                    int bytesRead = -1;
                    byte[] buffer = new byte[4096];
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }
                    fileOutputStream.close();
                    inputStream.close();
                    log.info("文件下载完成。");
                    // 下载成功，退出循环
                    httpURLConnection.disconnect();
                    break;
                } else {
                    log.warn("HTTP响应码不是200，准备重试。尝试次数: " + (retryCount + 1));
                }
                httpURLConnection.disconnect();
            } catch (Exception e) {
                log.error("文件下载失败。尝试次数: " + (retryCount + 1), e);
            }
            retryCount++;
            if (retryCount >= maxRetries) {
                log.error("文件下载失败，已达到最大重试次数。");
            }
        }
        return file;
    }

    public FileUploadBucketConfigDTO initPlatformConfig(Long tenantId) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(tenantId);
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        addPlatformConfig(bucketConfig);
        return bucketConfig;
    }

    public FileInfoDTO getFileInfoByKeyWithoutLogin(FileDetailQuerySingleCommand command) {
        String fileKey = command.getFileKey();
        FileInfo fileInfo = fileStorageService.getFileInfoByFileKey(fileKey);
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(fileInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(fileInfo.getSystemId(), fileInfo.getTenantId(), fileInfo.getBusinessCode());
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(fileInfo.getSystemId(), 1L, fileInfo.getBusinessCode());
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        addPlatformConfig(bucketConfig);
        // 水印处理 根据数据分级对外链进行处理
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .username(command.getOperatorName())
            .build();
        fileProcessing(fileInfo, businessConfig, ssoUserAuthInfo);
        return fileInfoAssembler.entity2DTO(fileInfo);
    }

    @SneakyThrows
    public FileInfoDTO uploadWordTransferPdf(MultipartFile file, SsoUserAuthInfoDTO ssoUserAuthInfo, String businessCode, String objectId, String objectType) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(ssoUserAuthInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 获取业务配置
        FileUploadBusinessConfigDTO businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), ssoUserAuthInfo.getTenantId(), businessCode);
        if (Objects.isNull(businessConfig)) {
            log.info("租户的配置的业务场景不存在 取默认平台的业务场景配置");
            // 取默认的业务场景配置
            businessConfig = businessConfigQueryService.acquireBusinessConfig(ssoUserAuthInfo.getSystemId(), 1L, businessCode);
            if (Objects.isNull(businessConfig)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
            }
        }
        // word转pdf
        FileInfoDTO fileInfo = null;
        try {
            File saveFile = File.createTempFile(file.getOriginalFilename(), null);
            FileCopyUtils.copy(file.getBytes(), saveFile);
            if (Objects.isNull(saveFile)) {
                throw new BusinessException(MessageCode.FILE_TRANSFER_ERROR);
            }
            if (file.getOriginalFilename().endsWith("doc") || file.getOriginalFilename().endsWith("docx")) {
                String outputFilePath = this.octTempFilePath + file.getOriginalFilename() + ".pdf";
                DocumentConverter.create()
                    .input(new FileInputStream(saveFile))
                    .output(new FileOutputStream(outputFilePath))
                    .convert(DocumentConvertType.PDF);
                InputStreamFileWrapper inputStreamFileWrapperAfter = new InputStreamFileWrapper();
                FileInputStream fileInputStreamAfter = new FileInputStream(outputFilePath);
                inputStreamFileWrapperAfter.setInputStream(fileInputStreamAfter);
                inputStreamFileWrapperAfter.setName(file.getOriginalFilename() + ".pdf");
                inputStreamFileWrapperAfter.setContentType("application/pdf");
                fileInfo = this.uploadFile(inputStreamFileWrapperAfter, ssoUserAuthInfo, businessCode, objectId, objectType);
                FileUtil.del(outputFilePath);
            } else {
                fileInfo = this.uploadFile(file, ssoUserAuthInfo, businessCode, objectId, objectType);
            }
        } catch (Exception e) {
            log.error("文件转存word转换为pdf失败 error ", e);
            throw e;
        }

        return fileInfo;
    }

    public byte[] getFileByte(String fileKey) {
        FileInfo fileInfo = fileStorageService.getFileInfoByFileKey(fileKey);
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByTenantId(fileInfo.getTenantId());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        addPlatformConfig(bucketConfig);
        return fileStorageService.download(fileInfo).bytes();
    }

    public FileInputStream downloadFile(String fileURL, String ext) {
        File file = downloadFile(fileURL, ext, this.octTempFilePath);
        if (ObjUtil.isNull(file)) {
            return null;
        }
        FileInputStream is = null;
        try {
            is = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        return is;
    }

    public TosTempStsConfigDTO getTosStsConfig(TosTempStsConfigCreateCommand command) {
        // 获取bucket配置
        FileUploadBucketConfigDTO bucketConfig = bucketConfigQueryService.acquireBucketConfigByPlatform(command.getPlatform());
        if (Objects.isNull(bucketConfig)) {
            throw new BusinessException(MessageCode.BUCKET_CONFIG_NOT_EXIST);
        }
        // 查询对应的桶是否有IAM角色
        Map<String, String> config = tosBucketConfigProperties.getIamConfig().getConfig();
        if (Objects.isNull(config) || Objects.isNull(config.get(bucketConfig.getBucketName()))) {
            throw new BusinessException(MessageCode.PLATFORM_NOT_SUPPORT);
        }
        String roleArn = config.get(bucketConfig.getBucketName());
        try {
            ISTSService stsService = STSServiceImpl.getInstance();
            stsService.setAccessKey(bucketConfig.getAccessKey());
            stsService.setSecretKey(bucketConfig.getSecretKey());
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleSessionName(command.getRoleSessionName());
            // 默认一小时过期
            request.setDurationSeconds(3600);
//            request.setRoleTrn("trn:iam::2102045950:role/genn-frontend-STS");
            request.setRoleTrn(roleArn);
            AssumeRoleResponse resp = stsService.assumeRole(request);
            if (Objects.nonNull(resp)) {
               return TosTempStsConfigDTO.builder()
                    .accessKeyId(resp.getResult().getCredentials().getAccessKeyId())
                    .secretAccessKey(resp.getResult().getCredentials().getSecretAccessKey())
                    // "CurrentTime":"2025-03-11T17:26:19+08:00","ExpiredTime":"2025-03-11T18:26:19+08:00"
                    .currentTime(LocalDateTime.parse(resp.getResult().getCredentials().getCurrentTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                    .expiredTime(LocalDateTime.parse(resp.getResult().getCredentials().getExpiredTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                    .sessionToken(resp.getResult().getCredentials().getSessionToken())
                    .platform(bucketConfig.getPlatform())
                    .endPoint(bucketConfig.getEndPoint())
                    .bucketName(bucketConfig.getBucketName())
                    .domain(bucketConfig.getDomain())
                    .build();
            }
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        } catch (Exception e) {
            log.error("获取TOS临时STS配置失败", e);
            throw new BusinessException(MessageCode.TOS_STS_CONFIG_ERROR);
        }
    }
}
