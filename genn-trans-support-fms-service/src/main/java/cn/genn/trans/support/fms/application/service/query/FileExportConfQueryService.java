package cn.genn.trans.support.fms.application.service.query;

import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.trans.support.fms.interfaces.dto.FileExportConfDTO;
import cn.genn.trans.support.fms.interfaces.query.FileExportConfQuery;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportConfPO;
import cn.genn.trans.support.fms.application.assembler.FileExportConfAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileExportConfMapper;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileExportConfQueryService {

    @Resource
    private FileExportConfMapper mapper;
    @Resource
    private FileExportConfAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileExportConfDTO分页对象
     */
    public PageResultDTO<FileExportConfDTO> page(FileExportConfQuery query) {
        FileExportConfPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileExportConfDTO
     */
    public FileExportConfDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    @IgnoreTenant
    public FileExportConfDTO queryByBusinessCode(Long systemId, Long tenantId, String businessCode) {
        return assembler.PO2DTO(mapper.selectOne(new QueryWrapper<FileExportConfPO>()
            .lambda()
            .eq(FileExportConfPO::getSystemId, systemId)
            .eq(FileExportConfPO::getTenantId, tenantId)
            .eq(FileExportConfPO::getBusinessCode, businessCode)
        ));
    }

    @IgnoreTenant
    public FileExportConfDTO queryBySubBusinessCode(Long systemId, Long tenantId, String subBusinessCode) {
        return assembler.PO2DTO(mapper.selectOne(new QueryWrapper<FileExportConfPO>()
            .lambda()
            .eq(FileExportConfPO::getSystemId, systemId)
            .eq(FileExportConfPO::getTenantId, tenantId)
            .eq(FileExportConfPO::getSubBusinessCode, subBusinessCode)
        ));
    }

    @IgnoreTenant
    public FileExportConfDTO queryByBusinessCodeWithSub(Long systemId, Long tenantId, String businessCode, String subBusinessCode) {
        return assembler.PO2DTO(mapper.selectOne(new QueryWrapper<FileExportConfPO>()
            .lambda()
            .eq(FileExportConfPO::getSystemId, systemId)
            .eq(FileExportConfPO::getTenantId, tenantId)
            .eq(FileExportConfPO::getBusinessCode, businessCode)
            .eq(StrUtil.isNotBlank(subBusinessCode), FileExportConfPO::getSubBusinessCode, subBusinessCode)
        ));
    }
}

