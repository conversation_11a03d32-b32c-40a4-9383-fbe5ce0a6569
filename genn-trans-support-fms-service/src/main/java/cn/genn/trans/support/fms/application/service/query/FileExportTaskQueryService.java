package cn.genn.trans.support.fms.application.service.query;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.query.FileExportTaskQuery;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportTaskPO;
import cn.genn.trans.support.fms.application.assembler.FileExportTaskAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileExportTaskMapper;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileExportTaskQueryService {

    @Resource
    private FileExportTaskMapper mapper;
    @Resource
    private FileExportTaskAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileExportTaskDTO分页对象
     */
    public PageResultDTO<FileExportTaskDTO> page(FileExportTaskQuery query) {
        FileExportTaskPO po = assembler.query2PO(query);
        po.setCreateUserId(CurrentUserHolder.getUserId());
        po.setDeleted(DeletedEnum.NOT_DELETED);
        QueryWrapper queryWrapper = new QueryWrapper<>(po);
        queryWrapper.orderByDesc("create_time");
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), queryWrapper));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileExportTaskDTO
     */
    public FileExportTaskDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }
}

