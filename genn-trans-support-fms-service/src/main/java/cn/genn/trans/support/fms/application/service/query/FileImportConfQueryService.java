package cn.genn.trans.support.fms.application.service.query;

import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileExportConfPO;
import cn.genn.trans.support.fms.interfaces.dto.FileExportConfDTO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.trans.support.fms.interfaces.dto.FileImportConfDTO;
import cn.genn.trans.support.fms.interfaces.query.FileImportConfQuery;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileImportConfPO;
import cn.genn.trans.support.fms.application.assembler.FileImportConfAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileImportConfMapper;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileImportConfQueryService {

    @Resource
    private FileImportConfMapper mapper;
    @Resource
    private FileImportConfAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileImportConfDTO分页对象
     */
    public PageResultDTO<FileImportConfDTO> page(FileImportConfQuery query) {
        FileImportConfPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileImportConfDTO
     */
    public FileImportConfDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }
    @IgnoreTenant
    public FileImportConfDTO queryBySubBusinessCode(Long systemId, Long tenantId, String subBusinessCode) {
        return assembler.PO2DTO(mapper.selectOne(new QueryWrapper<FileImportConfPO>()
            .lambda()
            .eq(FileImportConfPO::getSystemId, systemId)
            .eq(FileImportConfPO::getTenantId, tenantId)
            .eq(FileImportConfPO::getSubBusinessCode, subBusinessCode)
        ));
    }
    @IgnoreTenant
    public FileImportConfDTO queryByBusinessCodeWithSub(Long systemId, Long tenantId, String businessCode, String subBusinessCode) {
        return assembler.PO2DTO(mapper.selectOne(new QueryWrapper<FileImportConfPO>()
            .lambda()
            .eq(FileImportConfPO::getSystemId, systemId)
            .eq(FileImportConfPO::getTenantId, tenantId)
            .eq(FileImportConfPO::getBusinessCode, businessCode)
            .eq(StrUtil.isNotBlank(subBusinessCode), FileImportConfPO::getSubBusinessCode, subBusinessCode)
        ));
    }
}

