package cn.genn.trans.support.fms.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.trans.support.fms.application.assembler.FileUploadBucketConfigAssembler;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileUploadBucketConfigMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadBucketConfigPO;
import cn.genn.trans.support.fms.interfaces.command.FileUploadBucketConfigInitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBucketConfigQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileUploadBucketConfigQueryService {

    @Resource
    private FileUploadBucketConfigMapper mapper;
    @Resource
    private FileUploadBucketConfigAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileUploadBucketConfigDTO分页对象
     */
    @IgnoreTenant
    public PageResultDTO<FileUploadBucketConfigDTO> page(FileUploadBucketConfigQuery query) {
        FileUploadBucketConfigPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileUploadBucketConfigDTO
     */
    @IgnoreTenant
    public FileUploadBucketConfigDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    /**
     * 根据租户id获取相应的bucket配置
     *
     * @param tenantId
     * @return
     */
    @IgnoreTenant
    public FileUploadBucketConfigDTO acquireBucketConfigByTenantId(Long tenantId) {
        LambdaQueryWrapper<FileUploadBucketConfigPO> wrapper = Wrappers.lambdaQuery(FileUploadBucketConfigPO.class)
            .eq(FileUploadBucketConfigPO::getTenantId, tenantId);
        return assembler.PO2DTO(mapper.selectOne(wrapper));
    }

    /**
     * 根据platform获取相应的bucket配置
     *
     * @param platform
     * @return
     */
    @IgnoreTenant
    public FileUploadBucketConfigDTO acquireBucketConfigByPlatform(String platform) {
        LambdaQueryWrapper<FileUploadBucketConfigPO> wrapper = Wrappers.lambdaQuery(FileUploadBucketConfigPO.class)
            .eq(FileUploadBucketConfigPO::getPlatform, platform);
        return assembler.PO2DTO(mapper.selectOne(wrapper));
    }

    @IgnoreTenant
    public Boolean initConfig(FileUploadBucketConfigInitCommand command) {
        Long tenantId = command.getTenantId();
        String platform = command.getPlatform();
        // 校验需要初始化的租户是否已经配置
        LambdaQueryWrapper<FileUploadBucketConfigPO> wrapper = Wrappers.lambdaQuery(FileUploadBucketConfigPO.class)
            .eq(FileUploadBucketConfigPO::getTenantId, tenantId);
        FileUploadBucketConfigPO configPO = mapper.selectOne(wrapper);
        if (Objects.nonNull(configPO)) {
            return true;
        }
        wrapper = Wrappers.lambdaQuery(FileUploadBucketConfigPO.class)
            .eq(FileUploadBucketConfigPO::getPlatform, platform);
        configPO = mapper.selectOne(wrapper);
        if (Objects.isNull(configPO)) {
            return false;
        }
        configPO.setTenantId(tenantId);
        configPO.setId(null);
        configPO.setPlatform("huawei-obs-tenant-" + tenantId);
        mapper.insert(configPO);
        return true;
    }

    @IgnoreTenant
    public List<FileUploadBucketConfigDTO> getSelectInitConfig() {
        LambdaQueryWrapper<FileUploadBucketConfigPO> wrapper = Wrappers.lambdaQuery(FileUploadBucketConfigPO.class)
            .eq(FileUploadBucketConfigPO::getTenantId, 0);
        List<FileUploadBucketConfigDTO> list = assembler.PO2DTO(mapper.selectList(wrapper));
        list.forEach(item -> {
            item.setAccessKey(null);
            item.setSecretKey(null);
        });
        return list;
    }
}

