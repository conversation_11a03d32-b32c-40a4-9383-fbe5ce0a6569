package cn.genn.trans.support.fms.application.service.query;

import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileUploadBusinessConfigMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadBusinessConfigPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBusinessConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBusinessConfigQuery;
import cn.genn.trans.support.fms.application.assembler.FileUploadBusinessConfigAssembler;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileUploadBusinessConfigQueryService {

    @Resource
    private FileUploadBusinessConfigMapper mapper;
    @Resource
    private FileUploadBusinessConfigAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileUploadBusinessConfigDTO分页对象
     */
    public PageResultDTO<FileUploadBusinessConfigDTO> page(FileUploadBusinessConfigQuery query) {
        FileUploadBusinessConfigPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileUploadBusinessConfigDTO
     */
    public FileUploadBusinessConfigDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    @IgnoreTenant
    public FileUploadBusinessConfigDTO acquireBusinessConfig(Long systemId, Long tenantId, String businessCode) {
        LambdaQueryWrapper<FileUploadBusinessConfigPO> wrapper = Wrappers.lambdaQuery(FileUploadBusinessConfigPO.class)
            .eq(FileUploadBusinessConfigPO::getSystemId, systemId)
            .eq(FileUploadBusinessConfigPO::getTenantId, tenantId)
            .eq(FileUploadBusinessConfigPO::getBusinessCode, businessCode);
        return assembler.PO2DTO(mapper.selectOne(wrapper));
    }
}

