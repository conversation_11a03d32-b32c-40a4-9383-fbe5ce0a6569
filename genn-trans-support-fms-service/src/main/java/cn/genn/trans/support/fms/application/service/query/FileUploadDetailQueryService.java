package cn.genn.trans.support.fms.application.service.query;

import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileUploadDetailMapper;
import cn.genn.trans.support.fms.infrastructure.repository.po.FileUploadDetailPO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadDetailDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadDetailQuery;
import cn.genn.trans.support.fms.application.assembler.FileUploadDetailAssembler;
import cn.genn.core.model.page.PageResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileUploadDetailQueryService {

    @Resource
    private FileUploadDetailMapper mapper;
    @Resource
    private FileUploadDetailAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return FileUploadDetailDTO分页对象
     */
    public PageResultDTO<FileUploadDetailDTO> page(FileUploadDetailQuery query) {
        FileUploadDetailPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return FileUploadDetailDTO
     */
    public FileUploadDetailDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }
}

