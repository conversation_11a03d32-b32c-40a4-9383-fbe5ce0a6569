package cn.genn.trans.support.fms.application.strategy;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.lock.base.LockException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.trans.support.fms.application.assembler.FileExportTaskAssembler;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileExportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Service
@Slf4j
public abstract class CommonAbstractExportStrategy {

    @Resource
    private FileExportTaskMapper fileExportTaskMapper;
    @Resource
    private FileExportTaskAssembler fileExportTaskAssembler;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Resource
    private LockTemplate lockTemplate;

    public void export(FileExportTaskDTO fileExportTaskDTO) {
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("fileExportTask_execute_" + fileExportTaskDTO.getId());

            // core-上下文
            BaseRequestContext baseRequestContext = new BaseRequestContext();
            baseRequestContext.setTenantId(fileExportTaskDTO.getTenantId());
            BaseRequestContext.set(baseRequestContext);

            if (lockInfo != null) {
                // 校验是否是其他状态
                if (TaskStatusEnum.WAIT != fileExportTaskDTO.getTaskStatus() && TaskStatusEnum.FAIL != fileExportTaskDTO.getTaskStatus()) {
                    log.info("当前任务不处于待执行或失败状态，任务id:{}", fileExportTaskDTO.getId());
                    return;
                }
                fileExportTaskDTO.setTaskStatus(TaskStatusEnum.PROCESS);
                fileExportTaskMapper.updateById(fileExportTaskAssembler.toPO(fileExportTaskDTO));
                // 开始执行
                try (Workbook workbook = executeTask(fileExportTaskDTO)) {
                    // 更新状态
                    fileExportTaskDTO.setTaskStatus(TaskStatusEnum.UPLOAD);
                    fileExportTaskMapper.updateById(fileExportTaskAssembler.toPO(fileExportTaskDTO));
                    // 上传执行结果
                    InputStream fileInputStream = ExcelHandleUtil.toInputStream(workbook);
                    FileInfo fileInfo = fileUploadSupportActionService.uploadFileForExport(fileInputStream, fileExportTaskDTO.getId(), fileExportTaskDTO.getBusinessCode(), fileExportTaskDTO.getTenantId(),
                        fileExportTaskDTO.getSystemId(), fileExportTaskDTO.getFileName(), fileExportTaskDTO.getTemplateType());
                    // 更新成功
                    fileExportTaskDTO.setTaskStatus(TaskStatusEnum.SUCCESS);
                    fileExportTaskDTO.setResultFileKey(fileInfo.getFileKey());
                    fileExportTaskDTO.setExecuteCount(fileExportTaskDTO.getExecuteCount() + 1);
                    fileExportTaskDTO.setFileName(fileInfo.getOriginalFilename());
                    // 更新状态和执行次数
                    fileExportTaskMapper.updateById(fileExportTaskAssembler.toPO(fileExportTaskDTO));
                    // TODO 发送执行成功通知消息
                } catch (Exception e) {
                    log.error("执行导出任务失败，任务id:{}", fileExportTaskDTO.getId(), e);
                    throw new BusinessException(e.getMessage());
                }
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));
        if (Objects.isNull(fileExportTaskDTO.getTaskParam())) {
            return null;
        }
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        List<ExcelExportEntity> colList = new ArrayList<>();
        if (Objects.isNull(configGridDTO) || Objects.isNull(configGridDTO.getColList())) {
            return null;
        }
        //        configGridDTO.getColList().sort(Comparator.comparing(ConfigGridColDTO::getSortOrder));
        configGridDTO.getColList().forEach(col -> {
            ExcelExportEntity excelExportEntity = new ExcelExportEntity();
            excelExportEntity.setName(col.getColName());
            excelExportEntity.setKey(col.getBackendPropertyName());
            excelExportEntity.setOrderNum(col.getSortOrder());
            colList.add(excelExportEntity);
        });
        return colList;
    }


    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    public abstract ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO);

    public abstract Workbook executeTask(FileExportTaskDTO fileExportTaskDTO);

    public abstract ExportTaskBusinessCodeEnum getBusinessCode();

}
