package cn.genn.trans.support.fms.application.strategy;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.lock.base.LockException;
import cn.genn.lock.base.LockTemplate;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.trans.support.fms.application.assembler.FileExportTaskAssembler;
import cn.genn.trans.support.fms.application.assembler.FileImportTaskAssembler;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileExportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileImportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportConfDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileImportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.ImportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Service
@Slf4j
public abstract class CommonAbstractImportStrategy {

    @Resource
    private FileImportTaskMapper fileImportTaskMapper;
    @Resource
    private FileImportTaskAssembler fileImportTaskAssembler;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Resource
    private LockTemplate lockTemplate;

    /**
     * 执行导入任务
     *
     * @param fileImportTaskDTO
     */
    public void export(FileImportTaskDTO fileImportTaskDTO) {
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock("fileImportTask_execute_" + fileImportTaskDTO.getId());

            // core-上下文
            BaseRequestContext baseRequestContext = new BaseRequestContext();
            baseRequestContext.setTenantId(fileImportTaskDTO.getTenantId());
            BaseRequestContext.set(baseRequestContext);

            if (lockInfo != null) {
                // 校验是否是其他状态
                if (TaskStatusEnum.WAIT != fileImportTaskDTO.getTaskStatus() && TaskStatusEnum.FAIL != fileImportTaskDTO.getTaskStatus()) {
                    log.info("当前任务不处于待执行或失败状态，任务id:{}", fileImportTaskDTO.getId());
                    return;
                }
                fileImportTaskDTO.setTaskStatus(TaskStatusEnum.PROCESS);
                fileImportTaskMapper.updateById(fileImportTaskAssembler.toPO(fileImportTaskDTO));
                // 开始执行
                try (Workbook workbook = executeTask(fileImportTaskDTO)) {
                    // 更新状态
                    fileImportTaskDTO.setTaskStatus(TaskStatusEnum.UPLOAD);
                    fileImportTaskMapper.updateById(fileImportTaskAssembler.toPO(fileImportTaskDTO));
                    // 上传执行结果
                    InputStream fileInputStream = ExcelHandleUtil.toInputStream(workbook);
                    FileInfo fileInfo = fileUploadSupportActionService.uploadFileForExport(fileInputStream, fileImportTaskDTO.getId(), fileImportTaskDTO.getBusinessCode(), fileImportTaskDTO.getTenantId(),
                        fileImportTaskDTO.getSystemId(), fileImportTaskDTO.getFileName(), fileImportTaskDTO.getTemplateType());
                    // 更新成功
                    fileImportTaskDTO.setTaskStatus(TaskStatusEnum.SUCCESS);
                    fileImportTaskDTO.setResultFileKey(fileInfo.getFileKey());
                    fileImportTaskDTO.setFileName(fileInfo.getOriginalFilename());
                    fileImportTaskDTO.setRemark("");
                    // 更新状态和执行次数
                    fileImportTaskMapper.updateById(fileImportTaskAssembler.toPO(fileImportTaskDTO));
                    // TODO 发送执行成功通知消息
                } catch (Exception e) {
                    log.error("执行导入任务失败，任务id:{}", fileImportTaskDTO.getId(), e);
                    throw new BusinessException(e.getMessage());
                }
            } else {
                throw new LockException();
            }
        } finally {
            if (lockInfo != null) {
                lockTemplate.releaseLock(lockInfo);
            }
        }
    }

    public abstract Workbook executeTask(FileImportTaskDTO fileImportTaskDTO);

    public abstract ImportTaskBusinessCodeEnum getBusinessCode();
}
