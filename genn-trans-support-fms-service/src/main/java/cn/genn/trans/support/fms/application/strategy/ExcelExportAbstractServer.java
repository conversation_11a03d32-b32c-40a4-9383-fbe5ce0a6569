package cn.genn.trans.support.fms.application.strategy;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.genn.trans.support.fms.infrastructure.context.MagicTokenContext;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class ExcelExportAbstractServer implements IExcelExportServer {

    @Override
    public List<Object> selectListForExcelExport(Object queryParams, int pageNo) {
        FileExportTaskDTO taskInfo = (FileExportTaskDTO) queryParams;
        //如果导出任务已取消，不必再继续获取数据
        if (taskInfo.getTaskStatus() == TaskStatusEnum.CANCEL) {
            return null;
        }

        // 设置MagicToken上下文，确保在调用远程服务时能自动添加magic-token请求头
        return MagicTokenContext.runWithTokenData(taskInfo.getTokenData(), () -> {
            //获取数据
            long startTime = System.currentTimeMillis();
            log.info("列表导出第{}次调用业务接口计时开始，业务类型={}", pageNo, taskInfo.getBusinessCode());
            List<Object> list = queryListForExcelExport(taskInfo, pageNo);
            log.info("列表导出第{}次调用业务接口计时结束，业务类型={}", pageNo, taskInfo.getBusinessCode());
            long startEnd = System.currentTimeMillis();
            log.info("列表导出第{}次调用业务接口计耗时={}ms;业务类型={}", pageNo, (startEnd - startTime), taskInfo.getBusinessCode());

            if (CollectionUtil.isEmpty(list)) {
                log.info("第{}次selectListForExcelExport--size=0,businessCode={}", pageNo, taskInfo.getBusinessCode());
                return null;
            }
            //更新导出记录数
            //TODO 数据处理-列表数据转换(一个模板可配制多个列转换器)
            log.info("第{}次selectListForExcelExport 总数={},businessCode={}", pageNo, list.size(), taskInfo.getBusinessCode());
            //数据处理-字段转换
            return list;
        });
    }

    public abstract List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo);

    protected int getPageSize(FileExportTaskDTO taskInfo) {
        int pageSize = 1000;
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            pageSize = taskInfo.getExportConfConfig().getPageSize();
        }
        return pageSize;
    }

    protected boolean checkExportLimit(FileExportTaskDTO taskInfo, int pageNo, int pageSize) {
        int offset = (pageNo - 1) * pageSize;
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return true;
        }
        return false;
    }

}
