package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.bill.PayBillDetailViewPageDTO;
import cn.genn.trans.support.fms.application.dto.bill.PayeeBillDetailViewPageDTO;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.CustomExcelStyler;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 对账单数据导出
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("billExcelExportStrategy")
@Slf4j
public class BillExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private BillExcelExportServer billExcelExportServer;

    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        // 判断是应收对账单还是应付对账单
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillPageQuery().getBillType();
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName((BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "账单明细");
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        //        exportParams.setTitle(fileExportTaskDTO.getTaskName());
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        exportParams.setStyle(CustomExcelStyler.class);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, billExcelExportServer, fileExportTaskDTO);
        // 已完成明细数据 开始进行汇总数据的模板填充
        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }
        // 获取当前的 sheet
        Sheet currentSheet = workbook.getSheetAt(0);
        // 创建一个新的 sheet 根据模板进行替换
        // 判断是应收对账单还是应付对账单
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillPageQuery().getBillType();
        Sheet newSheet = workbook.createSheet((BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "账单");
        // 处理汇总数据sheet
        Sheet summarySheet = billExcelExportServer.getSummaryDataSheet(fileExportTaskDTO);

        // 处理需要将文件转为数字格式的列名数据
        /*if (CollectionUtil.isNotEmpty(fileExportTaskDTO.getExportConfConfig().getNumberStyleColNameList())) {
            ExcelHandleUtil.handleNumberStyleColNameList(currentSheet, fileExportTaskDTO.getExportConfConfig().getNumberStyleColNameList());
        }*/

        // 复制模板 sheet 的内容到新的 sheet
        ExcelHandleUtil.copySheet(summarySheet, newSheet);
        // 将当前的 sheet 移动到第二个位置
        workbook.setSheetOrder(currentSheet.getSheetName(), 1);

        workbook.setActiveSheet(0);

        String fileName = null;
        if (fileExportTaskDTO.getSystemId().equals(SystemEnum.TMS.getCode())) {
            fileName = "对账单_" + configGridDTO.getSifBillPageQuery().getBillNo() + "." + fileExportTaskDTO.getTemplateType();
        } else {
            fileName = (BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "对账单_" + configGridDTO.getSifBillPageQuery()
                .getBillNo() + "." + fileExportTaskDTO.getTemplateType();
        }
        fileExportTaskDTO.setFileName(fileName);
        return workbook;
    }

    @Override
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));

        List<ExcelExportEntity> colList = new ArrayList<>();
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillPageQuery().getBillType();
        Field[] fields = BillTypeEnum.isCustomer(billType) ? PayeeBillDetailViewPageDTO.class.getDeclaredFields() : PayBillDetailViewPageDTO.class.getDeclaredFields();
        for (Field field : fields) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                int sortOrder = annotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(beanPath);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            }
        }
        return colList;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.SIF_BILL_EXPORT;
    }


}
