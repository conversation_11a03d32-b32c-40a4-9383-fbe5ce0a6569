package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.bill.*;
import cn.genn.trans.support.fms.application.service.action.FileTemplateActionService;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.genn.trans.support.fms.interfaces.query.business.SifBillPageQuery;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.hutool.core.bean.BeanPath;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 对账单数据导出
 * 调用rpc client
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("billExcelExportServer")
@Slf4j
public class BillExcelExportServer extends ExcelExportAbstractServer {

    @Resource
    private InternalDomainUtil internalDomainUtil;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private FileTemplateActionService fileTemplateActionService;

    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    // 应付对账单
    private String queryPayDetailUrl = "/api/ops/bill/export/pay/detailview";
    private String queryPayOverviewUrl = "/api/ops/bill/export/pay/overview";
    // 应收对账单
    private String queryPayeeDetailUrl = "/api/ops/bill/export/payee/detailview";
    private String queryPayeeOverviewUrl = "/api/ops/bill/export/payee/overview";

    /**
     * 应付对账单明细
     *
     * @param taskInfo
     * @param pageNo
     * @return
     */
    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        SifBillPageQuery sifBillPageQuery = configGridDTO.getSifBillPageQuery();
        sifBillPageQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            sifBillPageQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        String url = null;
        String requsetBody = JsonUtils.toJson(sifBillPageQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");
        // 区分应收应付类型
        BillTypeEnum billTypeEnum = sifBillPageQuery.getBillType();
        // 应付
        String opsServerUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), SystemEnum.OPS.getCode());
        if (BillTypeEnum.isCarrier(billTypeEnum)) {
            url = opsServerUrl + queryPayDetailUrl;
            return handlePayBillDetailView(taskInfo, pageNo, url, requsetBody, headers, sifBillPageQuery);
        }
        // 应收
        if (BillTypeEnum.isCustomer(billTypeEnum)) {
            url = opsServerUrl + queryPayeeDetailUrl;
            return handlePayeeBillDetailView(taskInfo, pageNo, url, requsetBody, headers, sifBillPageQuery);
        }
        return Collections.emptyList();
    }



    @NotNull
    private List<Object> handlePayBillDetailView(FileExportTaskDTO taskInfo, int pageNo, String url, String requsetBody, MultiValueMap<String, String> headers, SifBillPageQuery sifBillPageQuery) {
        PageResultDTO<PayBillDetailViewPageDTO> response = fetchPostResponseOfPay(url, requsetBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * sifBillPageQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        for (PayBillDetailViewPageDTO dto : response.getList()) {
            Map<String, Object> map = new HashMap<>();
            for (Field field : dto.getClass().getDeclaredFields()) {
                DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
                if (annotation != null) {
                    String beanPath = annotation.beanPath();
                    String fieldName = annotation.fieldName();
                    Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                    BeanPath resolver = new BeanPath(beanPath);
                    Object value = resolver.get(dto);
                    if (!wrapperClass.equals(DataWrapper.class)) {
                        try {
                            DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                            value = wrapper.wrap(value);
                        } catch (Exception e) {
                            log.error("DataWrapper实例化失败", e);
                        }
                    }
                    map.put(beanPath, Optional.ofNullable(value).orElse(""));
                }
            }
            resultList.add(map);
        }
        return resultList;
    }

    private List<Object> handlePayeeBillDetailView(FileExportTaskDTO taskInfo, int pageNo, String url, String requsetBody, MultiValueMap<String, String> headers, SifBillPageQuery sifBillPageQuery) {
        PageResultDTO<PayeeBillDetailViewPageDTO> response = fetchPostResponseOfPayee(url, requsetBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * sifBillPageQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        for (PayeeBillDetailViewPageDTO dto : response.getList()) {
            Map<String, Object> map = new HashMap<>();
            for (Field field : dto.getClass().getDeclaredFields()) {
                DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
                if (annotation != null) {
                    String beanPath = annotation.beanPath();
                    String fieldName = annotation.fieldName();
                    Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                    BeanPath resolver = new BeanPath(beanPath);
                    Object value = resolver.get(dto);
                    if (!wrapperClass.equals(DataWrapper.class)) {
                        try {
                            DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                            value = wrapper.wrap(value);
                        } catch (Exception e) {
                            log.error("DataWrapper实例化失败", e);
                        }
                    }
                    map.put(beanPath, Optional.ofNullable(value).orElse(""));
                }
            }
            resultList.add(map);
        }
        return resultList;
    }

    /**
     * 应付对账单明细页数据
     *
     * @param url
     * @param requestBody
     * @param headers
     * @return
     */
    public PageResultDTO<PayBillDetailViewPageDTO> fetchPostResponseOfPay(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<PayBillDetailViewPageDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<PayBillDetailViewPageDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }

    /**
     * 应收对账单明细页数据
     *
     * @param url
     * @param requestBody
     * @param headers
     * @return
     */
    public PageResultDTO<PayeeBillDetailViewPageDTO> fetchPostResponseOfPayee(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<PayeeBillDetailViewPageDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<PayeeBillDetailViewPageDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }

    /**
     * 账单总览页汇总数据
     *
     * @return
     */
    public Sheet getSummaryDataSheet(FileExportTaskDTO taskInfo) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(taskInfo.getCreateUserId())
            .username(taskInfo.getCreateUserName())
            .tenantId(taskInfo.getTenantId())
            .systemId(taskInfo.getSystemId())
            .build();
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        String fileKey = taskInfo.getExportConfConfig().getTemplateFileKeyOfBill().get( configGridDTO.getSifBillPageQuery().getBillType());
        if (Objects.isNull(fileKey)) {
            throw new BusinessException(MessageCode.EXPORT_TEMPLATE_NOT_EXIST);
        }
        Map<Integer, Map<String, Object>> excelExportData = new HashMap<>();
        // 调用http接口获取填充参数
        SifBillPageQuery sifBillPageQuery = configGridDTO.getSifBillPageQuery();
        String url = null;
        String requsetBody = JsonUtils.toJson(sifBillPageQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");

        // 区分应收应付类型
        BillTypeEnum billTypeEnum = sifBillPageQuery.getBillType();
        String opsServerUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), SystemEnum.OPS.getCode());
        // 应付
        if (BillTypeEnum.isCarrier(billTypeEnum)) {
            url = opsServerUrl + queryPayOverviewUrl;
            excelExportData = handlePayBillOverview(taskInfo, url, requsetBody, headers, sifBillPageQuery);
        }
        // 应收
        if (BillTypeEnum.isCustomer(billTypeEnum)) {
            url = opsServerUrl + queryPayeeOverviewUrl;
            excelExportData = handlePayeeBillOverview(taskInfo, url, requsetBody, headers, sifBillPageQuery);
        }
        FileInfoDTO summaryFileInfoDTO = fileTemplateActionService.generateFileTemplateOfMultipleParam(ssoUserAuthInfo, fileKey, excelExportData);
        if (Objects.isNull(summaryFileInfoDTO)) {
            throw new BusinessException(MessageCode.FILE_HANDLE_ERROR);
        }
        // 获取替换占位符后的最终汇总数据的文件的文件流
        File saveFile = FileUploadSupportActionService.downloadFile(summaryFileInfoDTO.getUrl(),summaryFileInfoDTO.getExt(), this.octTempFilePath);
        try (FileInputStream templateFile = new FileInputStream(saveFile)) {
            Workbook templateWorkbook = new XSSFWorkbook(templateFile);
            Sheet templateSheet = templateWorkbook.getSheetAt(0);
            templateFile.close();
            return templateSheet;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (saveFile != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(saveFile);
            }
        }
    }



    private Map<Integer, Map<String, Object>> handlePayBillOverview(FileExportTaskDTO taskInfo, String url, String requsetBody, MultiValueMap<String, String> headers, SifBillPageQuery sifBillPageQuery) {
        PayBillOverViewPageDTO response = fetchPostResponseOfPayBillOverView(url, requsetBody, headers);
        // 处理模板数据 包含集合的数据
        return getExcelExportDataOfPayBillOverView(response);
    }
    private Map<Integer, Map<String, Object>> handlePayeeBillOverview(FileExportTaskDTO taskInfo, String url, String requsetBody, MultiValueMap<String, String> headers, SifBillPageQuery sifBillPageQuery) {
        PayeeBillOverViewPageDTO response = fetchPostResponseOfPayeeBillOverView(url, requsetBody, headers);
        // 处理模板数据 包含集合的数据
        return getExcelExportDataOfPayeeBillOverView(response);
    }


    private Map<Integer, Map<String, Object>> getExcelExportDataOfPayBillOverView(PayBillOverViewPageDTO billOverView) {
        Map<Integer, Map<String, Object>> excelExportData = new HashMap<>();
        // templateDataMap
        Map<String, Object> templateDataMap = new HashMap<>();
        // 占位符处理
        for (Field field : PayBillOverViewPageDTO.class.getDeclaredFields()) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                BeanPath resolver = new BeanPath(beanPath);
                Object value = resolver.get(billOverView);
                if (!wrapperClass.equals(DataWrapper.class)) {
                    try {
                        DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                        value = wrapper.wrap(value);
                    } catch (Exception e) {
                        log.error("DataWrapper实例化失败", e);
                    }
                }
                templateDataMap.put(fieldName, Optional.ofNullable(value).orElse(""));
            }
        }
        excelExportData.put(0, templateDataMap);
        return excelExportData;
    }

    private Map<Integer, Map<String, Object>> getExcelExportDataOfPayeeBillOverView(PayeeBillOverViewPageDTO billOverView) {
        Map<Integer, Map<String, Object>> excelExportData = new HashMap<>();
        // templateDataMap
        Map<String, Object> templateDataMap = new HashMap<>();
        // 占位符处理
        for (Field field : PayeeBillOverViewPageDTO.class.getDeclaredFields()) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                BeanPath resolver = new BeanPath(beanPath);
                Object value = resolver.get(billOverView);
                if (!wrapperClass.equals(DataWrapper.class)) {
                    try {
                        DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                        value = wrapper.wrap(value);
                    } catch (Exception e) {
                        log.error("DataWrapper实例化失败", e);
                    }
                }
                templateDataMap.put(fieldName, Optional.ofNullable(value).orElse(""));
            }
        }
        // 占位符中集合数据的处理
        List<PayeeBillOverviewPageDetailInfoDTO> list = billOverView.getPayeeBillOverviewPageDetailInfoDTOList();

        if (CollectionUtils.isNotEmpty(list)) {
            List<Map<String, Object>> detailList = new ArrayList<>(list.size());
            for (int i = 0; i < list.size(); i++) {
                PayeeBillOverviewPageDetailInfoDTO detail = list.get(i);
                Map<String, Object> lineData = new HashMap<>();
                // 单行数据
                for (Field field : PayeeBillOverviewPageDetailInfoDTO.class.getDeclaredFields()) {
                    DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
                    if (annotation != null) {
                        String beanPath = annotation.beanPath();
                        String fieldName = annotation.fieldName();
                        Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                        BeanPath resolver = new BeanPath(beanPath);
                        Object value = resolver.get(detail);
                        if (!wrapperClass.equals(DataWrapper.class)) {
                            try {
                                DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                                value = wrapper.wrap(value);
                            } catch (Exception e) {
                                log.error("DataWrapper实例化失败", e);
                            }
                        }
                        lineData.put(fieldName, Optional.ofNullable(value).orElse(""));
                    }
                }
                lineData.put("index", i + 1);
                detailList.add(lineData);
            }
            templateDataMap.put("list", detailList);
        }
        excelExportData.put(0, templateDataMap);
        return excelExportData;
    }

    public PayBillOverViewPageDTO fetchPostResponseOfPayBillOverView(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PayBillOverViewPageDTO> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PayBillOverViewPageDTO>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }

    public PayeeBillOverViewPageDTO fetchPostResponseOfPayeeBillOverView(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PayeeBillOverViewPageDTO> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PayeeBillOverViewPageDTO>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
