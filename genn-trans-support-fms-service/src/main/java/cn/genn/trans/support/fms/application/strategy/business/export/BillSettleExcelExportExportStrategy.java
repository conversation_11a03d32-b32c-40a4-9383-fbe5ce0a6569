package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.settle.PaySettleBillDetailViewPageDTO;
import cn.genn.trans.support.fms.application.dto.settle.PayeeSettleBillDetailViewPageDTO;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.infrastructure.config.GennRequestContext;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.BillTypeEnum;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算单数据导出
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("billSettleExcelExportStrategy")
@Slf4j
public class BillSettleExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private BillSettleExcelExportServer billSettleExcelExportServer;
    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;

    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillSettlePageQuery().getBillType();
        exportParams.setSheetName((BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "账单明细");
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        //        exportParams.setTitle(fileExportTaskDTO.getTaskName());
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, billSettleExcelExportServer, fileExportTaskDTO);
        // 已完成明细数据 开始进行汇总数据的模板填充
        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }
        // 获取当前的 sheet
        Sheet currentSheet = workbook.getSheetAt(0);
        // 创建一个新的 sheet 根据模板进行替换
        // 判断是应收还是应付结算单
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillSettlePageQuery().getBillType();
        Sheet newSheet = workbook.createSheet((BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "账单");
        // 处理汇总数据sheet
        Sheet summarySheet = billSettleExcelExportServer.getSummaryDataSheet(fileExportTaskDTO);
        // 处理需要将文件转为数字格式的列名数据
        if (CollectionUtil.isNotEmpty(fileExportTaskDTO.getExportConfConfig().getNumberStyleColNameList())) {
            ExcelHandleUtil.handleNumberStyleColNameList(currentSheet, fileExportTaskDTO.getExportConfConfig().getNumberStyleColNameList());
        }
        // 复制模板 sheet 的内容到新的 sheet
        ExcelHandleUtil.copySheet(summarySheet, newSheet);
        // 将当前的 sheet 移动到第二个位置
        workbook.setSheetOrder(currentSheet.getSheetName(), 1);
        workbook.setActiveSheet(0);
        try {
            //添加结算章
            if(BillTypeEnum.isCustomer(billType)){
                String carrierSeal = GennRequestContext.getAttachment("carrierSeal", String.class);
                String entrustSeal = GennRequestContext.getAttachment("entrustSeal",String.class);
                if(StrUtil.isNotBlank(carrierSeal)){
                    addSettlementSeal(workbook,carrierSeal, 1, 6);
                }
                if(StrUtil.isNotBlank(entrustSeal)){
                    addSettlementSeal(workbook,entrustSeal, 4, 6);
                }
            }
            if(BillTypeEnum.isCarrier(billType)){
                String carrierSeal = GennRequestContext.getAttachment("carrierSeal", String.class);
                String settleSeal = GennRequestContext.getAttachment("settleSeal",String.class);
                if(StrUtil.isNotBlank(carrierSeal)){
                    addSettlementSeal(workbook,carrierSeal, 12, 12);
                }
                if(StrUtil.isNotBlank(settleSeal)){
                    addSettlementSeal(workbook,settleSeal, 2, 2);
                }
            }
        } catch (IOException e) {
            log.error("添加结算章报错!");
            throw new RuntimeException(e);
        }

        String fileName = null;
        if (fileExportTaskDTO.getSystemId().equals(SystemEnum.TMS.getCode())) {
            fileName =  "结算单_" + configGridDTO.getSifBillSettlePageQuery().getSettleNo() + "." + fileExportTaskDTO.getTemplateType();
        } else {
            fileName = (BillTypeEnum.isCustomer(billType) ? "应收" : "应付") + "结算单_" + configGridDTO.getSifBillSettlePageQuery()
                    .getSettleNo() + "." + fileExportTaskDTO.getTemplateType();
        }
        fileExportTaskDTO.setFileName(fileName);
        return workbook;
    }

    /**
     * 添加结算章
     */
    private void addSettlementSeal(Workbook workbook,String fileKey,int col,int row) throws IOException {
        //获取文件字节数组
        byte[] bytes = fileUploadSupportActionService.getFileByte(fileKey);
        // 在工作表中插入图片
        int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);

        // 创建一个画图的位置
        CreationHelper helper = workbook.getCreationHelper();
        ClientAnchor anchor = helper.createClientAnchor();

        // 设置图片的起始位置，这里设置为第一行，第一列
        anchor.setCol1(col);
        anchor.setRow1(row);

        // 根据anchor的位置，画图
        Drawing drawing = workbook.getSheetAt(0).createDrawingPatriarch();
        Picture picture = drawing.createPicture(anchor, pictureIdx);
        picture.resize();
    }

    @Override
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));

        List<ExcelExportEntity> colList = new ArrayList<>();
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        BillTypeEnum billType = configGridDTO.getSifBillSettlePageQuery().getBillType();
        Field[] fields = BillTypeEnum.isCustomer(billType) ? PayeeSettleBillDetailViewPageDTO.class.getDeclaredFields() : PaySettleBillDetailViewPageDTO.class.getDeclaredFields();
        for (Field field : fields) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                int sortOrder = annotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(beanPath);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            }
        }
        return colList;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.SIF_SETTLE_BILL_EXPORT;
    }


}
