package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.generic.agg.interfaces.dto.OrderDeliveryAggDTO;
import cn.genn.trans.support.fms.application.dto.datahub.DatahubDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 数据开放平台数据导出
 * 调用rpc client
 *
 * @Date: 2024/10/10
 * @Author: kangjian
 */
@Component("dataHubExcelExportServer")
@Slf4j
public class DataHubExcelExportServer extends ExcelExportAbstractServer {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RestTemplate lbRestTemplate;


    private String queryUrl = "/api/data-hub/chartView/callbackGetData";

    private String url = "http://genn-data-hub";


    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        String callbackParam = configGridDTO.getCallbackParam();
        int pageSize = 1000;
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            pageSize = taskInfo.getExportConfConfig().getPageSize();
        }
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");
        String finalUrl = url + queryUrl + "?pageNo=" + pageNo + "&pageSize=" + pageSize;
        DatahubDTO response = fetchPostResponse(finalUrl, callbackParam, headers);

        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return Collections.emptyList();
        }
        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * pageSize;
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        response.getData().forEach(dataMap -> {
            Map<String, Object> map = new HashMap<>();
            dataMap.forEach((fieldName, value) -> {
                Object fieldValue = value;
                String fieldValueStr = fieldValue == null ? "" : fieldValue.toString();
                map.put(fieldName, fieldValueStr);
            });
            resultList.add(map);
        });

        return resultList;
    }

    public static List<String> getJsonFormatAnnotatedFields(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> annotatedFieldNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(JsonFormat.class)) {
                annotatedFieldNames.add(field.getName());
            }
        }
        return annotatedFieldNames;
    }

    public static void main(String[] args) {
        Class<OrderDeliveryAggDTO> orderDeliveryAggDTOClass = OrderDeliveryAggDTO.class;
        List<String> fieldsWithJsonFormat = getJsonFormatAnnotatedFields(orderDeliveryAggDTOClass);

        System.out.println("Attributes with @JsonFormat in OrderDeliveryAggDTO:");
        for (String fieldName : fieldsWithJsonFormat) {
            System.out.println(fieldName);
        }
    }

    public DatahubDTO fetchPostResponse(String url, String callbackParam, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(callbackParam, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, callbackParam);
            ResponseEntity<String> responseEntity = lbRestTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<DatahubDTO> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<DatahubDTO>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
