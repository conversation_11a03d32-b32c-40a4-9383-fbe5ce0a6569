package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.OpsContainerOrderDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.genn.trans.support.fms.interfaces.query.business.OrdDeliverContainerPageQuery;
import cn.hutool.core.bean.BeanPath;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 提箱单数据导出
 * 调用rpc client
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("deliveryContainerExcelExportServer")
@Slf4j
public class DeliveryContainerExcelExportServer extends ExcelExportAbstractServer {

    @Resource
    private InternalDomainUtil internalDomainUtil;

    @Resource
    private RestTemplate restTemplate;

    private String opsQueryUrl = "/api/ops/order/container/page";
    private String tmsQueryUrl = "/api/tms/containerDelivery/page";

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        OrdDeliverContainerPageQuery deliverContainerQuery = configGridDTO.getDeliverContainerQuery();
        deliverContainerQuery.setTenantId(taskInfo.getTenantId());
        deliverContainerQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            deliverContainerQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * deliverContainerQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }
        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url;
        if (Objects.equals(SystemEnum.OPS.getCode(), taskInfo.getSystemId())) {
            url = serverUrl + opsQueryUrl;
        } else {
            url = serverUrl + tmsQueryUrl;
        }
        String requsetBody = JsonUtils.toJson(deliverContainerQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");
        PageResultDTO<OpsContainerOrderDTO> response = fetchPostResponse(url, requsetBody, headers);

        List<Object> resultList = new ArrayList<>();
        List<String> propertyNameList = new ArrayList<>();
        //        configGridDTO.getColList().sort(Comparator.comparing(ConfigGridColDTO::getSortOrder));
        configGridDTO.getColList().forEach(col -> {
            propertyNameList.add(col.getBackendPropertyName());
        });

        response.getList().forEach(aggDTO -> {
            Map<String, Object> map = new HashMap<>();
            propertyNameList.forEach(propertyName -> {
                BeanPath resolver = new BeanPath(propertyName);
                Object result = resolver.get(aggDTO);
                if (result instanceof LocalDateTime) {
                    map.put(propertyName, ((LocalDateTime) result).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } else {
                    map.put(propertyName, Optional.ofNullable(result).orElse(""));
                }
            });
            resultList.add(map);
        });
        return resultList;
    }

    public PageResultDTO<OpsContainerOrderDTO> fetchPostResponse(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<OpsContainerOrderDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<OpsContainerOrderDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
