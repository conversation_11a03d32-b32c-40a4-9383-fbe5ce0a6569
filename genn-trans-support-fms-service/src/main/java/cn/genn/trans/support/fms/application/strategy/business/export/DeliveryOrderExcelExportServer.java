package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.core.order.interfaces.enums.contract.ChargeUnitEnum;
import cn.genn.trans.core.sif.interfaces.dto.charge.BillStatDTO;
import cn.genn.trans.generic.agg.interfaces.dto.OrderDeliveryAggDTO;
import cn.genn.trans.support.fms.application.dto.DeliveryOrderPageDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.genn.trans.support.fms.interfaces.query.business.DeliveryOrderPageQuery;
import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 运单数据导出
 * 调用rpc client
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("deliveryOrderExcelExportServer")
@Slf4j
public class DeliveryOrderExcelExportServer extends ExcelExportAbstractServer {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private InternalDomainUtil internalDomainUtil;

    @Value("${genn.file.service.url:http://10.13.10.35:8108/api/fms/support/image/}")
    private String visitUrl;

    private String opsQueryUrl = "/api/ops/delivery/page";
    private String tmsQueryUrl = "/api/tms/delivery/page";


    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        DeliveryOrderPageQuery deliveryOrderPageQuery = configGridDTO.getDeliveryOrderPageQuery();
        deliveryOrderPageQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            deliveryOrderPageQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url;
        if (Objects.equals(SystemEnum.OPS.getCode(), taskInfo.getSystemId())) {
            url = serverUrl + opsQueryUrl;
        } else {
            url = serverUrl + tmsQueryUrl;
        }
        String requsetBody = JsonUtils.toJson(deliveryOrderPageQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");

        PageResultDTO<DeliveryOrderPageDTO> response = fetchPostResponse(url, requsetBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * deliveryOrderPageQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        List<String> propertyNameList = new ArrayList<>();
        //        configGridDTO.getColList().sort(Comparator.comparing(ConfigGridColDTO::getSortOrder));
        configGridDTO.getColList().forEach(col -> {
            propertyNameList.add(col.getBackendPropertyName());
        });

        response.getList().forEach(deliveryOrder -> {
            Map<String, Object> map = new HashMap<>();
            propertyNameList.forEach(propertyName -> {
                BeanPath resolver = new BeanPath(propertyName);
                Object result = resolver.get(deliveryOrder);
                if (result instanceof LocalDateTime) {
                    map.put(propertyName, ((LocalDateTime) result).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } else {
                    // 装车数量 和 卸车数量 兼容 吨 方 车
                    if (propertyName.equals("loadsNetWeight") || propertyName.equals("unloadNetWeight")) {
                        ChargeUnitEnum chargeUnit = deliveryOrder.getCarrierChargeUnit();
                        switch (chargeUnit) {
                            case TON:
                                if (Objects.nonNull(result) && "0.00".equals(result.toString())) {
                                    map.put(propertyName, "-");
                                    break;
                                }
                                Object finalResult = result;
                                Optional.ofNullable(result).ifPresent(value -> map.put(propertyName, finalResult));
                                break;
                            case SIDE:
                                if (propertyName.equals("loadsNetWeight")) {
                                    resolver = new BeanPath("loadsVolume");
                                } else {
                                    resolver = new BeanPath("unloadVolume");
                                }
                                result = resolver.get(deliveryOrder);
                                if (Objects.nonNull(result) && "0.00".equals(result.toString())) {
                                    map.put(propertyName, "-");
                                    break;
                                }
                                map.put(propertyName, result);
                                break;
                            case TRUCK:
                                // 判断是否有装卸时间
                                if (propertyName.equals("loadsNetWeight")) {
                                    if (Objects.nonNull(deliveryOrder.getLoadsTime())) {
                                        map.put(propertyName, 1);
                                    }
                                } else {
                                    if (Objects.nonNull(deliveryOrder.getUnloadTime())) {
                                        map.put(propertyName, 1);
                                    }
                                }
                                break;
                            default:
                                break;
                        }
                    } else if (propertyName.equals("demandTaxPrice") || propertyName.equals("carrierTaxPrice")) {
                        map.put(propertyName, result + "元/" + deliveryOrder.getCarrierChargeUnit().getDescription());
                    } else if (propertyName.equals("vehicleCodeName")) {
                        // 车辆类型后面追加挂车车辆类型
                        String vehicleCodeName = (String) result;
                        if (StrUtil.isNotBlank(deliveryOrder.getTrailerTypeName())) {
                            vehicleCodeName = vehicleCodeName + "-" + deliveryOrder.getTrailerTypeName();
                        }
                        map.put(propertyName, vehicleCodeName);
                    } else if (propertyName.equals("carrierBill.chargeCount")) {
                        // 计费单位如果为车 转换为 计费数量为整数
                        if (Objects.isNull(deliveryOrder.getCarrierBill()) || Objects.isNull(deliveryOrder.getCarrierBill().getChargeUnit())) {
                            map.put(propertyName, "-");
                            return;
                        }
                        cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum chargeUnit = deliveryOrder.getCarrierBill().getChargeUnit();
                        if (chargeUnit == cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum.TRUCK) {
                            map.put(propertyName, Optional.ofNullable(deliveryOrder.getCarrierBill()).map(BillStatDTO::getChargeCount).map(BigDecimal::intValue).orElse(0));
                        } else {
                            map.put(propertyName, Optional.ofNullable(deliveryOrder.getCarrierBill()).map(BillStatDTO::getChargeCount).map(BigDecimal::toString).orElse("-"));
                        }

                    } else if (propertyName.equals("customerBill.chargeCount")) {
                        // 计费单位如果为车 转换为 计费数量为整数
                        if (Objects.isNull(deliveryOrder.getCustomerBill()) || Objects.isNull(deliveryOrder.getCustomerBill().getChargeUnit())) {
                            map.put(propertyName, "-");
                            return;
                        }
                        cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum chargeUnit = deliveryOrder.getCustomerBill().getChargeUnit();
                        if (chargeUnit == cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum.TRUCK) {
                            map.put(propertyName, Optional.ofNullable(deliveryOrder.getCustomerBill()).map(BillStatDTO::getChargeCount).map(BigDecimal::intValue).orElse(0));
                        } else {
                            map.put(propertyName, Optional.ofNullable(deliveryOrder.getCustomerBill()).map(BillStatDTO::getChargeCount).map(BigDecimal::toString).orElse("-"));
                        }
                    } else if (propertyName.equals("loadsPoundPic") || propertyName.equals("unloadPoundPic")) {
                        map.put(propertyName, StrUtil.isNotBlank((String) result) ? visitUrl + result : "");
                    } else {
                        map.put(propertyName, Optional.ofNullable(result).orElse(""));
                    }
                }
            });
            resultList.add(map);
        });

        return resultList;
    }

    public static List<String> getJsonFormatAnnotatedFields(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> annotatedFieldNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(JsonFormat.class)) {
                annotatedFieldNames.add(field.getName());
            }
        }
        return annotatedFieldNames;
    }

    public static void main(String[] args) {
        Class<OrderDeliveryAggDTO> orderDeliveryAggDTOClass = OrderDeliveryAggDTO.class;
        List<String> fieldsWithJsonFormat = getJsonFormatAnnotatedFields(orderDeliveryAggDTOClass);

        System.out.println("Attributes with @JsonFormat in OrderDeliveryAggDTO:");
        for (String fieldName : fieldsWithJsonFormat) {
            System.out.println(fieldName);
        }
    }

    public PageResultDTO<DeliveryOrderPageDTO> fetchPostResponse(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<DeliveryOrderPageDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<DeliveryOrderPageDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
