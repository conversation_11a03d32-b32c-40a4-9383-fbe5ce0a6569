package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 需求订单导出
 *
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Component("demandOrderExcelExportStrategy")
public class DemandOrderExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private DemandOrderExcelExportServer demandOrderExcelExportServer;

    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(fileExportTaskDTO.getFileName());
        //        exportParams.setAddIndex(true);
        exportParams.setAutoSize(true);
        exportParams.setCreateHeadRows(true);
        return exportParams;
    }

    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("列1", "属性1");
        ExcelExportEntity colEntity1 = new ExcelExportEntity("列2", "属性2");
        colList.add(colEntity);
        colList.add(colEntity1);
        return colList;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, demandOrderExcelExportServer, fileExportTaskDTO);
        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        // 在第一行之前插入新的行
        ExcelHandleUtil.insertRow(sheet, 0);
        Row newRow = sheet.getRow(0);
        Cell newCell = newRow.createCell(0);
        newCell.setCellValue("需求订单导出时间范围: 2023-01-01 00:00:00 - 2023-01-31 23:59:59");
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.DEMAND_ORDER_EXPORT;
    }

}
