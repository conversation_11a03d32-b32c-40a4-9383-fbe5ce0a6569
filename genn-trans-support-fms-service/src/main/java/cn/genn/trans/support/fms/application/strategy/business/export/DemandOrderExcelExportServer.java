package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.query.business.DemandOrderQuery;
import cn.hutool.core.map.MapUtil;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 需求订单导出查询
 * 调用rpc client
 *
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Component("demandOrderExcelExportServer")
public class DemandOrderExcelExportServer extends ExcelExportAbstractServer {

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        // TODO client调用 某些情况下要考虑租户的vpcGroup
        ConfigGridDTO configGridDTO =  JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        DemandOrderQuery demandOrderQuery = configGridDTO.getDemandOrderQuery();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            demandOrderQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }
        List<Object> list = new ArrayList<>();
        Map<String, String> build = MapUtil.builder(new HashMap<String, String>())
            .put("属性1", "PropertyName1_" + pageNo)
            .put("属性2", "PropertyName2_" + pageNo)
            .put("key2", "value2").build();
        // 模拟下查询到最后一页
        if (pageNo != 1) {
            return new ArrayList<>();
        }
        list.add(build);
        return list;
    }
}
