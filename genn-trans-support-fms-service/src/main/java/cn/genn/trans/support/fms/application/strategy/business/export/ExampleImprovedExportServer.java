package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.http.MagicTokenHttpClient;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 使用新方案的导出服务示例
 * 展示如何优雅地处理magic-token，避免在业务逻辑中手动添加
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component("exampleImprovedExportServer")
@Slf4j
public class ExampleImprovedExportServer extends ExcelExportAbstractServer {

    @Resource
    private MagicTokenHttpClient magicTokenHttpClient;
    
    @Resource
    private InternalDomainUtil internalDomainUtil;

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        int pageSize = getPageSize(taskInfo);

        if (checkExportLimit(taskInfo, pageNo, pageSize)) {
            return Collections.emptyList();
        }

        // 方案1：使用MagicTokenHttpClient（推荐）
        return queryDataUsingHttpClient(taskInfo, configGridDTO, pageNo, pageSize);
        
        // 方案2：使用改造后的基类（ExcelExportAbstractServer已经设置了MagicTokenContext）
        // return queryDataUsingRestTemplate(taskInfo, configGridDTO, pageNo, pageSize);
    }

    /**
     * 方案1：使用MagicTokenHttpClient
     * 优点：封装了所有HTTP调用逻辑，使用简单，类型安全
     */
    private List<Object> queryDataUsingHttpClient(FileExportTaskDTO taskInfo, ConfigGridDTO configGridDTO, int pageNo, int pageSize) {
        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url = serverUrl + "/api/example/data/page";
        
        // 构建查询参数
        ExampleQuery query = ExampleQuery.builder()
            .pageNo(pageNo)
            .pageSize(pageSize)
            .businessParam(configGridDTO.getCallbackParam())
            .build();

        // 使用MagicTokenHttpClient发送请求，自动处理magic-token
        // tokenData会从MagicTokenContext中自动获取（由基类设置）
        PageResultDTO<Object> result = magicTokenHttpClient.postForObject(
            url, 
            query, 
            new TypeReference<ResponseResult<PageResultDTO<Object>>>() {}
        );

        return result != null ? result.getList() : Collections.emptyList();
    }

    /**
     * 方案2：使用RestTemplate（如果你想继续使用RestTemplate）
     * 优点：利用了RestTemplate拦截器，自动添加magic-token
     */
    @Resource
    private org.springframework.web.client.RestTemplate restTemplate; // 已配置MagicToken拦截器

    private List<Object> queryDataUsingRestTemplate(FileExportTaskDTO taskInfo, ConfigGridDTO configGridDTO, int pageNo, int pageSize) {
        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url = serverUrl + "/api/example/data/page";
        
        // 构建查询参数
        ExampleQuery query = ExampleQuery.builder()
            .pageNo(pageNo)
            .pageSize(pageSize)
            .businessParam(configGridDTO.getCallbackParam())
            .build();

        // 使用RestTemplate发送请求，拦截器会自动添加magic-token
        // 注意：不需要手动添加magic-token请求头
        org.springframework.http.HttpEntity<ExampleQuery> httpEntity = 
            new org.springframework.http.HttpEntity<>(query);

        try {
            org.springframework.http.ResponseEntity<String> responseEntity = 
                restTemplate.postForEntity(url, httpEntity, String.class);
            
            if (org.springframework.http.HttpStatus.OK == responseEntity.getStatusCode()) {
                ResponseResult<PageResultDTO<Object>> result = JsonUtils.parse(
                    responseEntity.getBody(), 
                    new TypeReference<ResponseResult<PageResultDTO<Object>>>() {}
                );
                
                if (result.isSuccess() && result.getData() != null) {
                    return result.getData().getList();
                }
            }
        } catch (Exception e) {
            log.error("调用远程服务失败", e);
        }

        return Collections.emptyList();
    }

    /**
     * 示例查询对象
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ExampleQuery {
        private int pageNo;
        private int pageSize;
        private String businessParam;
    }
}
