package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.datahub.DatahubDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 改进版数据开放平台数据导出 - 使用自动magic-token注入
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component("improvedDataHubExcelExportServer")
@Slf4j
public class ImprovedDataHubExcelExportServer extends ExcelExportAbstractServer {
    
    @Resource
    private RestTemplate restTemplate; // 这个RestTemplate已经配置了MagicToken拦截器

    private String queryUrl = "/api/data-hub/chartView/callbackGetData";
    private String url = "http://genn-data-hub";

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        String callbackParam = configGridDTO.getCallbackParam();
        int pageSize = getPageSize(taskInfo);

        if (checkExportLimit(taskInfo, pageNo, pageSize)) {
            return Collections.emptyList();
        }

        // 注意：这里不再需要手动添加magic-token请求头
        // RestTemplate拦截器会自动从MagicTokenContext中获取tokenData并添加magic-token请求头
        String finalUrl = url + queryUrl + "?pageNo=" + pageNo + "&pageSize=" + pageSize;
        DatahubDTO response = fetchPostResponse(finalUrl, callbackParam);

        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return Collections.emptyList();
        }

        return response.getData();
    }

    /**
     * 简化的HTTP请求方法 - 不再需要手动处理magic-token
     */
    private DatahubDTO fetchPostResponse(String url, String callbackParam) {
        // 创建请求体，只需要设置Content-Type，magic-token会自动添加
        HttpEntity<String> httpEntity = new HttpEntity<>(callbackParam);
        
        try {
            log.info("远程调用, url: {}, body: {}", url, callbackParam);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }
            
            log.info("远程调用, result: {}", responseEntity.getBody());
            ResponseResult<DatahubDTO> result = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<DatahubDTO>>() {});
            
            if (result.isSuccess()) {
                return result.getData();
            } else {
                log.error("远程调用失败: {}", result.getMessage());
                throw new BusinessException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("远程调用异常", e);
            throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
        }
    }
}
