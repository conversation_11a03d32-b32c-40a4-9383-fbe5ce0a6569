package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.api.common.enums.order.OrderBusinessTypeEnum;
import cn.genn.trans.core.order.interfaces.enums.contract.ChargeUnitEnum;
import cn.genn.trans.generic.agg.interfaces.dto.OrderDeliveryAggDTO;
import cn.genn.trans.support.fms.application.dto.PoundReviewPageDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.query.business.PoundReviewOpsPageQuery;
import cn.hutool.core.bean.BeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
@Component("poundReviewOpsExcelExportServer")
public class PoundReviewOpsExcelExportServer extends ExcelExportAbstractServer {

    @Resource
    private InternalDomainUtil internalDomainUtil;
    @Resource
    private RestTemplate restTemplate;

    private String opsQueryUrl = "/api/ops/pound/review/page";

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        PoundReviewOpsPageQuery poundReviewOpsPageQuery = configGridDTO.getPoundReviewOpsPageQuery();
        poundReviewOpsPageQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            poundReviewOpsPageQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url = serverUrl + opsQueryUrl;
        String requsetBody = JsonUtils.toJson(poundReviewOpsPageQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");

        PageResultDTO<PoundReviewPageDTO> response = fetchPostResponse(url, requsetBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * poundReviewOpsPageQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        for (PoundReviewPageDTO dto : response.getList()) {
            Map<String, Object> map = new HashMap<>();
            // 货物取值位数
            int digits = Integer.parseInt(Optional.ofNullable(dto.getDigits()).orElse("2"));
            // 货物单位
            ChargeUnitEnum chargeUnit = Optional.ofNullable(dto.getChargeUnit()).orElse(ChargeUnitEnum.TON);
            // 业务类型
            OrderBusinessTypeEnum businessType = Optional.ofNullable(dto.getBusinessType()).orElse(OrderBusinessTypeEnum.EXTERNAL_TRANSPORT);
            for (Field field : dto.getClass().getDeclaredFields()) {
                DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
                if (annotation != null) {
                    String beanPath = annotation.beanPath();
                    String fieldName = annotation.fieldName();
                    Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                    BeanPath resolver = new BeanPath(beanPath);
                    Object value = resolver.get(dto);
                    if (!wrapperClass.equals(DataWrapper.class)) {
                        try {
                            DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                            value = wrapper.wrap(value);
                        } catch (Exception e) {
                            log.error("DataWrapper实例化失败", e);
                        }
                    }
                    // 货物重量和体积处理;1.吨:显示毛,皮,净,扣;2.方:体积;3.件,车:重量,体积
                    if (Objects.nonNull(value)) {
                        if (chargeUnit.equals(ChargeUnitEnum.TON)) {
                            if (beanPath.equals("loadingNetWeight") ||
                                beanPath.equals("loadingTareWeight") || beanPath.equals("loadingGrossWeight") ||
                                beanPath.equals("dischargeNetWeight") || beanPath.equals("dischargeTareWeight") ||
                                beanPath.equals("dischargeGrossWeight") || beanPath.equals("dischargeDeductWeight")) {
                                map.put(beanPath, this.roundToDecimalPlaces(value, digits));
                                continue;
                            }
                            if (beanPath.equals("loadingVolume") || beanPath.equals("dischargeVolume")) {
                                map.put(beanPath, "");
                                continue;
                            }

                        } else if (chargeUnit.equals(ChargeUnitEnum.SIDE)) {
                            if (beanPath.equals("loadingVolume") || beanPath.equals("dischargeVolume")) {
                                map.put(beanPath, this.roundToDecimalPlaces(value, 2));
                                continue;
                            }
                            if (beanPath.equals("loadingNetWeight") ||
                                beanPath.equals("loadingTareWeight") || beanPath.equals("loadingGrossWeight") ||
                                beanPath.equals("dischargeNetWeight") || beanPath.equals("dischargeTareWeight") ||
                                beanPath.equals("dischargeGrossWeight") || beanPath.equals("dischargeDeductWeight")) {
                                map.put(beanPath, "");
                                continue;
                            }
                        } else {
                            if (beanPath.equals("loadingVolume") || beanPath.equals("dischargeVolume")) {
                                map.put(beanPath, this.roundToDecimalPlaces(value, 2));
                                continue;
                            }
                            if (beanPath.equals("loadingNetWeight") || beanPath.equals("dischargeNetWeight")) {
                                map.put(beanPath, this.roundToDecimalPlaces(value, digits));
                                continue;
                            }
                            if (beanPath.equals("loadingTareWeight") || beanPath.equals("loadingGrossWeight") ||
                                beanPath.equals("dischargeTareWeight") ||
                                beanPath.equals("dischargeGrossWeight") || beanPath.equals("dischargeDeductWeight")) {
                                map.put(beanPath, "");
                                continue;
                            }
                        }
                    }
                    map.put(beanPath, Optional.ofNullable(value).orElse(""));
                }
            }
            resultList.add(map);
        }
        return resultList;
    }

    public static List<String> getJsonFormatAnnotatedFields(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> annotatedFieldNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(JsonFormat.class)) {
                annotatedFieldNames.add(field.getName());
            }
        }
        return annotatedFieldNames;
    }

    public static void main(String[] args) {
        Class<OrderDeliveryAggDTO> orderDeliveryAggDTOClass = OrderDeliveryAggDTO.class;
        List<String> fieldsWithJsonFormat = getJsonFormatAnnotatedFields(orderDeliveryAggDTOClass);

        System.out.println("Attributes with @JsonFormat in OrderDeliveryAggDTO:");
        for (String fieldName : fieldsWithJsonFormat) {
            System.out.println(fieldName);
        }
    }

    public PageResultDTO<PoundReviewPageDTO> fetchPostResponse(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<PoundReviewPageDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<PoundReviewPageDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }

    public String roundToDecimalPlaces(Object value, int digits) {
        BigDecimal bigDecimalValue = (BigDecimal) value;
        String result = bigDecimalValue.setScale(digits, RoundingMode.HALF_UP).toString();
        if (result.indexOf('.') > -1) {
            // 去除末尾的 0
            while (result.endsWith("0")) {
                result = result.substring(0, result.length() - 1);
            }
            // 如果只剩下小数点，也去除
            if (result.endsWith(".")) {
                result = result.substring(0, result.length() - 1);
            }
        }
        return result;
    }
}
