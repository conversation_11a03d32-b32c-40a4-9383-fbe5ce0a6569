package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.PoundReviewPageDTO;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component("poundReviewTmsExcelExportStrategy")
public class PoundReviewTmsExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private PoundReviewTmsExcelExportServer poundReviewTmsExcelExportServer;

    private final static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(fileExportTaskDTO.getFileName());
        // exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        exportParams.setCreateHeadRows(true);
        exportParams.setFixedTitle(false);
        //        exportParams.setTitle(fileExportTaskDTO.getTaskName());
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, poundReviewTmsExcelExportServer, fileExportTaskDTO);
        fileExportTaskDTO.setFileName("磅单审核明细" + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "." +  fileExportTaskDTO.getTemplateType());
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.POUND_REVIEW_TMS_EXPORT;
    }

    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));

        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = PoundReviewPageDTO.class.getDeclaredFields();
        for (Field field : fields) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                int sortOrder = annotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(beanPath);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            }
        }
        return colList;
    }

}
