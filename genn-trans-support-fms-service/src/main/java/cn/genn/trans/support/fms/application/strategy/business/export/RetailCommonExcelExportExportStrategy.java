package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.extension.FileNameExtension;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 补能业务通用数据导出
 *
 * @Date: 2024/10/10
 * @Author: kangjian
 */
@Component("retailCommonExcelExportStrategy")
@Slf4j
public class RetailCommonExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private RetailCommonExcelExportServer server;

    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(fileExportTaskDTO.getFileName());
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
//        exportParams.setTitle(fileExportTaskDTO.getTaskName());
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = this.getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, server, fileExportTaskDTO);
        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }
        fileExportTaskDTO.setFileName(FileNameExtension.generateFileName(fileExportTaskDTO));
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.RETAIL_COMMON_EXPORT;
    }


    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));
        if (Objects.isNull(fileExportTaskDTO.getTaskParam())) {
            return null;
        }
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        List<ExcelExportEntity> colList = new ArrayList<>();
        if (Objects.isNull(configGridDTO) || Objects.isNull(configGridDTO.getColList())) {
            return null;
        }
        //        configGridDTO.getColList().sort(Comparator.comparing(ConfigGridColDTO::getSortOrder));
        configGridDTO.getColList().forEach(col -> {
            ExcelExportEntity excelExportEntity = new ExcelExportEntity();
            excelExportEntity.setName(col.getColName());
            excelExportEntity.setKey(col.getBackendPropertyName());
            excelExportEntity.setOrderNum(col.getSortOrder());
            colList.add(excelExportEntity);
        });
        return colList;
    }
}
