package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.extension.DataMappingStrategy;
import cn.genn.trans.support.fms.application.extension.DataMappingStrategyFactory;
import cn.genn.trans.support.fms.application.extension.RemoteExtension;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridColDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.hutool.core.collection.CollectionUtil;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 补能业务通用数据导出
 * 调用rpc client
 *
 * @Date: 2024/10/10
 * @Author: kangjian
 */
@Component("retailCommonExcelExportServer")
@Slf4j
public class RetailCommonExcelExportServer extends ExcelExportAbstractServer {
    @Resource
    private RemoteExtension remoteExtension;
    @Autowired
    private DataMappingStrategyFactory dataMappingStrategyFactory;

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        String callbackParam = configGridDTO.getCallbackParam();
        int pageSize = getPageSize(taskInfo);

        if (checkExportLimit(taskInfo, pageNo, pageSize)) {
            return Collections.emptyList();
        }


        // callbackParam 解析为JsonObject 然后 加入值 pageNo pageSize
        String updatedCallBackParam = remoteExtension.handlePageParam(callbackParam, pageNo, pageSize);
        TypeToken<Map<String, String>> mapTypeToken = new TypeToken<Map<String, String>>() {
        };
        PageResultDTO<Map<String, String>> response = remoteExtension.fetchPostPageResponse(taskInfo.getExportConfConfig()
                .getRestTemplateStrategy(), remoteExtension.generateQueryUrl(taskInfo), updatedCallBackParam, taskInfo.getTokenData(), mapTypeToken);

        if (Objects.isNull(response) || CollectionUtil.isEmpty(response.getList())) {
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        List<String> propertyNameList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(configGridDTO.getColList())) {
            configGridDTO.getColList().forEach(col -> {
                propertyNameList.add(col.getBackendPropertyName());
            });
        } else {
            List<ConfigGridColDTO> colList = taskInfo.getExportConfConfig().getColList();
            if (CollectionUtil.isEmpty(colList)) {
                throw new BusinessException(MessageCode.BUSINESS_CONFIG_COL_LISTNOT_EXIST);
            }
        }

        // 获取当前配置的策略类
        String strategyClass = taskInfo.getExportConfConfig().getDataMappingStrategyClass();
        DataMappingStrategy dataMappingStrategy = dataMappingStrategyFactory.getStrategy(strategyClass);
        response.getList().forEach(dataMap -> {
            Map<String, Object> mappedData = dataMappingStrategy.mapData(dataMap, propertyNameList);
            resultList.add(mappedData);
        });
        return resultList;
    }
}
