package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.TempEstimateBillDTO;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 暂估单导出策略
 * @date 2024-11-05
 */
@Component("tempEstimateBillExcelExportStrategy")
@Slf4j
public class TempEstimateBillExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private TempEstimateBillExcelExportServer tempEstimateBillExcelExportServer;

    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("暂估单");
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, tempEstimateBillExcelExportServer, fileExportTaskDTO);
        // 已完成明细数据 开始进行汇总数据的模板填充
        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }

        String suffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        fileExportTaskDTO.setFileName(exportParams.getSheetName() + "_" + suffix + "." + fileExportTaskDTO.getTemplateType());
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.TEMP_ESTIMATE_BILL_EXPORT;
    }

    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));

        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = TempEstimateBillDTO.class.getDeclaredFields();
        for (Field field : fields) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                int sortOrder = annotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(beanPath);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            }
        }
        return colList;

    }
}
