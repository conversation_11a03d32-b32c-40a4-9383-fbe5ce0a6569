package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.core.sif.interfaces.enums.ChargeUnitEnum;
import cn.genn.trans.support.fms.application.dto.TempEstimateBillDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.InternalDomainUtil;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.query.business.TempEstimateBillQuery;
import cn.hutool.core.bean.BeanPath;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description 暂估单导出
 * @date 2024-11-05
 */
@Slf4j
@Component("tempEstimateBillExcelExportServer")
public class TempEstimateBillExcelExportServer extends ExcelExportAbstractServer {

    @Resource
    private InternalDomainUtil internalDomainUtil;
    @Resource
    private RestTemplate restTemplate;

    private final String queryUrl = "/api/ops/tempEstimate/bill/page";

    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        TempEstimateBillQuery tempEstimateBillQuery = configGridDTO.getTempEstimateBillQuery();
        tempEstimateBillQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            tempEstimateBillQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        String serverUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), taskInfo.getSystemId());
        String url = serverUrl + queryUrl;
        String requestBody = JsonUtils.toJson(tempEstimateBillQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");

        PageResultDTO<TempEstimateBillDTO> response = fetchPostResponse(url, requestBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * tempEstimateBillQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        for (TempEstimateBillDTO dto : response.getList()) {
            Map<String, Object> map = new HashMap<>();
            for (Field field : dto.getClass().getDeclaredFields()) {
                DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
                if (annotation != null) {
                    String beanPath = annotation.beanPath();
                    Class<? extends DataWrapper> wrapperClass = annotation.wrapperClass();
                    BeanPath resolver = new BeanPath(beanPath);
                    Object value = resolver.get(dto);
                    if (!wrapperClass.equals(DataWrapper.class)) {
                        try {
                            DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                            value = wrapper.wrap(value);
                        } catch (Exception e) {
                            log.error("DataWrapper实例化失败", e);
                        }
                    }
                    if (beanPath.equals("loadingValue") || beanPath.equals("unloadingValue")) {
                        // 如果是车，需要改为整数
                        ChargeUnitEnum chargeUnit = dto.getChargeUnit();
                        if (ChargeUnitEnum.isTruck(chargeUnit)) {
                            value = ((BigDecimal) value).intValue();
                        }
                    }
                    map.put(beanPath, Optional.ofNullable(value).orElse(""));
                }
            }
            resultList.add(map);
        }
        return resultList;
    }

    public static List<String> getJsonFormatAnnotatedFields(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> annotatedFieldNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(JsonFormat.class)) {
                annotatedFieldNames.add(field.getName());
            }
        }
        return annotatedFieldNames;
    }

    public PageResultDTO<TempEstimateBillDTO> fetchPostResponse(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<TempEstimateBillDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference<ResponseResult<PageResultDTO<TempEstimateBillDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
