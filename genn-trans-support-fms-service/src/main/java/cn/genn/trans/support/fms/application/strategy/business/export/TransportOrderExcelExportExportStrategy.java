package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.query.business.TransportOrderQuery;
import cn.hutool.core.date.DateUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 拉运数据报表导出
 */
@Component("transportOrderExcelExportStrategy")
public class TransportOrderExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private TransportOrderExcelExportServer transportOrderExcelExportServer;

    private final static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(fileExportTaskDTO.getFileName());
        // exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        exportParams.setCreateHeadRows(true);
        exportParams.setFixedTitle(false);
        //        exportParams.setTitle(fileExportTaskDTO.getTaskName());
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, transportOrderExcelExportServer, fileExportTaskDTO);
        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);
        ExcelHandleUtil.autoSizeHeader(sheet);
        // 在第一行之前插入新的行
        ExcelHandleUtil.insertRow(sheet, 0);
        Row newRow = sheet.getRow(0);
        Cell newCell = newRow.createCell(0);
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        TransportOrderQuery transportOrderQuery = configGridDTO.getTransportOrderQuery();
        String cellValue = "时间范围:" + DateUtil.format(transportOrderQuery.getStartTime(), TIME_FORMAT)
            + "-" + DateUtil.format(transportOrderQuery.getEndTime(), TIME_FORMAT);
        newCell.setCellValue(cellValue);
        // 定义要合并的单元格区域
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, excelExportEntities.size() - 1);
        // 合并单元格
        sheet.addMergedRegion(cellRangeAddress);
        fileExportTaskDTO.setFileName("拉运数据导出" + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "." +  fileExportTaskDTO.getTemplateType());
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.TRANSPORT_ORDER_EXPORT;
    }



}
