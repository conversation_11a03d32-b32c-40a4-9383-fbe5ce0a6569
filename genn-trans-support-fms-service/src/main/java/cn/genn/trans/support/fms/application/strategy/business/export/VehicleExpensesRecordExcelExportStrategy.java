package cn.genn.trans.support.fms.application.strategy.business.export;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.VehicleExpensesRecordDTO;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.AuditStatusEnum;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.SettleStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 随车费用记录数据导出
 *
 * <AUTHOR>
 */
@Component("vehicleExpensesRecordExcelStrategy")
@Slf4j
public class VehicleExpensesRecordExcelExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private VehicleExpensesRecordExcelServer vehicleExpensesRecordExcelServer;

    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        AuditStatusEnum auditStatus = configGridDTO.getSifVehicleExpensesQuery().getAuditStatus();
        SettleStatusEnum settleStatus = configGridDTO.getSifVehicleExpensesQuery().getSettleStatus();
        String sheetName = "随车费用";
        switch (auditStatus){
            case WAIT_REVIEW:
                sheetName = sheetName + "_待审核";
                break;
            case APPROVED:
                sheetName = sheetName + (SettleStatusEnum.WAIT_SUMMARIZE.equals(settleStatus) ? "_待汇总" : "_已审核");
                break;
            case REJECTED:
                sheetName = sheetName + "_已驳回";
                break;
            default:
                break;
        }
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(sheetName);
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        return exportParams;
    }

    @Override
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO);
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, vehicleExpensesRecordExcelServer, fileExportTaskDTO);
        // 已完成明细数据 开始进行汇总数据的模板填充
        int numberOfSheets = workbook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }

        String suffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        fileExportTaskDTO.setFileName(exportParams.getSheetName() + "_" + suffix + "." + fileExportTaskDTO.getTemplateType());
        return workbook;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.VEHICLE_EXPENSES_RECORD_EXPORT;
    }

    /**
     * 需要导出字段和数据map的映射
     *
     * @param fileExportTaskDTO
     * @return
     */
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));

        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = VehicleExpensesRecordDTO.class.getDeclaredFields();
        for (Field field : fields) {
            DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
            if (annotation != null) {
                String beanPath = annotation.beanPath();
                String fieldName = annotation.fieldName();
                int sortOrder = annotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(beanPath);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            }
        }
        return colList;
    }
}
