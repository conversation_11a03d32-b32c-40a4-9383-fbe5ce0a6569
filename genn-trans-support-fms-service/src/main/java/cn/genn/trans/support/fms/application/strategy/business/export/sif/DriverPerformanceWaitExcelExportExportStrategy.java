package cn.genn.trans.support.fms.application.strategy.business.export.sif;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.sif.DriverDeductDTO;
import cn.genn.trans.support.fms.application.dto.sif.DriverPerformanceInfoDTO;
import cn.genn.trans.support.fms.application.dto.sif.DriverWaitSummaryDTO;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractExportStrategy;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanObject;
import cn.genn.trans.support.fms.infrastructure.utils.DataBeanPath;
import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 司机绩效待确认待汇总数据导出
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("driverPerformanceWaitExcelExportStrategy")
@Slf4j
public class DriverPerformanceWaitExcelExportExportStrategy extends CommonAbstractExportStrategy {

    @Resource
    private DriverPerformanceWaitConfirmExportServer waitConfirmExportServer;
    @Resource
    private DriverPerformanceWaitSummaryExportServer waitSummaryExportServer;
    @Resource
    private DriverPerformanceWaitOrderExportServer waitOrderExportServer;

    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    /**
     * Excel的相关设置 支持多sheet等等
     *
     * @param fileExportTaskDTO
     * @return
     */
    @Override
    public ExportParams getExportParams(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = new ExportParams();
        exportParams.setAddIndex(false);
        exportParams.setAutoSize(true);
        return exportParams;
    }

    @Override
    @SneakyThrows
    public Workbook executeTask(FileExportTaskDTO fileExportTaskDTO) {
        ExportParams exportParams = getExportParams(fileExportTaskDTO);
        List<ExcelExportEntity> excelExportEntities = getExcelExportEntityList(fileExportTaskDTO, DriverDeductDTO.class);
        Workbook workbook1 = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, waitConfirmExportServer, fileExportTaskDTO);

        // 获取当前的 sheet
        workbook1.setSheetName(0, "合同线路统计");
        Sheet sheet11 = workbook1.getSheetAt(0);
        // 判断是应收还是应付结算单
        excelExportEntities = getExcelExportEntityList(fileExportTaskDTO, DriverWaitSummaryDTO.class);
        Workbook workbook2 = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, waitSummaryExportServer, fileExportTaskDTO);
        Sheet sheet2 = workbook2.getSheetAt(0);
        // 先写入到个临时文件再获取sheet 不然内存中只有最后100行的数据
        InputStream fileInputStream2 = ExcelHandleUtil.toInputStream(workbook2);
        String outputFileUrl2 = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileExportTaskDTO.getTemplateType();
        FileUtils.forceMkdirParent(new File(outputFileUrl2));
        // 将模板输入流保存到临时文件
        File tempTemplateFile2 = new File(outputFileUrl2);
        FileUtils.copyInputStreamToFile(fileInputStream2, tempTemplateFile2);

        excelExportEntities = getExcelExportEntityList(fileExportTaskDTO, DriverPerformanceInfoDTO.class);
        Workbook workbook3 = ExcelExportUtil.exportBigExcel(exportParams, excelExportEntities, waitOrderExportServer, fileExportTaskDTO);
        Sheet sheet3 = workbook3.getSheetAt(0);
        // 先写入到个临时文件再获取sheet 不然内存中只有最后100行的数据
        InputStream fileInputStream3 = ExcelHandleUtil.toInputStream(workbook3);
        String outputFileUrl3 = this.octTempFilePath + "file_template_" + UUID.randomUUID() + "." + fileExportTaskDTO.getTemplateType();
        FileUtils.forceMkdirParent(new File(outputFileUrl3));
        // 将模板输入流保存到临时文件
        File tempTemplateFile3 = new File(outputFileUrl3);
        FileUtils.copyInputStreamToFile(fileInputStream3, tempTemplateFile3);

        // 把sheet2 sheet3 移动到 workbook
        Sheet sheet12 = workbook1.createSheet("司机统计");
        Sheet sheet13 = workbook1.createSheet("运单绩效明细");


        // 从临时文件中
        try (FileInputStream fis = new FileInputStream(outputFileUrl2);
             XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fis)) {
            Sheet xssfSheet = xssfWorkbook.getSheetAt(0);
            ExcelHandleUtil.copySheet(xssfSheet, sheet12);
        } catch (IOException e) {
            e.printStackTrace();
        }

        try (FileInputStream fis = new FileInputStream(outputFileUrl3);
             XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fis)) {
            Sheet xssfSheet = xssfWorkbook.getSheetAt(0);
            ExcelHandleUtil.copySheet(xssfSheet, sheet13);
        } catch (IOException e) {
            e.printStackTrace();
        }

        workbook1.setActiveSheet(0);
        workbook2.close();
        workbook3.close();
        // 调整列宽
        int numberOfSheets = workbook1.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++) {
            Sheet sheet = workbook1.getSheetAt(i);
            ExcelHandleUtil.autoSizeHeader(sheet);
        }

        ConfigGridDTO configGridDTO = JsonUtils.parse(fileExportTaskDTO.getTaskParam(), ConfigGridDTO.class);
        fileExportTaskDTO.setFileName("司机绩效导出" + "_" + configGridDTO.getDriverWaitConfirmQuery().getStatStatus()
                .getDescription() + "_" + LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "." + fileExportTaskDTO.getTemplateType());

        // 最后删除临时文件
        FileUtils.deleteQuietly(new File(outputFileUrl2));
        FileUtils.deleteQuietly(new File(outputFileUrl3));
        return workbook1;
    }

    //    @Override
    public List<ExcelExportEntity> getExcelExportEntityList(FileExportTaskDTO fileExportTaskDTO, Class<?> clazz) {
        log.info("getExcelExportEntityList:{}", JsonUtils.toJson(fileExportTaskDTO));
        List<ExcelExportEntity> colList = new ArrayList<>();
        process("", clazz, colList);
        return colList;
    }

    @Override
    public ExportTaskBusinessCodeEnum getBusinessCode() {
        return ExportTaskBusinessCodeEnum.DRIVER_PERFORMANCE_WAIT_EXPORT;
    }


    @SneakyThrows
    public static void process(String parent, Class<?> clazz, List<ExcelExportEntity> colList) {
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(DataBeanPath.class)) {
                DataBeanPath dataBeanPathAnnotation = field.getAnnotation(DataBeanPath.class);
                String path = StrUtil.isNotBlank(parent) ? parent + "." + dataBeanPathAnnotation.beanPath() : dataBeanPathAnnotation.beanPath();
                String fieldName = dataBeanPathAnnotation.fieldName();
                String suffix = dataBeanPathAnnotation.suffix();
                Class<? extends DataWrapper> wrapperClass = dataBeanPathAnnotation.wrapperClass();
                int sortOrder = dataBeanPathAnnotation.sortOrder();
                ExcelExportEntity excelExportEntity = new ExcelExportEntity();
                excelExportEntity.setName(fieldName);
                excelExportEntity.setKey(path);
                excelExportEntity.setOrderNum(sortOrder);
                colList.add(excelExportEntity);
            } else if (field.isAnnotationPresent(DataBeanObject.class)) {
                try {
                    process(field.getName(), field.getType(), colList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
