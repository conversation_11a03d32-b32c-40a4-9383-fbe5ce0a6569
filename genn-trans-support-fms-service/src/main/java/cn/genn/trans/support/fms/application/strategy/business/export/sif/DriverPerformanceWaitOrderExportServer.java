package cn.genn.trans.support.fms.application.strategy.business.export.sif;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.sif.DriverPerformanceInfoDTO;
import cn.genn.trans.support.fms.application.strategy.ExcelExportAbstractServer;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.*;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.SystemEnum;
import cn.genn.trans.support.fms.interfaces.query.business.DriverWaitConfirmQuery;
import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 司机绩效运单明细 待确认待汇总 数据导出
 * 调用rpc client
 *
 * @Date: 2024/7/2
 * @Author: kangjian
 */
@Component("driverPerformanceWaitOrderExportServer")
@Slf4j
public class DriverPerformanceWaitOrderExportServer extends ExcelExportAbstractServer {

    @Resource
    private InternalDomainUtil internalDomainUtil;
    @Resource
    private RestTemplate restTemplate;

    private String queryDetailUrl = "/api/tms/charge/bill/wait/order/info/export";

    /**
     * 应付对账单明细
     *
     * @param taskInfo
     * @param pageNo
     * @return
     */
    @Override
    public List<Object> queryListForExcelExport(FileExportTaskDTO taskInfo, int pageNo) {
        ConfigGridDTO configGridDTO = JsonUtils.parse(taskInfo.getTaskParam(), ConfigGridDTO.class);
        DriverWaitConfirmQuery driverWaitConfirmQuery = configGridDTO.getDriverWaitConfirmQuery();
        driverWaitConfirmQuery.setPageNo(pageNo);
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getPageSize())) {
            driverWaitConfirmQuery.setPageSize(taskInfo.getExportConfConfig().getPageSize());
        }

        String url = null;
        String requsetBody = JsonUtils.toJson(driverWaitConfirmQuery);
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("magic-token", MagicTokenHandleUtil.handleMagicToken(taskInfo.getTokenData()));
        headers.add("Content-Type", "application/json");
        String tmsServerUrl = internalDomainUtil.getInternalDomain(taskInfo.getTenantId(), SystemEnum.TMS.getCode());
        url = tmsServerUrl + queryDetailUrl;
        return handleDetailView(taskInfo, pageNo, url, requsetBody, headers, driverWaitConfirmQuery);
    }


    @NotNull
    private List<Object> handleDetailView(FileExportTaskDTO taskInfo, int pageNo, String url, String requsetBody, MultiValueMap<String, String> headers, DriverWaitConfirmQuery pageQuery) {
        PageResultDTO<DriverPerformanceInfoDTO> response = fetchPostResponse(url, requsetBody, headers);

        // 计算导出是否超过1w条 超过1w条返回空 不继续，未超出1w条
        int offset = (pageNo - 1) * pageQuery.getPageSize();
        if (Objects.nonNull(taskInfo.getExportConfConfig())
            && Objects.nonNull(taskInfo.getExportConfConfig().getSum())
            && offset >= taskInfo.getExportConfConfig().getSum()) {
            log.info("已超过导出模板配置的总数：{} 不进行服务调用获取数据", taskInfo.getExportConfConfig().getSum());
            return Collections.emptyList();
        }

        List<Object> resultList = new ArrayList<>();
        for (DriverPerformanceInfoDTO dto : response.getList()) {
            Map<String, Object> map = new HashMap<>();
            process(dto, "", dto, map);
            // 后处理 feeConfig 外协司机运价增加 (元/吨)
            if (map.containsKey("feeConfig") && StrUtil.isNotBlank(map.get("feeConfig").toString())) {
                map.put("feeConfig", map.get("feeConfig") + "元/" + dto.getCarrierChargeUnit().getDescription());
            }
            resultList.add(map);
        }
        return resultList;
    }


    @SneakyThrows
    public static void process(Object parentObj, String parent, Object obj, Map<String, Object> map) {
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(DataBeanPath.class)) {
                DataBeanPath dataBeanPathAnnotation = field.getAnnotation(DataBeanPath.class);
                String path = StrUtil.isNotBlank(parent) ? parent + "." + dataBeanPathAnnotation.beanPath() : dataBeanPathAnnotation.beanPath();
                String fieldName = dataBeanPathAnnotation.fieldName();
                String suffix = dataBeanPathAnnotation.suffix();
                Class<? extends DataWrapper> wrapperClass = dataBeanPathAnnotation.wrapperClass();
                int sortOrder = dataBeanPathAnnotation.sortOrder();
                BeanPath resolver = new BeanPath(path);
                Object value = resolver.get(parentObj);
                if (!wrapperClass.equals(DataWrapper.class)) {
                    try {
                        DataWrapper wrapper = wrapperClass.getDeclaredConstructor().newInstance();
                        value = wrapper.wrap(value);
                    } catch (Exception e) {
                        log.error("DataWrapper实例化失败", e);
                    }
                }
                map.put(path, Optional.ofNullable(value).orElse(""));
            } else if (field.isAnnotationPresent(DataBeanObject.class)) {
                try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        process(parentObj, field.getName(), fieldValue, map);

                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 应付对账单明细页数据
     *
     * @param url
     * @param requestBody
     * @param headers
     * @return
     */
    public PageResultDTO<DriverPerformanceInfoDTO> fetchPostResponse(String url, String requestBody, MultiValueMap<String, String> headers) {
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        try {
            log.info("远程调用, url: {}, body: {}", url, requestBody);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            log.info("远程调用, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            } else {
                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<PageResultDTO<DriverPerformanceInfoDTO>> responseResult = JsonUtils.parse(responseEntity.getBody(), new TypeReference< ResponseResult<PageResultDTO<DriverPerformanceInfoDTO>>>() {
                });
                if (Objects.isNull(responseResult)) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }
                if (!responseResult.isSuccess()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR, responseResult.getMsg());
                }
                return responseResult.getData();
            }
        } catch (RuntimeException e) {
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("远程调用, error", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }

        }
    }
}
