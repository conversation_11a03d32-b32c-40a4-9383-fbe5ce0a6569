package cn.genn.trans.support.fms.application.strategy.business.importExcel;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.assembler.FileImportTaskAssembler;
import cn.genn.trans.support.fms.application.extension.RemoteExtension;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.application.strategy.CommonAbstractImportStrategy;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.repository.mapper.FileImportTaskMapper;
import cn.genn.trans.support.fms.infrastructure.utils.ExcelHandleUtil;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileImportTaskDTO;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.FileImportConfConfig;
import cn.genn.trans.support.fms.interfaces.dto.config.FileImportResultInfo;
import cn.genn.trans.support.fms.interfaces.enums.ImportTaskBusinessCodeEnum;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.*;

/**
 * 全局通用业务通用数据导入
 *
 * @Date: 2024/12/9
 * @Author: kangjian
 */
@Component("commonExcelImportExportStrategy")
@Slf4j
public class CommonExcelImportExportStrategy extends CommonAbstractImportStrategy {

    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;
    @Resource
    private RemoteExtension remoteExtension;
    @Resource
    private FileImportTaskMapper fileImportTaskMapper;
    @Resource
    private FileImportTaskAssembler fileImportTaskAssembler;

    @Override
    public Workbook executeTask(FileImportTaskDTO fileImportTaskDTO) {
        /**
         * 读取导入的excel文件流
         * 解析excel的第一行
         * 和fileImportTaskDTO中的templateConf的映射关系对应
         * 将excel中的数据解析成对应的数据
         * 将taskParam的数据放入传给业务的数据中一并带回去，并且带上任务唯一id和行号
         * 根据templateConf的分页参数进行批量处理
         * 每批掉一次业务接口 业务接口从templateConf中获取
         * 业务接口返回的数据解析返回的行号 并把每行的处理结果赋值到excel后面追加的两列上
         * 完成导出 将Workbook对象上传进行文件流处理
         */
        FileImportConfConfig fileImportConfConfig = fileImportTaskDTO.getImportConfConfig();
        FileDetailQuerySingleCommand fileDetailQuerySingleCommand = new FileDetailQuerySingleCommand();
        fileDetailQuerySingleCommand.setFileKey(fileImportTaskDTO.getImportFileKey());
        FileInfoDTO importfileInfoDTO = fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(fileDetailQuerySingleCommand);
        if (importfileInfoDTO == null) {
            throw new BusinessException(MessageCode.EXPORT_TEMPLATE_NOT_EXIST);
        }
        // 替换前的文件
        File saveFile = FileUploadSupportActionService.downloadFile(importfileInfoDTO.getUrl(), importfileInfoDTO.getExt(), this.octTempFilePath);
        try {
            // 校验文件的第一行表头是否满足导入的全部列
            boolean checked = ExcelHandleUtil.validateFirstRow(saveFile, fileImportConfConfig.getImportColumnNames());
            if (!checked) {
                throw new BusinessException(MessageCode.IMPORT_EXCEL_HEAD_NOT_MATCH);
            }
            Workbook workbook = WorkbookFactory.create(saveFile);
            // 创建导入参数对象
            ImportParams params = new ImportParams();
            params.setTitleRows(0);
            params.setHeadRows(1);
            params.setStartRows(0);

            // 通过工具类直接导入，不需要@Excel注解
            List<Map<String, Object>> excelDataList = ExcelImportUtil.importExcel(saveFile, Map.class, params);
            // TODO 考虑是否需要增加校验空行的情况
            // 最后一列增加导入结果和失败原因的标题
            Sheet sheet = addTitle(workbook);
            FileImportResultInfo importResultInfo = new FileImportResultInfo();
            // 将每行数据转成带行号的Map,批量调用业务系统
            List<Map<String, Object>> excelHandledData = handleBatchData(fileImportConfConfig, excelDataList);
            if (Objects.nonNull(fileImportConfConfig.getSum()) && excelHandledData.size() > fileImportConfConfig.getSum()) {
                throw new BusinessException(MessageCode.IMPORT_EXCEL_TOO_MUCH_DATA);
            }
            importResultInfo.setSuccess(0);
            importResultInfo.setFail(0);
            // 按pageSiz进行分割batchData
            List<List<Map<String, Object>>> pageDataList = ListUtil.split(excelHandledData, fileImportConfConfig.getPageSize());
            for (List<Map<String, Object>> pageData : pageDataList) {
                // 批量调用业务系统API
                List<Map<String, Object>> results = callBusinessSystemBatch(fileImportTaskDTO, fileImportTaskDTO.getTaskParam(), pageData);
                // 将返回的每行数据调用的结果更新到excel中
                handleExcelResult(importResultInfo, sheet, results, params);
            }
            importResultInfo.setCount(importResultInfo.getSuccess() + importResultInfo.getFail());
            // 更新导入的执行结果
            fileImportTaskDTO.setResultInfo(JsonUtils.toJson(importResultInfo));
            updateImportResult(fileImportTaskDTO);
            /*// TODO 保存Excel文件 测试时先输出到本地看看
            InputStream fileInputStream = ExcelHandleUtil.toInputStream(workbook);
            // 写入到文件中
            try {
                FileUtils.copyInputStreamToFile(fileInputStream, new File("/Users/<USER>/Downloads/测试结果.xlsx"));
            } catch (Exception e) {
                e.printStackTrace();
            }*/
            return workbook;

        } catch (Exception e) {
            log.error("handle template error: " + e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        } finally {
            if (saveFile != null) {
                // 删除临时文件
                FileUtils.deleteQuietly(saveFile);
            }
        }

    }

    private void updateImportResult(FileImportTaskDTO fileImportTaskDTO) {
        fileImportTaskMapper.updateById(fileImportTaskAssembler.toPO(fileImportTaskDTO));
    }

    @NotNull
    private static Sheet addTitle(Workbook workbook) {
        // 在最后一列添加"导入结果"标题
        Sheet sheet = workbook.getSheetAt(0);
        Row titleRow = sheet.getRow(0);
        Cell importResult = titleRow.createCell(titleRow.getLastCellNum());
        importResult.setCellValue("导入结果");
        Cell message = titleRow.createCell(titleRow.getLastCellNum());
        message.setCellValue("失败原因");
        return sheet;
    }

    /**
     * 调用业务接口的处理结果更新到excel的最后两列中
     *
     * @param importResultInfo
     * @param sheet
     * @param results
     * @param params
     */
    private void handleExcelResult(FileImportResultInfo importResultInfo, Sheet sheet, List<Map<String, Object>> results, ImportParams params) {
        // 创建一个映射来存储非空行的实际索引
        Map<Integer, Integer> nonEmptyRowMap = new HashMap<>();
        int adjustedRowIndex = 0;

        // 遍历 Excel 行，构建非空行映射
        for (int rowIndex = 0; rowIndex < sheet.getLastRowNum() + 1; rowIndex++) {
            Row row = sheet.getRow(rowIndex + params.getStartRows());
            if (row == null || ExcelHandleUtil.isRowEmpty(row)) {
                continue; // 跳过空行
            }
            nonEmptyRowMap.put(adjustedRowIndex, rowIndex);
            adjustedRowIndex++;
        }

        // 处理导入结果
        for (Map<String, Object> result : results) {
            Object rowIndexObj = result.get("rowIndex");
            if (rowIndexObj == null) {
                continue; // 或者记录日志并跳过
            }
            int rowIndex = (int) rowIndexObj;

            // 获取实际的非空行索引
            Integer actualRowIndex = nonEmptyRowMap.get(rowIndex);
            if (actualRowIndex == null) {
                continue; // 或者记录日志并跳过
            }

            Row row = sheet.getRow(actualRowIndex + params.getStartRows());
            if (row == null) {
                continue; // 或者记录日志并跳过
            }

            // 创建导入结果列
            Cell resultCell = row.createCell(sheet.getRow(0).getLastCellNum() - 2);
            Object resultObj = result.get("result");
            boolean isSuccess = resultObj != null && (boolean) resultObj;
            resultCell.setCellValue(isSuccess ? "成功" : "失败");

            if (isSuccess) {
                importResultInfo.addSuccess();
            } else {
                importResultInfo.addFail();
            }

            // 创建失败原因列
            Cell messageCell = row.createCell(sheet.getRow(0).getLastCellNum() - 1);
            Object messageObj = result.get("message");
            String message = messageObj != null ? (String) messageObj : "";
            messageCell.setCellValue(message);
        }
    }



    /**
     * excel中的所有数据
     *
     * @param fileImportConfConfig
     * @param list
     * @return
     */
    private List<Map<String, Object>> handleBatchData(FileImportConfConfig fileImportConfConfig, List<Map<String, Object>> list) {
        // 获取excel的表格列配置 然后将读到的数据相应表格列列名转为对应的字段名
        int rowIndex = 1;
        Map<String, String> columnFieldMap = fileImportConfConfig.getColumnFieldMap();
        List<Map<String, Object>> batchData = new ArrayList<>();
        // 将excel中的数据转换成对应的字段名
        for (Map<String, Object> row : list) {
            Map<String, Object> data = new HashMap<>();
            // 增加行号
            data.put("rowIndex", rowIndex);
            for (Map.Entry<String, String> entry : columnFieldMap.entrySet()) {
                String columnName = entry.getKey();
                String fieldName = entry.getValue();
                data.put(fieldName, row.get(columnName));
            }
            batchData.add(data);
            rowIndex++;
        }
        return batchData;
    }


    private List<Map<String, Object>> callBusinessSystemBatch(FileImportTaskDTO taskInfo, String taskParam, List<Map<String, Object>> batchData) {
        // 调用业务系统API，将数据批量写入业务系统
        // 处理任务参数  构造类似这样的一个json对象 {"taskParam": {a:1,b:2}, "data": [{rowIndex:1,a:1,b:2},{rowIndex:2,a:1,b:2}]}
        Map<String, Object> requestParam = new HashMap<>();
        if (StrUtil.isNotBlank(taskParam) && !taskParam.equals("{}")) {
            // 将taskParam字符串转换为Map对象
            Map<String, Object> taskParamMap = JsonUtils.parse(taskParam, Map.class);
            // 构造请求参数
            requestParam.put("taskParam", taskParamMap);
        }
        requestParam.put("data", batchData);
        String handledRequestParam = JsonUtils.toJson(requestParam);
        log.info("调用业务系统api接口 请求参数:{}", handledRequestParam);
        // 生成业务系统api接口的请求路径

        // callbackParam 解析为JsonObject 然后 加入值 pageNo pageSize
        TypeToken<List<Map<String, Object>>> mapTypeToken = new TypeToken<List<Map<String, Object>>>() {
        };
        FileImportConfConfig fileImportConfConfig = taskInfo.getImportConfConfig();
        // 出参对象 {"success": true, "data": [{rowIndex:1,result:true,message:"失败原因"},{rowIndex:2,result:true,message:"失败原因"]}
        // 调用业务系统api接口
        ResponseResult<List<Map<String, Object>>> response = remoteExtension.fetchPostResponse(fileImportConfConfig.getRestTemplateStrategy(),
            remoteExtension.generateQueryUrl(taskInfo), handledRequestParam, taskInfo.getTokenData(), mapTypeToken);

        if (Objects.isNull(response) || Objects.isNull((response.getData()))) {
            return Collections.emptyList();
        }
        // 解析结果
        log.info("调用业务系统api接口 返回结果:{}", JsonUtils.toJson(response.getData()));
        return response.getData();
       /* // 构造模拟返回结果
        List<Map<String, String>> resultList = new ArrayList<>();

        for (Map<String, Object> data : batchData) {
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("rowIndex", String.valueOf(data.get("rowIndex")));
            resultMap.put("result", "true");
            resultMap.put("message", "导入成功" + RandomUtil.randomNumber());
            resultList.add(resultMap);
        }

        return resultList;*/
    }


    @Override
    public ImportTaskBusinessCodeEnum getBusinessCode() {
        return ImportTaskBusinessCodeEnum.COMMON_IMPORT;
    }

}
