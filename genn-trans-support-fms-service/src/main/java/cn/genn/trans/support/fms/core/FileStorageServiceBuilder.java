package cn.genn.trans.support.fms.core;

import cn.genn.trans.support.fms.core.util.Tools;
import cn.hutool.core.collection.CollUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.obs.services.ObsClient;
import com.volcengine.tos.TOSV2;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import cn.genn.trans.support.fms.core.FileStorageProperties.*;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.exception.FileStorageRuntimeException;
import cn.genn.trans.support.fms.core.file.*;
import cn.genn.trans.support.fms.core.platform.*;
import cn.genn.trans.support.fms.core.recorder.DefaultFileRecorder;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.tika.ContentTypeDetect;
import cn.genn.trans.support.fms.core.tika.DefaultTikaFactory;
import cn.genn.trans.support.fms.core.tika.TikaContentTypeDetect;
import cn.genn.trans.support.fms.core.tika.TikaFactory;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@Accessors(chain = true)
public class FileStorageServiceBuilder {
    /**
     * 配置参数
     */
    private FileStorageProperties properties;
    /**
     * 文件记录记录者
     */
    private FileRecorder fileRecorder;
    /**
     * Tika 工厂类
     */
    private TikaFactory tikaFactory;
    /**
     * 识别文件的 MIME 类型
     */
    private ContentTypeDetect contentTypeDetect;
    /**
     * 切面
     */
    private List<FileStorageAspect> aspectList = new ArrayList<>();
    /**
     * 文件包装适配器
     */
    private List<FileWrapperAdapter> fileWrapperAdapterList = new ArrayList<>();
    /**
     * Client 工厂
     */
    private List<List<FileStorageClientFactory<?>>> clientFactoryList = new ArrayList<>();
    /**
     * 存储平台
     */
    private List<FileStorage> fileStorageList = new ArrayList<>();

    public FileStorageServiceBuilder(FileStorageProperties properties) {
        this.properties = properties;
    }

    /**
     * 设置默认的文件记录者
     */
    public FileStorageServiceBuilder setDefaultFileRecorder() {
        fileRecorder = new DefaultFileRecorder();
        return this;
    }

    /**
     * 设置默认的 Tika 工厂类
     */
    public FileStorageServiceBuilder setDefaultTikaFactory() {
        tikaFactory = new DefaultTikaFactory();
        return this;
    }

    /**
     * 设置基于 Tika 识别文件的 MIME 类型
     */
    public FileStorageServiceBuilder setTikaContentTypeDetect() {
        if (tikaFactory == null) throw new FileStorageRuntimeException("请先设置 TikaFactory");
        contentTypeDetect = new TikaContentTypeDetect(tikaFactory);
        return this;
    }

    /**
     * 添加切面
     */
    public FileStorageServiceBuilder addAspect(FileStorageAspect aspect) {
        aspectList.add(aspect);
        return this;
    }

    /**
     * 添加文件包装适配器
     */
    public FileStorageServiceBuilder addFileWrapperAdapter(FileWrapperAdapter adapter) {
        fileWrapperAdapterList.add(adapter);
        return this;
    }

    /**
     * 添加 byte[] 文件包装适配器
     */
    public FileStorageServiceBuilder addByteFileWrapperAdapter() {
        if (contentTypeDetect == null) throw new FileStorageRuntimeException("请先设置 TikaFactory 和 ContentTypeDetect");
        fileWrapperAdapterList.add(new ByteFileWrapperAdapter(contentTypeDetect));
        return this;
    }

    /**
     * 添加 InputStream 文件包装适配器
     */
    public FileStorageServiceBuilder addInputStreamFileWrapperAdapter() {
        if (contentTypeDetect == null) throw new FileStorageRuntimeException("请先设置 TikaFactory 和 ContentTypeDetect");
        fileWrapperAdapterList.add(new InputStreamFileWrapperAdapter(contentTypeDetect));
        return this;
    }

    /**
     * 添加本地文件包装适配器
     */
    public FileStorageServiceBuilder addLocalFileWrapperAdapter() {
        if (contentTypeDetect == null) throw new FileStorageRuntimeException("请先设置 TikaFactory 和 ContentTypeDetect");
        fileWrapperAdapterList.add(new LocalFileWrapperAdapter(contentTypeDetect));
        return this;
    }

    /**
     * 添加 URI 文件包装适配器
     */
    public FileStorageServiceBuilder addUriFileWrapperAdapter() {
        if (contentTypeDetect == null) throw new FileStorageRuntimeException("请先设置 TikaFactory 和 ContentTypeDetect");
        fileWrapperAdapterList.add(new UriFileWrapperAdapter(contentTypeDetect));
        return this;
    }

    /**
     * 添加 HttpServletRequest 文件包装适配器
     */
    public FileStorageServiceBuilder addHttpServletRequestFileWrapperAdapter() {
        if (!doesNotExistClass("javax.servlet.http.HttpServletRequest")) {
            fileWrapperAdapterList.add(new JavaxHttpServletRequestFileWrapperAdapter());
        }
        if (!doesNotExistClass("jakarta.servlet.http.HttpServletRequest")) {
            fileWrapperAdapterList.add(new JakartaHttpServletRequestFileWrapperAdapter());
        }
        return this;
    }

    /**
     * 添加全部的文件包装适配器
     */
    public FileStorageServiceBuilder addAllFileWrapperAdapter() {
        addByteFileWrapperAdapter();
        addInputStreamFileWrapperAdapter();
        addLocalFileWrapperAdapter();
        addUriFileWrapperAdapter();
        addHttpServletRequestFileWrapperAdapter();
        return this;
    }

    /**
     * 添加 Client 工厂
     */
    public FileStorageServiceBuilder addFileStorageClientFactory(List<FileStorageClientFactory<?>> list) {
        clientFactoryList.add(list);
        return this;
    }

    /**
     * 添加 Client 工厂
     */
    public FileStorageServiceBuilder addFileStorageClientFactory(FileStorageClientFactory<?> factory) {
        clientFactoryList.add(Collections.singletonList(factory));
        return this;
    }

    /**
     * 添加存储平台
     */
    public FileStorageServiceBuilder addFileStorage(List<? extends FileStorage> storageList) {
        fileStorageList.addAll(storageList);
        return this;
    }

    /**
     * 添加存储平台
     */
    public FileStorageServiceBuilder addFileStorage(FileStorage storage) {
        fileStorageList.add(storage);
        return this;
    }

    /**
     * 使用默认配置
     */
    public FileStorageServiceBuilder useDefault() {
        setDefaultFileRecorder();
        setDefaultTikaFactory();
        setTikaContentTypeDetect();
        addAllFileWrapperAdapter();
        return this;
    }

    /**
     * 创建
     */
    public FileStorageService build() {
        if (properties == null) throw new FileStorageRuntimeException("properties 不能为 null");

        // 初始化各个存储平台
        fileStorageList.addAll(buildHuaweiObsFileStorage(properties.getHuaweiObs(), clientFactoryList));

        // 本体
        FileStorageService service = new FileStorageService();
        service.setSelf(service);
        service.setProperties(properties);
        service.setFileStorageList(new CopyOnWriteArrayList<>(fileStorageList));
        service.setFileRecorder(fileRecorder);
        service.setAspectList(new CopyOnWriteArrayList<>(aspectList));
        service.setFileWrapperAdapterList(new CopyOnWriteArrayList<>(fileWrapperAdapterList));
        service.setContentTypeDetect(contentTypeDetect);

        return service;
    }

    /**
     * 创建一个 FileStorageService 的构造器
     */
    public static FileStorageServiceBuilder create(FileStorageProperties properties) {
        return new FileStorageServiceBuilder(properties);
    }


    /**
     * 根据配置文件创建华为云 OBS 存储平台
     */
    public static List<HuaweiObsFileStorage> buildHuaweiObsFileStorage(
            List<? extends HuaweiObsConfig> list, List<List<FileStorageClientFactory<?>>> clientFactoryList) {
        if (CollUtil.isEmpty(list)) return Collections.emptyList();
        buildFileStorageDetect(list, "华为云 OBS", "com.obs.services.ObsClient");
        return list.stream()
                .map(config -> {
                    log.info("加载华为云 OBS 存储平台：{}", config.getPlatform());
                    FileStorageClientFactory<ObsClient> clientFactory = getFactory(
                            config.getPlatform(),
                            clientFactoryList,
                            () -> new HuaweiObsFileStorageClientFactory(config));
                    return new HuaweiObsFileStorage(config, clientFactory);
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据配置文件创建又 Amazon S3 存储平台
     */
    public static List<AmazonS3FileStorage> buildAmazonS3FileStorage(
        List<? extends AmazonS3Config> list, List<List<FileStorageClientFactory<?>>> clientFactoryList) {
        if (CollUtil.isEmpty(list)) return Collections.emptyList();
        buildFileStorageDetect(list, "Amazon S3", "com.amazonaws.services.s3.AmazonS3");
        return list.stream()
            .map(config -> {
                log.info("加载 Amazon S3 存储平台：{}", config.getPlatform());
                FileStorageClientFactory<AmazonS3> clientFactory = getFactory(
                    config.getPlatform(),
                    clientFactoryList,
                    () -> new AmazonS3FileStorageClientFactory(config));
                return new AmazonS3FileStorage(config, clientFactory);
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据配置文件创建又 TOS 火山云 存储平台
     */
    public static List<TosFileStorage> buildTosFileStorage(
        List<? extends TosConfig> list, List<List<FileStorageClientFactory<?>>> clientFactoryList) {
        if (CollUtil.isEmpty(list)) return Collections.emptyList();
        buildFileStorageDetect(list, "Tos", "com.volcengine.tos.TOSV2");
        return list.stream()
            .map(config -> {
                log.info("加载 TOS火山云 存储平台：{}", config.getPlatform());
                FileStorageClientFactory<TOSV2> clientFactory = getFactory(
                    config.getPlatform(),
                    clientFactoryList,
                    () -> new TosFileStorageClientFactory(config));
                return new TosFileStorage(config, clientFactory);
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取或创建指定存储平台的 Client 工厂对象
     */
    public static <Client> FileStorageClientFactory<Client> getFactory(
            String platform,
            List<List<FileStorageClientFactory<?>>> list,
            Supplier<FileStorageClientFactory<Client>> defaultSupplier) {
        if (list != null) {
            for (List<FileStorageClientFactory<?>> factoryList : list) {
                for (FileStorageClientFactory<?> factory : factoryList) {
                    if (Objects.equals(platform, factory.getPlatform())) {
                        try {
                            return Tools.cast(factory);
                        } catch (Exception e) {
                            throw new FileStorageRuntimeException(
                                    "获取 FileStorageClientFactory 失败，类型不匹配，platform：" + platform, e);
                        }
                    }
                }
            }
        }
        return defaultSupplier.get();
    }

    /**
     * 判断是否没有引入指定 Class
     */
    public static boolean doesNotExistClass(String name) {
        try {
            Class.forName(name);
            return false;
        } catch (ClassNotFoundException e) {
            return true;
        }
    }

    /**
     * 创建存储平台时的依赖检查
     */
    public static void buildFileStorageDetect(List<?> list, String platformName, String... classNames) {
        if (CollUtil.isEmpty(list)) return;
        for (String className : classNames) {
            if (doesNotExistClass(className)) {
                throw new FileStorageRuntimeException(
                        "检测到【" + platformName + "】配置，但是没有找到对应的依赖类：【" + className
                                + "】，所以无法加载此存储平台！配置参考地址：https://x-file-storage.xuyanwu.cn/2.1.0/#/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%97%A8");
            }
        }
    }
}
