package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.AbortMultipartUploadPretreatment;

/**
 * 手动分片上传-取消切面调用链结束回调
 */
public interface AbortMultipartUploadAspectChainCallback {
    FileInfo run(AbortMultipartUploadPretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder);
}
