package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.aspect.CompleteMultipartUploadAspectChainCallback;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.tika.ContentTypeDetect;
import cn.genn.trans.support.fms.core.upload.CompleteMultipartUploadPretreatment;

import java.util.Iterator;

/**
 * 手动分片上传-完成的切面调用链
 */
@Getter
@Setter
public class CompleteMultipartUploadAspectChain {

    private cn.genn.trans.support.fms.core.aspect.CompleteMultipartUploadAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public CompleteMultipartUploadAspectChain(
            Iterable<FileStorageAspect> aspects, CompleteMultipartUploadAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public FileInfo next(
            CompleteMultipartUploadPretreatment pre,
            FileStorage fileStorage,
            FileRecorder fileRecorder,
            ContentTypeDetect contentTypeDetect) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator
                    .next()
                    .completeMultipartUploadAround(this, pre, fileStorage, fileRecorder, contentTypeDetect);
        } else {
            return callback.run(pre, fileStorage, fileRecorder, contentTypeDetect);
        }
    }
}
