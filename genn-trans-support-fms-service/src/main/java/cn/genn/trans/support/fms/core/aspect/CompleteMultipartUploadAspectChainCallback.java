package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.tika.ContentTypeDetect;
import cn.genn.trans.support.fms.core.upload.CompleteMultipartUploadPretreatment;

/**
 * 手动分片上传-完成切面调用链结束回调
 */
public interface CompleteMultipartUploadAspectChainCallback {
    FileInfo run(
            CompleteMultipartUploadPretreatment pre,
            FileStorage fileStorage,
            FileRecorder fileRecorder,
            ContentTypeDetect contentTypeDetect);
}
