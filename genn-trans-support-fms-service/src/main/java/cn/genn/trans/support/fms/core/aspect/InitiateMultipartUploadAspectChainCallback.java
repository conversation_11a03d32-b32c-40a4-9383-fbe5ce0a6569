package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.InitiateMultipartUploadPretreatment;

/**
 * 手动分片上传-初始化切面调用链结束回调
 */
public interface InitiateMultipartUploadAspectChainCallback {
    FileInfo run(
            FileInfo fileInfo,
            InitiateMultipartUploadPretreatment pre,
            FileStorage fileStorage,
            FileRecorder fileRecorder);
}
