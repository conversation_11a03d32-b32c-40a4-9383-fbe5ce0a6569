package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.IsSupportMultipartUploadChainCallback;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.platform.MultipartUploadSupportInfo;

import java.util.Iterator;

/**
 * 是否支持手动分片上传的切面调用链
 */
@Getter
@Setter
public class IsSupportMultipartUploadAspectChain {

    private IsSupportMultipartUploadChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public IsSupportMultipartUploadAspectChain(
            Iterable<FileStorageAspect> aspects, IsSupportMultipartUploadChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public MultipartUploadSupportInfo next(FileStorage fileStorage) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator.next().isSupportMultipartUpload(this, fileStorage);
        } else {
            return callback.run(fileStorage);
        }
    }
}
