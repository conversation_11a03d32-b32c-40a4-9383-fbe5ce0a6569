package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.ListPartsAspectChainCallback;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.upload.FilePartInfoList;
import cn.genn.trans.support.fms.core.upload.ListPartsPretreatment;

import java.util.Iterator;

/**
 * 手动分片上传-列举已上传的分片的切面调用链
 */
@Getter
@Setter
public class ListPartsAspectChain {

    private ListPartsAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public ListPartsAspectChain(Iterable<FileStorageAspect> aspects, ListPartsAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public FilePartInfoList next(ListPartsPretreatment pre, FileStorage fileStorage) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator.next().listParts(this, pre, fileStorage);
        } else {
            return callback.run(pre, fileStorage);
        }
    }
}
