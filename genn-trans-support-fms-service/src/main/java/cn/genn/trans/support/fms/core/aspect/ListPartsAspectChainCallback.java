package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.upload.FilePartInfoList;
import cn.genn.trans.support.fms.core.upload.ListPartsPretreatment;

/**
 * 手动分片上传-列举已上传的分片切面调用链结束回调
 */
public interface ListPartsAspectChainCallback {
    FilePartInfoList run(ListPartsPretreatment pre, FileStorage fileStorage);
}
