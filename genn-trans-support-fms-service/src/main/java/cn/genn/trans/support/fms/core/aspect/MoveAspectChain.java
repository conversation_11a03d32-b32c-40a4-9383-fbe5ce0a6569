package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.MoveAspectChainCallback;
import cn.genn.trans.support.fms.core.move.MovePretreatment;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;

import java.util.Iterator;

/**
 * 移动的切面调用链
 */
@Getter
@Setter
public class MoveAspectChain {

    private cn.genn.trans.support.fms.core.aspect.MoveAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public MoveAspectChain(Iterable<FileStorageAspect> aspects, MoveAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public FileInfo next(
            FileInfo srcFileInfo, MovePretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator.next().moveAround(this, srcFileInfo, pre, fileStorage, fileRecorder);
        } else {
            return callback.run(srcFileInfo, pre, fileStorage, fileRecorder);
        }
    }
}
