package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.SameCopyAspectChainCallback;
import cn.genn.trans.support.fms.core.copy.CopyPretreatment;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;

import java.util.Iterator;

/**
 * 同存储平台复制的切面调用链
 */
@Getter
@Setter
public class SameCopyAspectChain {

    private cn.genn.trans.support.fms.core.aspect.SameCopyAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public SameCopyAspectChain(Iterable<FileStorageAspect> aspects, SameCopyAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public FileInfo next(
            FileInfo srcFileInfo,
            FileInfo destFileInfo,
            CopyPretreatment pre,
            FileStorage fileStorage,
            FileRecorder fileRecorder) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator
                    .next()
                    .sameCopyAround(this, srcFileInfo, destFileInfo, pre, fileStorage, fileRecorder);
        } else {
            return callback.run(srcFileInfo, destFileInfo, pre, fileStorage, fileRecorder);
        }
    }
}
