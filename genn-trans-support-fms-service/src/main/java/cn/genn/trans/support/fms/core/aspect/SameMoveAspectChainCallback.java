package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.move.MovePretreatment;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;

/**
 * 同存储平台移动切面调用链结束回调
 */
public interface SameMoveAspectChainCallback {
    FileInfo run(
            FileInfo srcFileInfo,
            FileInfo destFileInfo,
            MovePretreatment pre,
            FileStorage fileStorage,
            FileRecorder fileRecorder);
}
