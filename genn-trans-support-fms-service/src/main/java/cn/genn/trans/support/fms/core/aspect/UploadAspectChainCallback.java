package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.UploadPretreatment;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;

/**
 * 上传切面调用链结束回调
 */
public interface UploadAspectChainCallback {
    FileInfo run(FileInfo fileInfo, UploadPretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder);
}
