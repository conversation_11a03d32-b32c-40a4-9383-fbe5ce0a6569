package cn.genn.trans.support.fms.core.aspect;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.UploadPartAspectChainCallback;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.FilePartInfo;
import cn.genn.trans.support.fms.core.upload.UploadPartPretreatment;

import java.util.Iterator;

/**
 * 手动分片上传-上传分片的切面调用链
 */
@Getter
@Setter
public class UploadPartAspectChain {

    private cn.genn.trans.support.fms.core.aspect.UploadPartAspectChainCallback callback;
    private Iterator<FileStorageAspect> aspectIterator;

    public UploadPartAspectChain(Iterable<FileStorageAspect> aspects, UploadPartAspectChainCallback callback) {
        this.aspectIterator = aspects.iterator();
        this.callback = callback;
    }

    /**
     * 调用下一个切面
     */
    public FilePartInfo next(UploadPartPretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder) {
        if (aspectIterator.hasNext()) { // 还有下一个
            return aspectIterator.next().uploadPart(this, pre, fileStorage, fileRecorder);
        } else {
            return callback.run(pre, fileStorage, fileRecorder);
        }
    }
}
