package cn.genn.trans.support.fms.core.aspect;

import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.FilePartInfo;
import cn.genn.trans.support.fms.core.upload.UploadPartPretreatment;

/**
 * 手动分片上传-上传分片切面调用链结束回调
 */
public interface UploadPartAspectChainCallback {
    FilePartInfo run(UploadPartPretreatment pre, FileStorage fileStorage, FileRecorder fileRecorder);
}
