package cn.genn.trans.support.fms.core.file;

import cn.genn.trans.support.fms.core.file.FileWrapper;

import java.io.IOException;

/**
 * 文件包装适配器接口
 */
public interface FileWrapperAdapter {

    /**
     * 是否支持
     */
    boolean isSupport(Object source);

    /**
     * 获取文件包装
     */
    cn.genn.trans.support.fms.core.file.FileWrapper getFileWrapper(Object source, String name, String contentType, Long size) throws IOException;

    /**
     * 更新文件包装参数
     */
    default cn.genn.trans.support.fms.core.file.FileWrapper updateFileWrapper(FileWrapper fileWrapper, String name, String contentType, Long size) {
        if (name != null) fileWrapper.setName(name);
        if (contentType != null) fileWrapper.setContentType(contentType);
        if (size != null) fileWrapper.setSize(size);
        return fileWrapper;
    }
}
