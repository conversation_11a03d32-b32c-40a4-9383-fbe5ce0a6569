package cn.genn.trans.support.fms.core.file;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import cn.genn.trans.support.fms.core.file.FileWrapper;
import cn.genn.trans.support.fms.core.file.FileWrapperAdapter;
import cn.genn.trans.support.fms.core.file.LocalFileWrapper;
import cn.genn.trans.support.fms.core.tika.ContentTypeDetect;

import java.io.File;
import java.io.IOException;

/**
 * 本地文件包装适配器
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LocalFileWrapperAdapter implements FileWrapperAdapter {
    private ContentTypeDetect contentTypeDetect;

    @Override
    public boolean isSupport(Object source) {
        return source instanceof File || source instanceof cn.genn.trans.support.fms.core.file.LocalFileWrapper;
    }

    @Override
    public FileWrapper getFileWrapper(Object source, String name, String contentType, Long size) throws IOException {
        if (source instanceof cn.genn.trans.support.fms.core.file.LocalFileWrapper) {
            return updateFileWrapper((cn.genn.trans.support.fms.core.file.LocalFileWrapper) source, name, contentType, size);
        } else {
            File file = (File) source;
            if (name == null) name = file.getName();
            if (contentType == null) contentType = contentTypeDetect.detect(file);
            if (size == null) size = file.length();
            return new LocalFileWrapper(file, name, contentType, size);
        }
    }
}
