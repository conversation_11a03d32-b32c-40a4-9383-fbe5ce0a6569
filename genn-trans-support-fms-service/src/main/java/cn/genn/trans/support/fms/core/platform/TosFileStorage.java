package cn.genn.trans.support.fms.core.platform;

import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.ProgressListener;
import cn.genn.trans.support.fms.core.UploadPretreatment;
import cn.genn.trans.support.fms.core.*;
import cn.genn.trans.support.fms.core.copy.CopyPretreatment;
import cn.genn.trans.support.fms.core.exception.Check;
import cn.genn.trans.support.fms.core.exception.ExceptionFactory;
import cn.genn.trans.support.fms.core.file.FileWrapper;
import cn.genn.trans.support.fms.core.upload.*;
import cn.genn.trans.support.fms.core.util.Tools;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.RequestClientOptions;
import com.amazonaws.event.ProgressEventType;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.obs.services.ObsClient;
import com.obs.services.model.PartEtag;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2Client;
import com.volcengine.tos.comm.HttpMethod;
import com.volcengine.tos.comm.TosHeader;
import com.volcengine.tos.model.acl.PutObjectAclInput;
import com.volcengine.tos.model.object.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * TOS火山云 存储
 */
@Getter
@Setter
@NoArgsConstructor
public class TosFileStorage implements FileStorage {
    private String platform;
    private String bucketName;
    private String domain;
    private String basePath;
    private String defaultAcl;
    private int multipartThreshold;
    private int multipartPartSize;
    private FileStorageClientFactory<TOSV2> clientFactory;

    public TosFileStorage(
        FileStorageProperties.TosConfig config, FileStorageClientFactory<TOSV2> clientFactory) {
        platform = config.getPlatform();
        bucketName = config.getBucketName();
        domain = config.getDomain();
        basePath = config.getBasePath();
        defaultAcl = config.getDefaultAcl();
        multipartThreshold = config.getMultipartThreshold();
        multipartPartSize = config.getMultipartPartSize();
        this.clientFactory = clientFactory;
    }

    public TOSV2 getClient() {
        return clientFactory.getClient();
    }

    @Override
    public void close() {
        clientFactory.close();
    }

    @Override
    public boolean save(FileInfo fileInfo, UploadPretreatment pre) {
        fileInfo.setBasePath(basePath);
        String newFileKey = getFileKey(fileInfo);
        fileInfo.setUrl(domain + newFileKey);
        ProgressListener listener = pre.getProgressListener();
        TOSV2 client = getClient();
        //        boolean useMultipartUpload = fileInfo.getSize() == null || fileInfo.getSize() >= multipartThreshold;
        String uploadId = null;
        try (InputStreamPlus in = pre.getInputStreamPlus(false)) {
            //            if (useMultipartUpload) { // 分片上传
            //            } else {
            BufferedInputStream bin = new BufferedInputStream(in, RequestClientOptions.DEFAULT_STREAM_BUFFER_SIZE);
            PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(bucketName).setKey(newFileKey).setContent(bin);
            if (listener != null) {
                listener.start();
            }
            client.putObject(putObjectInput);
            if (listener != null) listener.finish();
            //            }
            if (fileInfo.getSize() == null) fileInfo.setSize(in.getProgressSize());

            // 上传缩略图
            byte[] thumbnailBytes = pre.getThumbnailBytes();
            if (thumbnailBytes != null) { // 上传缩略图
                String newThFileKey = getThFileKey(fileInfo);
                fileInfo.setThUrl(domain + newThFileKey);
                BufferedInputStream bin2 = new BufferedInputStream(in, RequestClientOptions.DEFAULT_STREAM_BUFFER_SIZE);
                PutObjectInput putObjectInput2 = new PutObjectInput()
                    .setBucket(bucketName).setKey(newThFileKey).setContent(bin2);
                client.putObject(putObjectInput2);
            }

            return true;
        } catch (Exception e) {
            try {
                //                if (useMultipartUpload) {
                //                    client.abortMultipartUpload(new AbortMultipartUploadInput(newFileKey, uploadId));
                //                } else {
                client.deleteObject(bucketName, newFileKey);
                //                }
            } catch (Exception ignored) {
            }
            throw ExceptionFactory.upload(fileInfo, platform, e);
        }
    }

    @Override
    public MultipartUploadSupportInfo isSupportMultipartUpload() {
        return MultipartUploadSupportInfo.supportAll();
    }

    @Override
    public void initiateMultipartUpload(FileInfo fileInfo, InitiateMultipartUploadPretreatment pre) {
        /*fileInfo.setBasePath(basePath);
        String newFileKey = getFileKey(fileInfo);
        fileInfo.setUrl(domain + newFileKey);
        ObjectMetadata metadata = getObjectMetadata(fileInfo);
        TOSV2 client = getClient();
        try {
            String uploadId = client.initiateMultipartUpload(
                    new InitiateMultipartUploadRequest(bucketName, newFileKey, metadata))
                .getUploadId();

            fileInfo.setUploadId(uploadId);
        } catch (Exception e) {
            throw ExceptionFactory.initiateMultipartUpload(fileInfo, platform, e);
        }*/
    }

    @Override
    public FilePartInfo uploadPart(UploadPartPretreatment pre) {
        /*FileInfo fileInfo = pre.getFileInfo();
        String newFileKey = getFileKey(fileInfo);
        TOSV2 client = getClient();
        FileWrapper partFileWrapper = pre.getPartFileWrapper();
        Long partSize = partFileWrapper.getSize();
        try (InputStreamPlus in = pre.getInputStreamPlus()) {
            // Amazon S3 比较特殊，上传分片必须传入分片大小，这里强制获取，可能会占用大量内存
            if (partSize == null) partSize = partFileWrapper.getInputStreamMaskResetReturn(Tools::getSize);
            UploadPartRequest part = new UploadPartRequest();
            part.setBucketName(bucketName);
            part.setKey(newFileKey);
            part.setUploadId(fileInfo.getUploadId());
            part.setInputStream(in);
            part.setPartSize(partSize);
            part.setPartNumber(pre.getPartNumber());
            PartETag partETag = client.uploadPart(part).getPartETag();
            FilePartInfo filePartInfo = new FilePartInfo(fileInfo);
            filePartInfo.setETag(partETag.getETag());
            filePartInfo.setPartNumber(partETag.getPartNumber());
            filePartInfo.setPartSize(in.getProgressSize());
            filePartInfo.setCreateTime(new Date());
            return filePartInfo;
        } catch (Exception e) {
            throw ExceptionFactory.uploadPart(fileInfo, platform, e);
        }*/
        return null;
    }

    @Override
    public void completeMultipartUpload(CompleteMultipartUploadPretreatment pre) {
       /* FileInfo fileInfo = pre.getFileInfo();
        String newFileKey = getFileKey(fileInfo);
        CannedAccessControlList fileAcl = getAcl(fileInfo.getFileAcl());
        TOSV2 client = getClient();
        try {
            List<PartETag> partList = pre.getPartInfoList().stream()
                .map(part -> new PartETag(part.getPartNumber(), part.getETag()))
                .collect(Collectors.toList());
            ProgressListener.quickStart(pre.getProgressListener(), fileInfo.getSize());
            client.completeMultipartUpload(
                new CompleteMultipartUploadRequest(bucketName, newFileKey, fileInfo.getUploadId(), partList));
            if (fileAcl != null) client.setObjectAcl(bucketName, newFileKey, fileAcl);
            ProgressListener.quickFinish(pre.getProgressListener(), fileInfo.getSize());
        } catch (Exception e) {
            throw ExceptionFactory.completeMultipartUpload(fileInfo, platform, e);
        }*/
    }

    @Override
    public void abortMultipartUpload(AbortMultipartUploadPretreatment pre) {
        /*FileInfo fileInfo = pre.getFileInfo();
        String newFileKey = getFileKey(fileInfo);
        TOSV2Client client = getClient();
        try {
            client.abortMultipartUpload(
                new AbortMultipartUploadRequest(bucketName, newFileKey, fileInfo.getUploadId()));
        } catch (Exception e) {
            throw ExceptionFactory.abortMultipartUpload(fileInfo, platform, e);
        }*/
    }

    @Override
    public FilePartInfoList listParts(ListPartsPretreatment pre) {
        /*FileInfo fileInfo = pre.getFileInfo();
        String newFileKey = getFileKey(fileInfo);
        TOSV2 client = getClient();
        try {
            ListPartsInput request = new ListPartsInput(bucketName, newFileKey, fileInfo.getUploadId());
            request.setMaxParts(pre.getMaxParts());
            request.setPartNumberMarker(pre.getPartNumberMarker());
            PartListing result = client.listParts(request);
            FilePartInfoList list = new FilePartInfoList();
            list.setFileInfo(fileInfo);
            list.setList(result.getParts().stream()
                .map(p -> {
                    FilePartInfo filePartInfo = new FilePartInfo(fileInfo);
                    filePartInfo.setETag(p.getETag());
                    filePartInfo.setPartNumber(p.getPartNumber());
                    filePartInfo.setPartSize(p.getSize());
                    filePartInfo.setLastModified(p.getLastModified());
                    return filePartInfo;
                })
                .collect(Collectors.toList()));
            list.setMaxParts(result.getMaxParts());
            list.setIsTruncated(result.isTruncated());
            list.setPartNumberMarker(result.getPartNumberMarker());
            list.setNextPartNumberMarker(result.getNextPartNumberMarker());
            return list;
        } catch (Exception e) {
            throw ExceptionFactory.listParts(fileInfo, platform, e);
        }*/
        return null;
    }

    /**
     * 获取文件的访问控制列表
     */
    public CannedAccessControlList getAcl(Object acl) {
        if (acl instanceof CannedAccessControlList) {
            return (CannedAccessControlList) acl;
        } else if (acl instanceof String || acl == null) {
            String sAcl = (String) acl;
            if (StrUtil.isEmpty(sAcl)) sAcl = defaultAcl;
            for (CannedAccessControlList item : CannedAccessControlList.values()) {
                if (item.toString().equals(sAcl)) {
                    return item;
                }
            }
            return null;
        } else {
            throw ExceptionFactory.unrecognizedAcl(acl, platform);
        }
    }

    /**
     * 获取对象的元数据
     */
    public ObjectMetadata getObjectMetadata(FileInfo fileInfo) {
        ObjectMetadata metadata = new ObjectMetadata();
        if (fileInfo.getSize() != null) metadata.setContentLength(fileInfo.getSize());
        if (fileInfo.getContentType() != null) metadata.setContentType(fileInfo.getContentType());
        metadata.setUserMetadata(fileInfo.getUserMetadata());
        if (CollUtil.isNotEmpty(fileInfo.getMetadata())) {
            fileInfo.getMetadata().forEach(metadata::setHeader);
        }
        return metadata;
    }

    /**
     * 获取缩略图对象的元数据
     */
    public ObjectMetadata getThObjectMetadata(FileInfo fileInfo) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(fileInfo.getThSize());
        metadata.setContentType(fileInfo.getThContentType());
        metadata.setUserMetadata(fileInfo.getThUserMetadata());
        if (CollUtil.isNotEmpty(fileInfo.getThMetadata())) {
            fileInfo.getThMetadata().forEach(metadata::setHeader);
        }
        return metadata;
    }

    @Override
    public boolean isSupportPresignedUrl() {
        return true;
    }

    @Override
    public String generatePresignedUrl(FileInfo fileInfo, Date expiration) {
        try {
            // 目前是对接火山云使用 预签名最多支持7天
            if (expiration.after(DateUtil.offsetDay(new Date(), 180))) {
                expiration = DateUtil.offsetDay(new Date(), 180);
            }
            // 预签名 URL 过期时间，单位为秒，设置 3600 秒即1小时后过期
            // 计算过期时间
            long expires = DateUtil.between(new Date(), expiration, DateUnit.SECOND);
            PreSignedURLInput input = new PreSignedURLInput().setBucket(bucketName).setKey(getFileKey(fileInfo))
                .setHttpMethod(HttpMethod.GET).setExpires(expires);
            PreSignedURLOutput output = getClient().preSignedURL(input);
            return output.getSignedUrl();
        } catch (Exception e) {
            throw ExceptionFactory.generatePresignedUrl(fileInfo, platform, e);
        }
    }

    @Override
    public String generateThPresignedUrl(FileInfo fileInfo, Date expiration) {
        try {
            // 目前是对接火山云使用 预签名最多支持7天
            if (expiration.after(DateUtil.offsetDay(new Date(), 7))) {
                expiration = DateUtil.offsetDay(new Date(), 7);
            }
            // 预签名 URL 过期时间，单位为秒，设置 3600 秒即1小时后过期
            // 计算过期时间
            long expires = DateUtil.between(new Date(), expiration, DateUnit.SECOND);
            PreSignedURLInput input = new PreSignedURLInput().setBucket(bucketName).setKey(getFileKey(fileInfo))
                .setHttpMethod("get").setExpires(expires);
            PreSignedURLOutput output = getClient().preSignedURL(input);
            return output.getSignedUrl();
        } catch (Exception e) {
            throw ExceptionFactory.generateThPresignedUrl(fileInfo, platform, e);
        }
    }

    @Override
    public String generatePresignedUrl(FileInfo fileInfo, Date expiration, Map<String, Object> queryParams) {
        try {
            // 目前是对接火山云使用 预签名最多支持7天
            if (expiration.after(DateUtil.offsetDay(new Date(), 180))) {
                expiration = DateUtil.offsetDay(new Date(), 180);
            }
            Map<String, String> header = new HashMap<>();
            if (StrUtil.isNotBlank(fileInfo.getOriginalFilename())) {
                header.put("response-content-disposition", String.format("attachment;filename=%s", URLEncoder.encode(fileInfo.getOriginalFilename(), "UTF-8")));
                if (Objects.nonNull(queryParams) && queryParams.containsKey(FileUploadSupportActionService.X_IMAGE_PROCESS)) {
                    header.put(TosHeader.QUERY_DATA_PROCESS, queryParams.get(FileUploadSupportActionService.X_IMAGE_PROCESS).toString());
                }
            }
            // 预签名 URL 过期时间，单位为秒，设置 3600 秒即1小时后过期
            // 计算过期时间
            long expires = DateUtil.between(new Date(), expiration, DateUnit.SECOND);

            PreSignedURLInput input = new PreSignedURLInput()
                .setBucket(bucketName)
                .setKey(getFileKey(fileInfo))
                .setHttpMethod(HttpMethod.GET)
                .setExpires(expires)
                .setQuery(header);
            PreSignedURLOutput output = getClient().preSignedURL(input);
            return output.getSignedUrl();
        } catch (Exception e) {
            throw ExceptionFactory.generatePresignedUrl(fileInfo, platform, e);
        }
    }

    @Override
    public boolean isSupportAcl() {
        return true;
    }

    @Override
    public boolean setFileAcl(FileInfo fileInfo, Object acl) {
        CannedAccessControlList oAcl = getAcl(acl);
        try {
            if (oAcl == null) return false;
            getClient().putObjectAcl(bucketName, (PutObjectAclInput) acl);
            return true;
        } catch (Exception e) {
            throw ExceptionFactory.setFileAcl(fileInfo, oAcl, platform, e);
        }
    }

    @Override
    public boolean setThFileAcl(FileInfo fileInfo, Object acl) {
        CannedAccessControlList oAcl = getAcl(acl);
        if (oAcl == null) return false;
        String key = getThFileKey(fileInfo);
        if (key == null) return false;
        try {
            getClient().putObjectAcl(bucketName, (PutObjectAclInput) acl);
            return true;
        } catch (Exception e) {
            throw ExceptionFactory.setThFileAcl(fileInfo, oAcl, platform, e);
        }
    }

    @Override
    public boolean isSupportMetadata() {
        return true;
    }

    @Override
    public boolean delete(FileInfo fileInfo) {
        TOSV2 client = getClient();
        try {
            if (fileInfo.getThFilename() != null) { // 删除缩略图
                client.deleteObject(bucketName, getThFileKey(fileInfo));
            }
            client.deleteObject(bucketName, getFileKey(fileInfo));
            return true;
        } catch (Exception e) {
            throw ExceptionFactory.delete(fileInfo, platform, e);
        }
    }

    @Override
    public boolean exists(FileInfo fileInfo) {
        try {
            return null != getClient().getObject(bucketName, getFileKey(fileInfo), null);
        } catch (Exception e) {
            throw ExceptionFactory.exists(fileInfo, platform, e);
        }
    }

    @Override
    public void download(FileInfo fileInfo, Consumer<InputStream> consumer) {
        GetObjectOutput object = getClient().getObject(bucketName, getFileKey(fileInfo));
        try (InputStream in = object.getContent()) {
            consumer.accept(in);
        } catch (Exception e) {
            throw ExceptionFactory.download(fileInfo, platform, e);
        }
    }

    @Override
    public void downloadTh(FileInfo fileInfo, Consumer<InputStream> consumer) {
        Check.downloadThBlankThFilename(platform, fileInfo);

        GetObjectOutput object = getClient().getObject(bucketName, getThFileKey(fileInfo));
        try (InputStream in = object.getContent()) {
            consumer.accept(in);
        } catch (Exception e) {
            throw ExceptionFactory.downloadTh(fileInfo, platform, e);
        }
    }

    @Override
    public boolean isSupportSameCopy() {
        return true;
    }

    @Override
    public void sameCopy(FileInfo srcFileInfo, FileInfo destFileInfo, CopyPretreatment pre) {

    }
}
