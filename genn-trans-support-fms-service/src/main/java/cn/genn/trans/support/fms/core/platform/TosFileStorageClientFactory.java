package cn.genn.trans.support.fms.core.platform;

import cn.genn.trans.support.fms.core.FileStorageProperties;
import com.volcengine.tos.TOSClientConfiguration;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.auth.StaticCredentials;
import com.volcengine.tos.transport.TransportConfig;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.IOException;

/**
 * TOS 火山云的 Client 工厂
 */
@Getter
@Setter
@NoArgsConstructor
public class TosFileStorageClientFactory implements FileStorageClientFactory<TOSV2> {
    private String platform;
    private String accessKey;
    private String secretKey;
    private String region;
    private String endPoint;
    private volatile TOSV2 client;

    public TosFileStorageClientFactory(FileStorageProperties.TosConfig config) {
        platform = config.getPlatform();
        accessKey = config.getAccessKey();
        secretKey = config.getSecretKey();
        region = config.getRegion();
        endPoint = config.getEndPoint();
    }

    @Override
    public TOSV2 getClient() {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    int connectTimeoutMills = 10000;
                    TransportConfig config = TransportConfig.builder()
                        .connectTimeoutMills(connectTimeoutMills)
                        .build();
                    TOSClientConfiguration configuration = TOSClientConfiguration.builder()
                        .transportConfig(config)
                        .region("cn-beijing")
                        .endpoint(endPoint)
                        .credentials(new StaticCredentials(accessKey, secretKey))
                        .build();

                    TOSV2 tos = new TOSV2ClientBuilder().build(configuration);
                    return tos;
                }
            }
        }
        return client;
    }

    @Override
    public void close() {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            client = null;
        }
    }
}
