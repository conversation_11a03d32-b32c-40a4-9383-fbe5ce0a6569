package cn.genn.trans.support.fms.core.tika;

import org.apache.tika.Tika;
import cn.genn.trans.support.fms.core.tika.TikaFactory;

/**
 * 默认的 Tika 工厂类
 */
public class DefaultTikaFactory implements TikaFactory {
    private volatile Tika tika;

    @Override
    public Tika getTika() {
        if (tika == null) {
            synchronized (this) {
                if (tika == null) {
                    tika = new Tika();
                }
            }
        }
        return tika;
    }
}
