package cn.genn.trans.support.fms.core.upload;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.aspect.AbortMultipartUploadAspectChain;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.exception.Check;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.AbortMultipartUploadPretreatment;

import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 手动分片上传-取消执行器
 */
public class AbortMultipartUploadActuator {
    private final FileStorageService fileStorageService;
    private final cn.genn.trans.support.fms.core.upload.AbortMultipartUploadPretreatment pre;

    public AbortMultipartUploadActuator(AbortMultipartUploadPretreatment pre) {
        this.pre = pre;
        this.fileStorageService = pre.getFileStorageService();
    }

    /**
     * 执行取消
     */
    public FileInfo execute() {
        FileInfo fileInfo = pre.getFileInfo();
        Check.abortMultipartUpload(fileInfo);

        FileStorage fileStorage = fileStorageService.getFileStorageVerify(fileInfo.getPlatform());
        CopyOnWriteArrayList<FileStorageAspect> aspectList = fileStorageService.getAspectList();
        FileRecorder fileRecorder = fileStorageService.getFileRecorder();

        return new AbortMultipartUploadAspectChain(aspectList, (_pre, _fileStorage, _fileRecorder) -> {
                    FileInfo _fileInfo = _pre.getFileInfo();
                    _fileStorage.abortMultipartUpload(_pre);
                    _fileRecorder.deleteFilePartByUploadId(_fileInfo.getUploadId());
                    _fileRecorder.delete(_fileInfo.getUrl());
                    return _fileInfo;
                })
                .next(pre, fileStorage, fileRecorder);
    }
}
