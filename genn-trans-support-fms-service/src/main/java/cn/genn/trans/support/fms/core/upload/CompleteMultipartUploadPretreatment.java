package cn.genn.trans.support.fms.core.upload;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.ProgressListener;
import cn.genn.trans.support.fms.core.ProgressListenerSetter;
import cn.genn.trans.support.fms.core.upload.CompleteMultipartUploadActuator;
import cn.genn.trans.support.fms.core.upload.FilePartInfo;

import java.util.List;

/**
 * 手动分片上传-完成预处理器
 */
@Getter
@Setter
@Accessors(chain = true)
public class CompleteMultipartUploadPretreatment
        implements ProgressListenerSetter<CompleteMultipartUploadPretreatment> {
    /**
     * 文件存储服务类
     */
    private FileStorageService fileStorageService;
    /**
     * 文件信息
     */
    private FileInfo fileInfo;
    /**
     * 文件分片信息，不传则自动使用全部已上传的分片
     */
    private List<FilePartInfo> partInfoList;
    /**
     * 完成进度监听
     */
    private ProgressListener progressListener;

    /**
     * 执行完成
     */
    public FileInfo complete() {
        return new CompleteMultipartUploadActuator(this).execute();
    }
}
