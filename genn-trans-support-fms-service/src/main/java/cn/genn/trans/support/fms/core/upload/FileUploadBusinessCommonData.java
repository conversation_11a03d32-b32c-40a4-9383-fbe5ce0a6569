package cn.genn.trans.support.fms.core.upload;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Date: 2024/5/13
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class FileUploadBusinessCommonData {
    /**
     * 系统id
     */
    private Long systemId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 业务标识
     */
    private String businessCode;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;
}
