package cn.genn.trans.support.fms.core.upload;

import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.aspect.UploadPartAspectChain;
import cn.genn.trans.support.fms.core.exception.Check;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.upload.FilePartInfo;
import cn.genn.trans.support.fms.core.upload.UploadPartPretreatment;

import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 手动分片上传-上传分片执行器
 */
public class UploadPartActuator {
    private final FileStorageService fileStorageService;
    private final cn.genn.trans.support.fms.core.upload.UploadPartPretreatment pre;

    public UploadPartActuator(UploadPartPretreatment pre) {
        this.pre = pre;
        this.fileStorageService = pre.getFileStorageService();
    }

    /**
     * 执行上传
     */
    public cn.genn.trans.support.fms.core.upload.FilePartInfo execute() {
        FileInfo fileInfo = pre.getFileInfo();
        Check.uploadPart(fileInfo);

        FileStorage fileStorage = fileStorageService.getFileStorageVerify(fileInfo.getPlatform());
        CopyOnWriteArrayList<FileStorageAspect> aspectList = fileStorageService.getAspectList();
        FileRecorder fileRecorder = fileStorageService.getFileRecorder();

        return new UploadPartAspectChain(aspectList, (_pre, _fileStorage, _fileRecorder) -> {
                    FilePartInfo filePartInfo = _fileStorage.uploadPart(_pre);
                    filePartInfo.setHashInfo(_pre.getHashCalculatorManager().getHashInfo());
                    _fileRecorder.saveFilePart(filePartInfo);
                    return filePartInfo;
                })
                .next(pre, fileStorage, fileRecorder);
    }
}
