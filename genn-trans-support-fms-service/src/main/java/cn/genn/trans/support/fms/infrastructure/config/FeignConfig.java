package cn.genn.trans.support.fms.infrastructure.config;

import cn.genn.trans.support.fms.infrastructure.feign.MagicTokenRequestInterceptor;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置类
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Configuration
public class FeignConfig {

    /**
     * 注册MagicToken请求拦截器
     */
    @Bean
    public RequestInterceptor magicTokenRequestInterceptor() {
        return new MagicTokenRequestInterceptor();
    }
}
