package cn.genn.trans.support.fms.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件模板配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = FileBusinessConfigProperties.PREFIX)
public class FileBusinessConfigProperties {

    public static final String PREFIX = "genn.business.config";

    /**
     * 配置哪些businessCode可以不校验上传文件的租户和读取文件的租户必须一致
     */
    private List<String> noteNeedTenantCheckOfbusinessCode = new ArrayList<>();

    /**
     * 文件模板配置 key:业务场景 value:文件模板fileKey
     */
    private FileTemplateConfig template = new FileTemplateConfig();

    /**
     * 图片生成模板配置 key:业务场景 value:freemark模板
     */
    private FileTemplateConfig imageTemplate = new FileTemplateConfig();

    @Data
    public static class FileTemplateConfig {
        private Map<String, String> config = new HashMap<>();
    }

}
