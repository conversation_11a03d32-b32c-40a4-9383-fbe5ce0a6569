package cn.genn.trans.support.fms.infrastructure.config;

import cn.genn.trans.support.fms.infrastructure.http.MagicTokenRestTemplateInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * RestTemplate配置类
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Configuration
public class RestTemplateConfig {

    @Resource
    private MagicTokenRestTemplateInterceptor magicTokenRestTemplateInterceptor;

    /**
     * 配置带有MagicToken拦截器的RestTemplate
     */
    @Bean
    @Primary
    public RestTemplate restTemplateWithMagicToken() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 添加拦截器
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(magicTokenRestTemplateInterceptor);
        restTemplate.setInterceptors(interceptors);
        
        return restTemplate;
    }

    /**
     * 不带MagicToken拦截器的RestTemplate（用于特殊场景）
     */
    @Bean("plainRestTemplate")
    public RestTemplate plainRestTemplate() {
        return new RestTemplate();
    }
}
