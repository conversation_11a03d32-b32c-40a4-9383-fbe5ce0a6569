package cn.genn.trans.support.fms.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * TOS对应的桶的IAM角色
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = TosBucketConfigProperties.PREFIX)
public class TosBucketConfigProperties {

    public static final String PREFIX = "genn.business.tos";

    /**
     * TOS对应的桶的IAM角色配置 key:桶名 value:IAM角色
     */
    private TosBucketIAMConfig iamConfig = new TosBucketIAMConfig();

    @Data
    public static class TosBucketIAMConfig {
        private Map<String, String> config = new HashMap<>();
    }

}
