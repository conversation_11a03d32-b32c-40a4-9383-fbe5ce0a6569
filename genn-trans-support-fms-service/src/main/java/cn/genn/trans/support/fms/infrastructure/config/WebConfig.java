package cn.genn.trans.support.fms.infrastructure.config;


import cn.genn.trans.upm.interfaces.base.web.filter.SsoAuthFilter;
import cn.genn.trans.upm.interfaces.base.web.filter.SsoLoginFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import javax.annotation.Resource;

@Configuration
public class WebConfig extends WebMvcConfigurerAdapter {

    @Resource
    @Lazy
    private SsoLoginFilter ssoLoginFilter;
    @Resource
    @Lazy
    private SsoAuthFilter ssoAuthFilter;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(ssoLoginFilter)
            .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html", "/favicon.ico", "/error");
        registry.addInterceptor(ssoAuthFilter)
            .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html", "/favicon.ico", "/error");
    }
}
