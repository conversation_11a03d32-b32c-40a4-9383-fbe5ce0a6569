package cn.genn.trans.support.fms.infrastructure.context;

import lombok.extern.slf4j.Slf4j;

/**
 * Magic Token 上下文管理器
 * 用于在异步任务执行过程中传递tokenData
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Slf4j
public class MagicTokenContext {

    private static final ThreadLocal<String> TOKEN_DATA_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 设置当前线程的tokenData
     *
     * @param tokenData 用户token数据
     */
    public static void setTokenData(String tokenData) {
        TOKEN_DATA_THREAD_LOCAL.set(tokenData);
        log.debug("设置MagicToken上下文: {}", tokenData != null ? "已设置" : "已清空");
    }

    /**
     * 获取当前线程的tokenData
     *
     * @return tokenData
     */
    public static String getTokenData() {
        return TOKEN_DATA_THREAD_LOCAL.get();
    }

    /**
     * 清除当前线程的tokenData
     */
    public static void clear() {
        TOKEN_DATA_THREAD_LOCAL.remove();
        log.debug("清除MagicToken上下文");
    }

    /**
     * 在指定的代码块中执行，并自动管理tokenData的生命周期
     *
     * @param tokenData 用户token数据
     * @param runnable 要执行的代码块
     */
    public static void runWithTokenData(String tokenData, Runnable runnable) {
        String originalTokenData = getTokenData();
        try {
            setTokenData(tokenData);
            runnable.run();
        } finally {
            if (originalTokenData != null) {
                setTokenData(originalTokenData);
            } else {
                clear();
            }
        }
    }

    /**
     * 在指定的代码块中执行，并自动管理tokenData的生命周期，支持返回值
     *
     * @param tokenData 用户token数据
     * @param supplier 要执行的代码块
     * @param <T> 返回值类型
     * @return 执行结果
     */
    public static <T> T runWithTokenData(String tokenData, java.util.function.Supplier<T> supplier) {
        String originalTokenData = getTokenData();
        try {
            setTokenData(tokenData);
            return supplier.get();
        } finally {
            if (originalTokenData != null) {
                setTokenData(originalTokenData);
            } else {
                clear();
            }
        }
    }
}
