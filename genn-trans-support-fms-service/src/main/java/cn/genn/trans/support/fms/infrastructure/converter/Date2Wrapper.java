package cn.genn.trans.support.fms.infrastructure.converter;

import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class Date2Wrapper implements DataWrapper {
    @Override
    public Object wrap(Object data) {
        if (data instanceof LocalDateTime) {
            return ((LocalDateTime) data).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        return data;
    }
}
