package cn.genn.trans.support.fms.infrastructure.converter;

import cn.genn.core.utils.RmbUtils;
import cn.genn.trans.support.fms.infrastructure.utils.DataWrapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 分转元
 */
public class FenToYuanWrapper implements DataWrapper {
    @Override
    public Object wrap(Object source) {
        if (source == null) {
            return "";
        } else if (source instanceof BigDecimal) {
            return ((BigDecimal)source).divide(BigDecimal.valueOf(100L), 2, RoundingMode.UNNECESSARY).toString();
        } else if (source instanceof Long) {
            return RmbUtils.fen2yuan((Long)source);
        } else if (source instanceof Integer) {
            return RmbUtils.fen2yuan((long)(Integer)source);
        } else if (source instanceof Float) {
            return RmbUtils.fen2yuan((double)(Float)source);
        } else if (source instanceof Double) {
            return RmbUtils.fen2yuan((Double)source);
        } else if (source instanceof String) {
            return RmbUtils.fen2yuan(Double.parseDouble((String)source));
        } else if (source instanceof Number) {
            return RmbUtils.fen2yuan(((Number)source).doubleValue());
        } else {
            throw new IllegalArgumentException("不支持的类型转换");
        }
    }
}
