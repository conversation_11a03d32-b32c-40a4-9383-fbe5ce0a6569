package cn.genn.trans.support.fms.infrastructure.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 * <AUTHOR>
 */
public enum MessageCode implements MessageCodeWrap {

    BUCKET_CONFIG_NOT_EXIST("201", "租户对应的bucket配置不存在"),
    BUSINESS_CONFIG_NOT_EXIST("202", "租户对应的业务场景配置不存在"),
    OPERATOR_USER_INFO_NOT_EXIST("203", "操作人信息获取失败,检查token信息"),
    MIME_TYPE_NOT_ALLOWED("204", "不允许上传的文件类型"),
    FILE_SIZE_NOT_ALLOWED("205", "上传文件大小超出限制"),
    FILE_STREAM_READ_ERROR("206", "文件流读取出现异常"),
    FILE_NOT_EXIST("207", "文件不存在"),
    BUSINESS_CODE_NOT_EXIST("208", "业务场景不能为空"),
    TASK_IN_NOT_WAITING("209", "当前任务不处于待执行状态"),
    TASK_ERROR_NOT_STRATEGY("210", "导出任务处理失败,不存在业务场景对应的导出策略"),
    OCR_ERROR("211", "OCR服务调用失败"),
    OCR_LIMIT_ERROR("212", "OCR服务调用失败,超出QPS限制,请稍后再试"),
    TENANT_NOT_MATCH("213", "文件对应的租户信息不一致"),
    FILE_TRANSFER_ERROR("214", "文件转存失败"),
    FILE_EXPORT_ERROR("215", "导出失败,远程服务异常"),
    FILE_HANDLE_ERROR("215", "导出失败,处理文件异常"),
    EXPORT_TEMPLATE_NOT_EXIST("216", "导出失败,不存在导出任务需要的模板文件"),
    TENANT_NOT_EXIST("217", "租户不存在"),
    INTERNAL_DOMAIN_NOT_EXIST("218", "内网域名不存在"),
    FILE_TEMPLATE_SIMPLE_PARAM_EMPTY("219", "生成文件模板参数为空"),
    FILE_NAME_GENERATION_ERROR("220","文件名生成失败"),
    INVALID_FILE_NAME_STRATEGY("221", "无效的文件名生成策略"),
    BUSINESS_CONFIG_COL_LISTNOT_EXIST("222", "未找到业务场景对应的自定义导出列配置"),
    IMPORT_EXCEL_TOO_MUCH_DATA("223", "导入文件行数超出上限"),
    IMPORT_EXCEL_HEAD_NOT_MATCH("224", "导入文件表头字段同模版不匹配，请重新确认上传的文件"),
    PLATFORM_NOT_SUPPORT("225", "不支持的云存储平台"),
    TOS_STS_CONFIG_ERROR("226", "获取火山云TOS的STS临时配置失败"),
    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getBizCode() {
        return DEFAULT_BIZ_CODE;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
