package cn.genn.trans.support.fms.infrastructure.feign;

import cn.genn.trans.support.fms.infrastructure.context.MagicTokenContext;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.hutool.core.util.StrUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Feign请求拦截器 - 自动添加magic-token请求头
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
@Slf4j
public class MagicTokenRequestInterceptor implements RequestInterceptor {

    private static final String MAGIC_TOKEN_HEADER = "magic-token";

    @Override
    public void apply(RequestTemplate template) {
        // 从上下文中获取tokenData
        String tokenData = MagicTokenContext.getTokenData();
        
        if (StrUtil.isNotBlank(tokenData)) {
            // 转换为magic-token格式并添加到请求头
            String magicToken = MagicTokenHandleUtil.handleMagicToken(tokenData);
            if (StrUtil.isNotBlank(magicToken)) {
                template.header(MAGIC_TOKEN_HEADER, magicToken);
                log.debug("自动添加magic-token请求头到Feign请求: {}", template.url());
            }
        }
    }
}
