package cn.genn.trans.support.fms.infrastructure.http;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.infrastructure.context.MagicTokenContext;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 支持自动magic-token注入的HTTP客户端工具类
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
@Slf4j
public class MagicTokenHttpClient {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 发送POST请求，自动添加magic-token请求头
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param <T> 响应数据类型
     * @return 响应数据
     */
    public <T> T postForObject(String url, Object requestBody, TypeReference<ResponseResult<T>> responseType) {
        return postForObject(url, requestBody, responseType, null);
    }

    /**
     * 发送POST请求，自动添加magic-token请求头
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param tokenData 指定的tokenData，如果为null则从上下文获取
     * @param <T> 响应数据类型
     * @return 响应数据
     */
    public <T> T postForObject(String url, Object requestBody, TypeReference<ResponseResult<T>> responseType, String tokenData) {
        return executeWithTokenData(tokenData, () -> {
            HttpHeaders headers = createHeaders();
            String requestBodyJson = JsonUtils.toJson(requestBody);
            HttpEntity<String> httpEntity = new HttpEntity<>(requestBodyJson, headers);

            try {
                log.info("远程调用, url: {}, body: {}", url, requestBodyJson);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                log.info("远程调用, result status code: {}", responseEntity.getStatusCode());

                if (HttpStatus.OK != responseEntity.getStatusCode()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }

                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<T> result = JsonUtils.parse(responseEntity.getBody(), responseType);

                if (result.isSuccess()) {
                    return result.getData();
                } else {
                    log.error("远程调用失败: {}", result.getMessage());
                    throw new BusinessException(result.getMessage());
                }
            } catch (Exception e) {
                log.error("远程调用异常", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }
        });
    }

    /**
     * 发送GET请求，自动添加magic-token请求头
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @param <T> 响应数据类型
     * @return 响应数据
     */
    public <T> T getForObject(String url, TypeReference<ResponseResult<T>> responseType) {
        return getForObject(url, responseType, null);
    }

    /**
     * 发送GET请求，自动添加magic-token请求头
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @param tokenData 指定的tokenData，如果为null则从上下文获取
     * @param <T> 响应数据类型
     * @return 响应数据
     */
    public <T> T getForObject(String url, TypeReference<ResponseResult<T>> responseType, String tokenData) {
        return executeWithTokenData(tokenData, () -> {
            HttpHeaders headers = createHeaders();
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);

            try {
                log.info("远程调用, url: {}", url);
                ResponseEntity<String> responseEntity = restTemplate.exchange(url, org.springframework.http.HttpMethod.GET, httpEntity, String.class);
                log.info("远程调用, result status code: {}", responseEntity.getStatusCode());

                if (HttpStatus.OK != responseEntity.getStatusCode()) {
                    throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
                }

                log.info("远程调用, result: {}", responseEntity.getBody());
                ResponseResult<T> result = JsonUtils.parse(responseEntity.getBody(), responseType);

                if (result.isSuccess()) {
                    return result.getData();
                } else {
                    log.error("远程调用失败: {}", result.getMessage());
                    throw new BusinessException(result.getMessage());
                }
            } catch (Exception e) {
                log.error("远程调用异常", e);
                throw new BusinessException(MessageCode.FILE_EXPORT_ERROR);
            }
        });
    }

    /**
     * 创建包含magic-token的请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");

        // 从上下文获取tokenData并转换为magic-token
        String tokenData = MagicTokenContext.getTokenData();
        if (StrUtil.isNotBlank(tokenData)) {
            String magicToken = MagicTokenHandleUtil.handleMagicToken(tokenData);
            if (StrUtil.isNotBlank(magicToken)) {
                headers.set("magic-token", magicToken);
                log.debug("添加magic-token请求头");
            }
        }

        return headers;
    }

    /**
     * 在指定的tokenData上下文中执行操作
     */
    private <T> T executeWithTokenData(String tokenData, java.util.function.Supplier<T> supplier) {
        if (tokenData != null) {
            return MagicTokenContext.runWithTokenData(tokenData, supplier);
        } else {
            return supplier.get();
        }
    }
}
