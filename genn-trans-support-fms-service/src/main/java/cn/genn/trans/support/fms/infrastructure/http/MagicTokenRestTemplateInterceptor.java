package cn.genn.trans.support.fms.infrastructure.http;

import cn.genn.trans.support.fms.infrastructure.context.MagicTokenContext;
import cn.genn.trans.support.fms.infrastructure.utils.MagicTokenHandleUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * RestTemplate请求拦截器 - 自动添加magic-token请求头
 * 
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
@Slf4j
public class MagicTokenRestTemplateInterceptor implements ClientHttpRequestInterceptor {

    private static final String MAGIC_TOKEN_HEADER = "magic-token";

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 从上下文中获取tokenData
        String tokenData = MagicTokenContext.getTokenData();
        
        if (StrUtil.isNotBlank(tokenData)) {
            // 转换为magic-token格式并添加到请求头
            String magicToken = MagicTokenHandleUtil.handleMagicToken(tokenData);
            if (StrUtil.isNotBlank(magicToken)) {
                request.getHeaders().add(MAGIC_TOKEN_HEADER, magicToken);
                log.debug("自动添加magic-token请求头到RestTemplate请求: {}", request.getURI());
            }
        }
        
        return execution.execute(request, body);
    }
}
