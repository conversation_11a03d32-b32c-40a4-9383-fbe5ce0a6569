package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;


/**
 * FileExportConfPO对象
 *
 * <AUTHOR>
 * @desc 文件导出配置
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_export_conf", autoResultMap = true)
public class FileExportConfPO {

    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 业务场景
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 业务场景
     */
    @TableField("sub_business_code")
    private String subBusinessCode;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     *
     */
    @TableField("config")
    private String config;

}

