package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * FileExportTaskPO对象
 *
 * <AUTHOR>
 * @desc 文件导出任务记录
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_export_task", autoResultMap = true)
public class FileExportTaskPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 业务场景
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 文件名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 导出文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 模板配置
     */
    @TableField("template_conf")
    private String templateConf;

    /**
     * 任务状态 -1：FAIL-执行失败，0：WAIT-待执行，1：PROCESS-执行中，2：UPLOAD-上传中，10：SUCCESS-执行成功
     */
    @TableField("task_status")
    private TaskStatusEnum taskStatus;

    /**
     * 任务参数
     */
    @TableField("task_param")
    private String taskParam;

    /**
     * token数据
     */
    @TableField("token_data")
    private String tokenData;

    /**
     * 执行次数
     */
    @TableField("execute_count")
    private Integer executeCount;

    /**
     * 导出文件唯一标识
     */
    @TableField("result_file_key")
    private String resultFileKey;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

