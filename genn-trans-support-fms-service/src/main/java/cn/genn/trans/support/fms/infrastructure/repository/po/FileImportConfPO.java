package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;


/**
 * FileImportConfPO对象
 *
 * <AUTHOR>
 * @desc 文件导入配置
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_import_conf", autoResultMap = true)
public class FileImportConfPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 业务场景
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 子业务场景
     */
    @TableField("sub_business_code")
    private String subBusinessCode;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 文件导入模板
     */
    @TableField("template_file_key")
    private String templateFileKey;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 模板配置
     */
    @TableField("template_conf")
    private String templateConf;

}

