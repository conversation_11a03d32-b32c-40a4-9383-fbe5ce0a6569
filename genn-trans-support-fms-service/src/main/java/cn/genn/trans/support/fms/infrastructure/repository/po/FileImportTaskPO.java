package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.genn.trans.support.fms.interfaces.enums.TaskStatusEnum;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * FileImportTaskPO对象
 *
 * <AUTHOR>
 * @desc 文件导入任务记录
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_import_task", autoResultMap = true)
public class FileImportTaskPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 业务场景
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 导入文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 导入文件唯一标识
     */
    @TableField("import_file_key")
    private String importFileKey;

    /**
     * 模板类型
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 模板配置
     */
    @TableField("template_conf")
    private String templateConf;

    /**
     * 任务状态 0：WAIT-待执行,1：PROCESS-执行中,2：UPLOAD-上传中,10：SUCCESS-执行成功,-2：CANCEL-取消，-1：FAIL-执行失败
     */
    @TableField("task_status")
    private TaskStatusEnum taskStatus;

    /**
     * 任务参数
     */
    @TableField("task_param")
    private String taskParam;

    /**
     * token数据
     */
    @TableField("token_data")
    private String tokenData;

    /**
     * 导入的结果文件的唯一标识
     */
    @TableField("result_file_key")
    private String resultFileKey;

    /**
     * 导入结果信息
     */
    @TableField("result_info")
    private String resultInfo;

    /**
     * 备注 失败原因
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

