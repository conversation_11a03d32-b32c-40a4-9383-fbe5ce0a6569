package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * FileUploadBucketConfigPO对象
 *
 * <AUTHOR>
 * @desc 文件上传租户bucket配置
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_upload_bucket_config", autoResultMap = true)
public class FileUploadBucketConfigPO {

    /**
     *
     */
    @TableId
    private Long id;

    /**
     *
     */
    @TableField("platform")
    private String platform;
    /**
     *
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     *
     */
    @TableField("access_key")
    private String accessKey;

    /**
     *
     */
    @TableField("secret_key")
    private String secretKey;

    /**
     *
     */
    @TableField("end_point")
    private String endPoint;

    /**
     *
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     *
     */
    @TableField("domain")
    private String domain;

    /**
     *
     */
    @TableField("base_path")
    private String basePath;

    /**
     *
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     *
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     *
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

}

