package cn.genn.trans.support.fms.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * FileUploadBusinessConfigPO对象
 *
 * <AUTHOR>
 * @desc 文件上传租户业务配置
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_upload_business_config", autoResultMap = true)
public class FileUploadBusinessConfigPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 
     */
    @TableField("limit_content_type")
    private String limitContentType;

    /**
     * 
     */
    @TableField("limit_file_size")
    private Integer limitFileSize;

    /**
     * 
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 
     */
    @TableField("path")
    private String path;

    /**
     * 
     */
    @TableField("filing_config")
    private String filingConfig;

    /**
     * 
     */
    @TableField("data_level")
    private Byte dataLevel;

    /**
     * 
     */
    @TableField("water_mark_config")
    private String waterMarkConfig;

    /**
     * 
     */
    @TableField("pre_signed_url_valid")
    private Long preSignedUrlValid;

    /**
     * 
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

}

