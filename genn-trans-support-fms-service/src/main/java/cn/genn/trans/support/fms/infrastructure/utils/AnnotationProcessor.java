package cn.genn.trans.support.fms.infrastructure.utils;

import cn.genn.trans.support.fms.application.dto.sif.DeductConfig;
import cn.genn.trans.support.fms.application.dto.sif.DriverPerformanceInfoDTO;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class AnnotationProcessor {
    public static List<String> process(String parent, Object obj) throws NoSuchFieldException {
        List<String> results = new ArrayList<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(DataBeanPath.class)) {
                DataBeanPath dataBeanPathAnnotation = field.getAnnotation(DataBeanPath.class);
                String path = StrUtil.isNotBlank(parent) ? parent + "." + dataBeanPathAnnotation.beanPath() : dataBeanPathAnnotation.beanPath();
                String fieldName = dataBeanPathAnnotation.fieldName();
                String suffix = dataBeanPathAnnotation.suffix();
                Class<? extends DataWrapper> wrapperClass = dataBeanPathAnnotation.wrapperClass();
                int sortOrder = dataBeanPathAnnotation.sortOrder();
                results.add("Path: " + path + ", Field Name: " + fieldName + ", Suffix: " + suffix + ", Wrapper Class: " + wrapperClass.getName() + ", Sort Order: " + sortOrder);
            } else if (field.isAnnotationPresent(DataBeanObject.class)) {
                try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        results.addAll(process(field.getName(), fieldValue));
                        /*Field[] innerFields = fieldValue.getClass().getDeclaredFields();
                        for (Field innerField : innerFields) {
                            innerField.setAccessible(true);
                            if (innerField.isAnnotationPresent(DataBeanPath.class)) {
                                DataBeanPath innerDataBeanPathAnnotation = innerField.getAnnotation(DataBeanPath.class);
                                String innerBeanPath = PathBuilder.buildPath(fieldValue, innerField);
                                String innerFieldName = innerField.getAnnotation(DataBeanPath.class).fieldName();
                                String innerPath = innerDataBeanPathAnnotation.beanPath();
                                String innerSuffix = innerDataBeanPathAnnotation.suffix();
                                Class<? extends DataWrapper> innerWrapperClass = innerDataBeanPathAnnotation.wrapperClass();
                                int innerSortOrder = innerDataBeanPathAnnotation.sortOrder();
                                results.add("Complex Object Inner Path: " + innerBeanPath + ", Field Name: " + innerFieldName + ", Suffix: " + innerSuffix + ", Wrapper Class: " + innerWrapperClass.getName() + ", Sort Order: " + innerSortOrder);
                            }
                        }*/
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            } else {
               /* try {
                    Object fieldValue = field.get(obj);
                    if (fieldValue!= null) {
                        results.addAll(process(fieldValue));
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }*/
            }
        }
        return results;
    }

    public static void main(String[] args) throws NoSuchFieldException {
        DriverPerformanceInfoDTO dto = new DriverPerformanceInfoDTO();
        DeductConfig deductConfig = new DeductConfig();
        deductConfig.setDeductAmount(new BigDecimal("1.2"));
//        dto.setDeductConfig(deductConfig);
        List<String> list = process("", dto);
        System.out.println(list);
    }

}
