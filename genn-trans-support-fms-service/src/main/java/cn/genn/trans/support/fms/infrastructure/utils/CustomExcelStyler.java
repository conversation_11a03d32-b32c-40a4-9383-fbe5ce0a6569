package cn.genn.trans.support.fms.infrastructure.utils;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.styler.AbstractExcelExportStyler;
import cn.afterturn.easypoi.excel.export.styler.ExcelExportStylerDefaultImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

@Slf4j
public class CustomExcelStyler extends ExcelExportStylerDefaultImpl {

    private final CellStyle integerStyle;
    private final CellStyle decimalStyle;

    public CustomExcelStyler(Workbook workbook) {
        super(workbook);
        // 初始化样式
        this.integerStyle = createIntegerStyle(workbook);
        this.decimalStyle = createDecimalStyle(workbook);
    }

    @Override
    public CellStyle getHeaderStyle(short color) {
        return super.getHeaderStyle(color);  // 默认表头样式
    }

    @Override
    public CellStyle getTitleStyle(short color) {
        return super.getTitleStyle(color);   // 默认标题样式
    }

    @Override
    public CellStyle getStyles(Cell cell, int dataRow, ExcelExportEntity entity, Object obj, Object data) {
        log.info("cell: {}, dataRow: {}, entity: {}, obj: {}, data: {}", cell, dataRow, entity, obj, data);
        // 根据列名或数据类型返回不同样式
        if ("原发数".equals(entity.getKey())) {  // 假设 "amount" 列需要特殊样式
            return isInteger(cell) ? integerStyle : decimalStyle;
        }
        return super.getStyles(cell, dataRow, entity, obj, data);  // 默认样式
    }

    private boolean isInteger(Cell cell) {
        try {
            double value = cell.getNumericCellValue();
            return value % 1 == 0;
        } catch (Exception e) {
            return false;
        }
    }

    private CellStyle createIntegerStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        style.setDataFormat(format.getFormat("0"));
        return style;
    }

    private CellStyle createDecimalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        style.setDataFormat(format.getFormat("#,##0.???"));
        return style;
    }
}
