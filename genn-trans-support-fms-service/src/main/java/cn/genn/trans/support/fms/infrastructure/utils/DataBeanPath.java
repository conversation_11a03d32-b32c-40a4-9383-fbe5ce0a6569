package cn.genn.trans.support.fms.infrastructure.utils;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 导出属性path注解
 *
 * <AUTHOR>
 * @description
 * @date 2024-08-08
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataBeanPath {
    /**
     * 导出数据字段的取值path 尤其是一些枚举类型要配置到desc
     *
     * @return
     */
    @NotNull
    String beanPath();

    /**
     * 导出字段对应的列名
     *
     * @return
     */
    @NotNull
    String fieldName();

    /**
     * 后缀 单位元一类的 拼接到后面
     * @return
     */
    String suffix() default "";

    /**
     * 导出字段的包装处理类 只能处理时间 或者 增加单位 单个的字段
     *
     * @return
     */
    Class<? extends DataWrapper> wrapperClass() default DataWrapper.class;

    /**
     * 导出字段的排序，越小越靠前
     *
     * @return
     */
    int sortOrder() default 0;

    /**
     * 分组名称 同一个分组的导出字段会放在一起 并且按分组的命名导出
     *
     * @return
     */
    String group() default "";

    /**
     * 分组导出列名
     *
     * @return
     */
    String groupName() default "";

    /**
     * 分组各字段的分隔符
     */
    String groupSeparator() default "-";
}
