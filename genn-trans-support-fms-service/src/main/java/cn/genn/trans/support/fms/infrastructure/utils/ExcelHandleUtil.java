package cn.genn.trans.support.fms.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * excel处理工具类
 *
 * @Date: 2024/8/13
 * @Author: kangjian
 */
@Slf4j
public class ExcelHandleUtil {

    public static boolean validateFirstRow(File file, List<String> expectedColumns) {
        try (FileInputStream fis = new FileInputStream(file);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return false;
            }
            List<String> expectedColumnsCopy = new ArrayList<>(expectedColumns);
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String cellValue = headerRow.getCell(i).getStringCellValue();
                expectedColumnsCopy.remove(cellValue);
            }
            if (!expectedColumnsCopy.isEmpty()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("validateFirstRow error", e);
            return false;
        }
    }

    public static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }

        // 先用物理空行判断加速
        if (row.getLastCellNum() <= 0) {
            return true;
        }

        // 遍历所有单元格进行内容检查
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (!isCellEmpty(cell)) {
                // 发现非空单元格立即返回
                return false;
            }
        }
        return true;
    }

    public static boolean isCellEmpty(Cell cell) {
        if (cell == null) {
            return true;
        }

        switch (cell.getCellType()) {
            case BLANK:
                return true;
            case STRING:
                return cell.getStringCellValue().trim().isEmpty();
            case NUMERIC:
                return false; // 数值型单元格默认视为有值
            case BOOLEAN:
                return false; // 布尔型单元格默认视为有值
            case FORMULA:
                // 根据需求判断公式计算结果
                return false; // 默认视为有值
            default:
                return true;
        }
    }

    /**
     * 设置列宽
     * 使用列宽自适应方法sheet.AutoSizeColumn(i);
     * 只能解决英文、数字列宽自适应，如果该列为中文，会出现列宽不足现象。
     * 解决方法：
     * - 1、先使用POI提供的列宽自适应方法sheet.autoSizeColumn(i)
     * - 2、再使用方法sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 17 / 10);
     */
    public static void autoSizeHeader(Sheet sheets) {
        /*try {
            // 注意：在此处跟踪所有列
            SXSSFSheet sheet = (SXSSFSheet) sheets;
            sheet.trackAllColumnsForAutoSizing();

            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                int lastCellNum = headerRow.getLastCellNum();
                for (int i = 0; i < lastCellNum; i++) {
                    Cell cell = headerRow.getCell(i);
                    if (cell != null) {
                        CellStyle cellStyle = cell.getCellStyle();
                        // 取消自动换行
                        cellStyle.setWrapText(false);
                        // 根据表头字段设置列宽
                        // int width = cell.getStringCellValue().length() * 750;
                        // sheet.setColumnWidth(i, width);
                    }
                    // 1、先设置自动列宽
                    if (i < sheet.getRow(0).getLastCellNum()) {
                        sheet.autoSizeColumn(i);
                    }
                    // 2、再手动设置列宽度，设置列宽为自动列宽的1.7倍，为什么是1.7倍？(经过测试所得，也可以是1.5倍、1.6倍)
                    // The maximum column width for an individual cell is 255 characters.
                    //                int width = sheet.getColumnWidth(i) * 17 / 10;
                    //                sheet.setColumnWidth(i, Math.min(width, 255 * 256)); // 确保不超过最大列宽
                }
            }
        } catch (Exception e) {
            log.info("设置自动列宽失败autoSizeHeader error", e);
        }*/
    }

    // 在指定位置插入新行
    public static void insertRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();

        // 检查 rowIndex 是否在有效范围内
        if (rowIndex < 0 || rowIndex > lastRowNum) {
            throw new IllegalArgumentException("Row index " + rowIndex + " is out of valid range [0, " + lastRowNum + "]");
        }

        // 如果 rowIndex 不是最后一行，且最后一行存在，则执行移动操作
        if (lastRowNum >= rowIndex) {
            for (int i = lastRowNum; i >= rowIndex; i--) {
                Row currentRow = sheet.getRow(i);
                if (currentRow != null) {
                    Row newRow = sheet.createRow(i + 1);
                    copyRow(currentRow, newRow);
                    sheet.removeRow(currentRow);
                }
            }
        }
        // 创建新行
        sheet.createRow(rowIndex);
    }


    // 复制行内容到新行
    public static void copyRow(Row sourceRow, Row newRow) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell oldCell = sourceRow.getCell(i);
            if (oldCell != null) {
                Cell newCell = newRow.createCell(i);
                newCell.setCellValue(oldCell.getStringCellValue());
                newCell.setCellStyle(oldCell.getCellStyle());
            }
        }
    }

    public static void copySheet(Sheet sourceSheet, Sheet targetSheet) {
        Map<CellStyle, CellStyle> styleCache = new HashMap<>();
        // 复制合并区域
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            targetSheet.addMergedRegion(mergedRegion);
        }

        for (Row sourceRow : sourceSheet) {
            Row targetRow = targetSheet.createRow(sourceRow.getRowNum());
            targetRow.setHeight(sourceRow.getHeight());
            for (Cell sourceCell : sourceRow) {
                Cell targetCell = targetRow.createCell(sourceCell.getColumnIndex());
                copyCell(sourceCell, targetCell, styleCache);
            }
        }

        // 复制列宽
        if (null != sourceSheet.getRow(0)) {
            for (int i = 0; i < sourceSheet.getRow(0).getLastCellNum(); i++) {
                targetSheet.setColumnWidth(i, sourceSheet.getColumnWidth(i));
            }
        }

    }

    /**
     * 需要增加单元格样式缓存 要不然会报错The maximum number of Cell Styles was exceeded. You can define up to 64000 style in a .xlsx Workbook
     * 创建单元格样式数量超过限制导致报错
     *
     * @param sourceCell
     * @param targetCell
     */
    public static void copyCell(Cell sourceCell, Cell targetCell, Map<CellStyle, CellStyle> styleCache) {
        /**
         *  // 复制单元格样式
         *         CellStyle newStyle = targetCell.getSheet().getWorkbook().createCellStyle();
         *         newStyle.cloneStyleFrom(sourceCell.getCellStyle());
         *         targetCell.setCellStyle(newStyle);
         */
        // 获取源单元格的样式
        CellStyle sourceStyle = sourceCell.getCellStyle();

        // 从缓存中获取目标单元格的样式，如果不存在则创建并缓存
        CellStyle newStyle = styleCache.computeIfAbsent(sourceStyle, style -> {
            CellStyle clonedStyle = targetCell.getSheet().getWorkbook().createCellStyle();
            cloneCellStyle(clonedStyle, sourceStyle, targetCell);
            return clonedStyle;
        });

        // 设置目标单元格的样式
        targetCell.setCellStyle(newStyle);

        // 复制单元格内容
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                break;
        }
    }

    private static void cloneCellStyle(CellStyle targetStyle, CellStyle sourceStyle, Cell targetCell) {
        // 复制基本样式
        targetStyle.cloneStyleFrom(sourceStyle);

        // 手动设置外边框
        targetStyle.setBorderTop(sourceStyle.getBorderTop());
        targetStyle.setBorderBottom(sourceStyle.getBorderBottom());
        targetStyle.setBorderLeft(sourceStyle.getBorderLeft());
        targetStyle.setBorderRight(sourceStyle.getBorderRight());

        // 手动设置边框颜色
        targetStyle.setTopBorderColor(sourceStyle.getTopBorderColor());
        targetStyle.setBottomBorderColor(sourceStyle.getBottomBorderColor());
        targetStyle.setLeftBorderColor(sourceStyle.getLeftBorderColor());
        targetStyle.setRightBorderColor(sourceStyle.getRightBorderColor());

        // 手动设置水平对齐方式
        targetStyle.setAlignment(sourceStyle.getAlignment());

        // 手动设置垂直对齐方式
        targetStyle.setVerticalAlignment(sourceStyle.getVerticalAlignment());

        // 手动设置填充模式和颜色
        targetStyle.setFillPattern(sourceStyle.getFillPattern());
        targetStyle.setFillForegroundColor(sourceStyle.getFillForegroundColor());
        targetStyle.setFillBackgroundColor(sourceStyle.getFillBackgroundColor());

        // 手动设置字体
        //        Workbook workbook = targetCell.getSheet().getWorkbook();
        //        short fontIndex = sourceStyle.getFontIndex();
        //        Font sourceFont = workbook.getFontAt(fontIndex);
        //        Font targetFont = workbook.createFont();
        //        targetFont.setFontName(sourceFont.getFontName());
        //        targetFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
        //        targetFont.setBold(sourceFont.getBold());
        //        targetFont.setItalic(sourceFont.getItalic());
        //        targetFont.setColor(sourceFont.getColor());
        //        targetFont.setUnderline(sourceFont.getUnderline());
        //        targetFont.setStrikeout(sourceFont.getStrikeout());
        //        targetStyle.setFont(targetFont);

        // 手动设置数据格式
        targetStyle.setDataFormat(sourceStyle.getDataFormat());

        // 手动设置是否自动换行
        targetStyle.setWrapText(sourceStyle.getWrapText());

        // 手动设置是否隐藏
        targetStyle.setHidden(sourceStyle.getHidden());

        // 手动设置是否锁定
        targetStyle.setLocked(sourceStyle.getLocked());
    }

    /**
     * 对excel中指定的列名下的所有列数据输出样式改为数字
     *
     * @param sheets
     * @param numberStyleColNameList
     */
    public static void handleNumberStyleColNameList(Sheet sheets, List<String> numberStyleColNameList) {
        try {
            Row headerRow = sheets.getRow(0);
            if (headerRow == null) {
                log.error("Header row is null");
                return;
            }
            Map<String, Integer> headerIndexMap = new HashMap<>();
            for (Cell cell : headerRow) {
                headerIndexMap.put(cell.getStringCellValue(), cell.getColumnIndex());
            }
            Workbook workbook = sheets.getWorkbook();
            DataFormat format = workbook.createDataFormat();

            for (String colName : numberStyleColNameList) {
                Integer colIndex = headerIndexMap.get(colName);
                if (colIndex == null) {
                    log.warn("Column name {} not found in header", colName);
                    continue;
                }
                for (int rowIndex = 1; rowIndex <= sheets.getLastRowNum(); rowIndex++) { // 从第一行数据开始
                    Row row = sheets.getRow(rowIndex);
                    if (row == null) {
                        row = sheets.createRow(rowIndex);
                    }
                    Cell cell = row.getCell(colIndex);
                    if (cell == null) {
                        cell = row.createCell(colIndex);
                    }
                    // 如果单元格原本是字符串类型，需要转换为数字类型
                    if (cell.getCellType() == CellType.STRING) {
                        try {
                            double numericValue = Double.parseDouble(cell.getStringCellValue());
                            cell.setCellValue(numericValue);
                            CellStyle numberCellStyle = workbook.createCellStyle();
                            // 判断有没有小数位
                            if (numericValue % 1 == 0) {
                                numberCellStyle.setDataFormat(format.getFormat("0")); // 设置数字格式为自适应显示
                            } else {
                                numberCellStyle.setDataFormat(format.getFormat("#,##0.???")); // 设置数字格式为自适应显示最多三位小数
                            }
                            cell.setCellStyle(numberCellStyle);
                        } catch (NumberFormatException e) {
                            log.warn("Cell value in row {} and column {} is not a valid number: {}", rowIndex, colIndex, cell.getStringCellValue());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("handleNumberStyleColNameList error", e);
        }
    }

    public static InputStream toInputStream(Workbook workbook) {
        InputStream in = null;
        ByteArrayOutputStream out = null;
        try {
            out = new ByteArrayOutputStream();
            workbook.write(out);
            byte[] bookByteAry = out.toByteArray();
            in = new ByteArrayInputStream(bookByteAry);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return in;
    }
}
