package cn.genn.trans.support.fms.infrastructure.utils;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.core.FileInfo;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.corehr.v1.model.Object;
import com.lark.oapi.service.document_ai.v1.model.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 飞书开放平台接口
 */
@Slf4j
@Component
public class FeiShuOpenApiClient {
    private static final String FEISHU_TENANT_ACCESS_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
    private static final String API_KEY = "cli_a6c7e0a32e3c900b";
    private static final String API_SECRET = "TVah68rjR0JHFBu0ilFDBbKmayOUsgiQ";

    @Value("${genn.ocr.tempFilePath:temp/}")
    private String octTempFilePath;

    @SneakyThrows
    public BusinessLicense ocrOfBusinessLicense(FileInfo fileInfo) {
        File file = null;
        try {
            Client client = Client.newBuilder(API_KEY, API_SECRET).build();
            file = getFile(fileInfo);
            RecognizeBusinessLicenseReq req = RecognizeBusinessLicenseReq.newBuilder()
                .recognizeBusinessLicenseReqBody(
                    RecognizeBusinessLicenseReqBody.newBuilder()
                        .file(file)
                        .build())
                .build();
            RecognizeBusinessLicenseResp resp = client.documentAi().businessLicense().recognize(req);
            log.info("ocrOfBusinessLicense resp code={} msg={} data={}", resp.getCode(), resp.getMsg(), JsonUtils.toJson(resp.getData()));
            if (!resp.success()) {
                String errorMessage = String.format("code:%s,msg:%s,reqId:%s", resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error("ocrOfBusinessLicense error msg={}", errorMessage);
                return null;
            }
            // 业务数据处理
            RecognizeBusinessLicenseRespBody data = resp.getData();
            if (ObjUtil.isNull(data)) {
                log.error("ocrOfBusinessLicense result:{}", data);
                throw new BusinessException(MessageCode.OCR_ERROR);
            }
            return data.getBusinessLicense();
        } catch (Exception e) {
            log.error("ocrOfBusinessLicense error", e);
            throw new BusinessException(MessageCode.OCR_ERROR);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(file)) {
                FileUtils.deleteQuietly(file);
            }
        }
    }

    @SneakyThrows
    public IdCard ocrOfIdCard(FileInfo fileInfo) {
        File file = null;
        try {
            Client client = Client.newBuilder(API_KEY, API_SECRET).build();
            file = getFile(fileInfo);
            RecognizeIdCardReq req = RecognizeIdCardReq.newBuilder()
                .recognizeIdCardReqBody(RecognizeIdCardReqBody.newBuilder()
                    .file(file).build())
                .build();
            RecognizeIdCardResp resp = client.documentAi().idCard().recognize(req);
            log.info("ocrOfIdCard resp code={} msg={} data={}", resp.getCode(), resp.getMsg(), JsonUtils.toJson(resp.getData()));
            if (!resp.success()) {
                String errorMessage = String.format("code:%s,msg:%s,reqId:%s", resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error("ocrOfIdCard error msg={}", errorMessage);
                return null;
            }
            // 业务数据处理
            RecognizeIdCardRespBody data = resp.getData();
            if (ObjUtil.isNull(data)) {
                log.error("ocrOfIdCard result:{}", data);
                throw new BusinessException(MessageCode.OCR_ERROR);
            }
            return data.getIdCard();
        } catch (Exception e) {
            log.error("ocrOfIdCard error", e);
            throw new BusinessException(MessageCode.OCR_ERROR);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(file)) {
                FileUtils.deleteQuietly(file);
            }
        }
    }

    @SneakyThrows
    public DrvingLicense ocrOfDrivingLicense(FileInfo fileInfo) {
        File file = null;
        try {
            Client client = Client.newBuilder(API_KEY, API_SECRET).build();
            file = getFile(fileInfo);
            RecognizeDrivingLicenseReq req = RecognizeDrivingLicenseReq.newBuilder()
                .recognizeDrivingLicenseReqBody(RecognizeDrivingLicenseReqBody.newBuilder()
                    .file(file)
                    .build())
                .build();
            RecognizeDrivingLicenseResp resp = client.documentAi().drivingLicense().recognize(req);
            log.info("ocrOfDrivingLicense resp code={} msg={} data={}", resp.getCode(), resp.getMsg(), JsonUtils.toJson(resp.getData()));
            if (!resp.success()) {
                String errorMessage = String.format("code:%s,msg:%s,reqId:%s", resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error("ocrOfDrivingLicense error msg={}", errorMessage);
                return null;
            }
            // 业务数据处理
            RecognizeDrivingLicenseRespBody data = resp.getData();
            if (ObjUtil.isNull(data)) {
                log.error("ocrOfDrivingLicense result:{}", data);
                throw new BusinessException(MessageCode.OCR_ERROR);
            }
            return data.getDrivingLicense();
        } catch (Exception e) {
            log.error("ocrOfDrivingLicense error", e);
            throw new BusinessException(MessageCode.OCR_ERROR);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(file)) {
                FileUtils.deleteQuietly(file);
            }
        }
    }

    @SneakyThrows
    public VehicleLicense ocrOfVehicleLicense(FileInfo fileInfo) {
        File file = null;
        try {
            Client client = Client.newBuilder(API_KEY, API_SECRET).build();
            file = getFile(fileInfo);
            RecognizeVehicleLicenseReq req = RecognizeVehicleLicenseReq.newBuilder()
                .recognizeVehicleLicenseReqBody(RecognizeVehicleLicenseReqBody.newBuilder()
                    .file(file)
                    .build())
                .build();
            RecognizeVehicleLicenseResp resp = client.documentAi().vehicleLicense().recognize(req);
            log.info("ocrOfVehicleLicense resp code={} msg={} data={}", resp.getCode(), resp.getMsg(), JsonUtils.toJson(resp.getData()));
            if (!resp.success()) {
                String errorMessage = String.format("code:%s,msg:%s,reqId:%s", resp.getCode(), resp.getMsg(), resp.getRequestId());
                log.error("ocrOfVehicleLicense error msg={}", errorMessage);
                return null;
            }
            // 业务数据处理
            RecognizeVehicleLicenseRespBody data = resp.getData();
            if (ObjUtil.isNull(data)) {
                log.error("ocrOfVehicleLicense result:{}", data);
                throw new BusinessException(MessageCode.OCR_ERROR);
            }
            return data.getVehicleLicense();
        } catch (Exception e) {
            log.error("ocrOfVehicleLicense error", e);
            throw new BusinessException(MessageCode.OCR_ERROR);
        } finally {
            // 删除临时文件
            if (Objects.nonNull(file)) {
                FileUtils.deleteQuietly(file);
            }
        }
    }

    public static String getTenantAccessToken() throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(FEISHU_TENANT_ACCESS_TOKEN_URL);
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity entity = new StringEntity("{\"app_id\": \"" + API_KEY + "\", \"app_secret\": \"" + API_SECRET + "\"}", StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                return JSONUtil.parseObj(responseBody).getStr("tenant_access_token");
            }
        }
    }

    public File getFile(FileInfo fileInfo) {
        // 替换为你要下载的文件的 URL
        String fileUrl = fileInfo.getUrl();
        // 替换为你要保存文件的路径和文件名
        String savePath = this.octTempFilePath + fileInfo.getFilename();
        try {
            // 创建 URL 对象
            URL url = new URL(fileUrl);
            // 打开连接
            URLConnection connection = url.openConnection();
            // 获取输入流
            InputStream inputStream = connection.getInputStream();
            // 创建 File 对象
            File file = new File(savePath);
            // 创建输出流
            FileOutputStream outputStream = new FileOutputStream(file);
            // 读取数据并写入文件
            int bytesRead;
            byte[] buffer = new byte[1024];
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            // 关闭输入流和输出流
            inputStream.close();
            outputStream.close();
        } catch (IOException e) {
            log.error("ocrOfBusinessLicense error", e);
            throw new BusinessException(MessageCode.OCR_ERROR);
        }
        return new File(savePath);
    }

    public String getOctTempFilePath() {
        return octTempFilePath;
    }

    public void setOctTempFilePath(String octTempFilePath) {
        this.octTempFilePath = octTempFilePath;
    }
}
