package cn.genn.trans.support.fms.infrastructure.utils;

/**
 * @Date: 2024/6/12
 * @Author: kangjian
 */
public class FileSizeUtil {

    public static final long ONE_KB = 1024;
    public static final long ONE_MB = ONE_KB * ONE_KB;
    public static final long ONE_GB = ONE_KB * ONE_MB;
    public static final long ONE_TB = ONE_KB * ONE_GB;

    public static String getHumanReadableFileSize(long size) {
        if (size < ONE_KB) {
            return size + " B";
        } else if (size < ONE_MB) {
            return String.format("%.2f KB", size / (double) ONE_KB);
        } else if (size < ONE_GB) {
            return String.format("%.2f MB", size / (double) ONE_MB);
        } else if (size < ONE_TB) {
            return String.format("%.2f GB", size / (double) ONE_GB);
        }
        return String.format("%.2f TB", size / (double) ONE_TB);
    }

    public static void main(String[] args) {
        System.out.println(getHumanReadableFileSize(20971520));
        System.out.println(ONE_GB);
        System.out.println(getHumanReadableFileSize(999999999));
        System.out.println(getHumanReadableFileSize(1073741824));
        /**
         * 20.00 MB
         * 1073741824
         * 953.67 MB
         * 1.00 GB
         */
    }
}
