package cn.genn.trans.support.fms.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;

/**
 * @Date: 2024/9/13
 * @Author: kang<PERSON>an
 */
@Slf4j
public class ImageUtil {

    /**
     * 目标图片大小，至于高度，等比例即可,ps:width在2000以上，已看不出区别
     */
    public static final int TARGET_WIDTH = 2500;
    public static Image base64ToImage2(String base64String) {
        byte[] imageBytes = Base64.getDecoder().decode(base64String);
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bis);
            //获取图片的宽、高
            double rate = image.getWidth() * 1.0 / image.getHeight();
            int targetHeight = (int) (TARGET_WIDTH * 1.0 / rate);
            //调整图片大小为 400X400尺寸
            BufferedImage newImage = resizeImage(image, TARGET_WIDTH, targetHeight);
            log.info("old:w,h= " + image.getWidth() + "," + image.getHeight() + "\nnew:w,h=" + TARGET_WIDTH + "," + targetHeight);
            //将缓冲区图片保存到 F:/test/pic1.jpg (文件不存在会自动创建文件保存，文件存在会覆盖原文件保存)
//            ImageIO.write(newImage, "jpg", new File("./b.png"));
//            bis.close();

            return image;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过BufferedImage图片流调整图片大小
     */
    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) throws IOException {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_AREA_AVERAGING);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, null);
        return outputImage;
    }
}
