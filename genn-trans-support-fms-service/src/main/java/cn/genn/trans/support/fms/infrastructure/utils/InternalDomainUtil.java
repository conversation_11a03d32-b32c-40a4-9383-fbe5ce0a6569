package cn.genn.trans.support.fms.infrastructure.utils;

import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.infrastructure.constant.CacheConstants;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.interfaces.api.IClusterService;
import cn.genn.trans.upm.interfaces.api.IUpmTenantService;
import cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 获取内网域名工具类
 *
 * @Date: 2024/8/20
 * @Author: kangjian
 */
@Component
@Slf4j
public class InternalDomainUtil {

    @Resource
    private IClusterService clusterService;

    /**
     * 根据租户id加系统id获取对应的内网域名
     *
     * @param tenantId
     * @return
     */
    @Cache(value = CacheConstants.INTERNAL_DOMAIN_CACHE, fieldKey = "#tenantId + '_' + #systemId", expireTime = 1800)
    public String getInternalDomain(Long tenantId, Long systemId) {
        // 根据租户找到租户对应的vpcGroup, vpcGroup+系统找到internal_domain
        ClusterSystemDTO clusterSystemDTO = clusterService.queryByTenantIdAndSystemId(tenantId, systemId);
        if (Objects.isNull(clusterSystemDTO)) {
            throw new BusinessException(MessageCode.INTERNAL_DOMAIN_NOT_EXIST);
        }
        return clusterSystemDTO.getInternalDomain();
    }
}
