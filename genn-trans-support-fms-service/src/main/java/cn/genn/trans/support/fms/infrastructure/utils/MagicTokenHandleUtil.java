package cn.genn.trans.support.fms.infrastructure.utils;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.dto.MagicTokenDTO;
import cn.hutool.core.net.URLEncodeUtil;

/**
 * @Date: 2024/8/21
 * @Author: kangjian
 */
public class MagicTokenHandleUtil {

    public static String handleMagicToken(String tokenData) {
        if (tokenData == null) {
            return null;
        }
        MagicTokenDTO magicTokenDTO = JsonUtils.parse(tokenData, MagicTokenDTO.class);
        // 对象序列化成json 字符串需要UrlEncode
        tokenData = JsonUtils.toJson(magicTokenDTO);
        return URLEncodeUtil.encode(tokenData);
    }
}
