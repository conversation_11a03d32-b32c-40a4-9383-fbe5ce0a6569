package cn.genn.trans.support.fms.infrastructure.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class PathBuilder {
    public static String buildPath(Object obj, Field field) throws NoSuchFieldException {
        String prefix = "";
        Field currentField = field;
        while (currentField.getDeclaringClass()!= obj.getClass()) {
            prefix = currentField.getName() + "." + prefix;
            currentField = currentField.getDeclaringClass().getDeclaredField(currentField.getName());
        }
        DataBeanPath annotation = field.getAnnotation(DataBeanPath.class);
        if (annotation!= null) {
            return prefix + annotation.beanPath();
        }
        return null;
    }
}
