package cn.genn.trans.support.fms.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.support.fms.application.service.action.FileExportTaskActionService;
import cn.genn.trans.support.fms.application.service.query.FileExportTaskQueryService;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitClientCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.command.SingleIdCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileExportTaskDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.query.FileExportTaskQuery;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 文件导出支持相关接口
 *
 * @Date: 2024/5/27
 * @Author: kangjian
 */
@Api(tags = "文件导出支持")
@RestController
@RequestMapping("/export")
@Slf4j
public class FileExportController {
    @Resource
    private FileExportTaskQueryService queryService;
    @Resource
    private FileExportTaskActionService actionService;

    /**
     * 提交导出任务
     *
     * @param command
     * @return
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交导出任务")
    public FileExportTaskDTO submit(@ApiParam(value = "任务参数") @RequestBody FileExportTaskSubmitCommand command) {
        return actionService.submit(command);
    }

    /**
     * 提交导出任务 RPC调用 无登录态
     *
     * @param command
     * @return
     */
    @PostMapping("/submitForClient")
    @ApiOperation(value = "提交导出任务 RPC调用 无登录态")
    public FileExportTaskDTO submitForClient(@ApiParam(value = "任务参数") @RequestBody FileExportTaskSubmitClientCommand command) {
        SsoUserAuthInfoDTO ssoUserAuthInfoDTO = new SsoUserAuthInfoDTO();
        ssoUserAuthInfoDTO.setSystemId(command.getSystemId());
        ssoUserAuthInfoDTO.setTenantId(command.getTenantId());
        ssoUserAuthInfoDTO.setOperatorId(command.getOperatorId());
        ssoUserAuthInfoDTO.setCarrierId(command.getCarrierId());
        ssoUserAuthInfoDTO.setDriverId(command.getDriverId());
        ssoUserAuthInfoDTO.setUserId(command.getUserId());
        ssoUserAuthInfoDTO.setUsername(command.getUsername());
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(ssoUserAuthInfoDTO);
        FileExportTaskSubmitCommand commandNew = new FileExportTaskSubmitCommand();
        BeanUtils.copyProperties(command, commandNew);
        commandNew.setBusinessCode(ExportTaskBusinessCodeEnum.of(command.getBusinessCode()));
        return actionService.submit(commandNew);
    }

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<FileExportTaskDTO> page(@ApiParam(value = "查询类") @RequestBody FileExportTaskQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public FileExportTaskDTO get(@Valid @RequestBody SingleIdCommand command) {
        return queryService.get(command.getId());
    }

    @PostMapping("/retry")
    @ApiOperation(value = "重试")
    public void retry(@Valid @RequestBody SingleIdCommand command) {
        actionService.retry(command.getId());
    }

    @PostMapping("/cancel")
    @ApiOperation(value = "取消")
    public void cancel(@Valid @RequestBody SingleIdCommand command) {
        actionService.cancel(command.getId());
    }
}
