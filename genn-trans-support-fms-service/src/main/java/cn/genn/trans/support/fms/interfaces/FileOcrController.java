package cn.genn.trans.support.fms.interfaces;

import cn.genn.trans.support.fms.application.service.action.FileOcrService;
import cn.genn.trans.support.fms.interfaces.dto.FileOcrResult;
import cn.genn.trans.support.fms.interfaces.enums.FileBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.query.FileOcrRecognizeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 文件OCR识别业务支持
 *
 * @Date: 2024/5/29
 * @Author: kangjian
 */
@Api(tags = "文件OCR识别支持")
@RestController
@RequestMapping("/ocr")
@Slf4j
public class FileOcrController {

    @Resource
    private FileOcrService fileOcrService;

    /**
     * 营业执照识别
     *
     * @param fileKey
     * @return
     */
    @PostMapping("/businessLicense")
    @ApiOperation(value = "营业执照识别")
    public FileOcrResult ocrOfBusinessLicense(@RequestParam(value = "fileKey") String fileKey) {
        return fileOcrService.ocrRecognize(fileKey, FileBusinessCodeEnum.BUSINESS_LICENSE);
    }

    @PostMapping("/recognize")
    @ApiOperation(value = "ocr识别")
    public FileOcrResult ocrRecognize(@RequestBody FileOcrRecognizeQuery query) {
        return fileOcrService.ocrRecognize(query.getFileKey(), query.getBusinessCode());
    }
}
