package cn.genn.trans.support.fms.interfaces;

import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.infrastructure.utils.AsyncTaskExecutor;
import cn.genn.trans.support.fms.interfaces.command.*;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.TosTempStsConfigDTO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.web.spring.annotation.LogCost;
import cn.genn.web.spring.annotation.ResponseResultWrapper;
import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 文件上传下载操作支持
 */
@Api(tags = "文件上传和获取")
@RestController
@RequestMapping("/support")
@Slf4j
public class FileSupportController {

    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;


    /**
     * 上传文件到指定存储平台，成功返回文件信息
     */
    @PostMapping(value = "/upload")
    public FileInfoDTO upload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "businessCode") String businessCode,
                              @RequestParam(value = "objectId", required = false) String objectId,
                              @RequestParam(value = "objectType", required = false) String objectType) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        return fileUploadSupportActionService.uploadFile(file, ssoUserAuthInfo, businessCode, objectId, objectType);
    }

    /**
     * 小程序使用的上传文件接口 由入参指定租户id
     */
    @PostMapping(value = "/miniUpload")
    public FileInfoDTO miniUpload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "businessCode") String businessCode,
                                  @RequestParam(value = "objectId", required = false) String objectId,
                                  @RequestParam(value = "objectType", required = false) String objectType,
                                  @RequestParam(value = "tenantId", required = false) Long tenantId) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        ssoUserAuthInfo.setTenantId(tenantId);
        return fileUploadSupportActionService.uploadFile(file, ssoUserAuthInfo, businessCode, objectId, objectType);
    }

    /**
     * app上传文件接口
     */
    @PostMapping(value = "/appUpload")
    public FileInfoDTO appUpload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "fileName") String fileName,
                                 @RequestParam(value = "businessCode") String businessCode,
                                 @RequestParam(value = "domainName") String domainName,
                                 @RequestParam(value = "objectId", required = false) String objectId,
                                 @RequestParam(value = "objectType", required = false) String objectType,
                                 @RequestParam(value = "tenantId") Long tenantId) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        ssoUserAuthInfo.setTenantId(tenantId);
        return fileUploadSupportActionService.uploadFile(file, fileName, domainName, ssoUserAuthInfo, businessCode, objectId, objectType);
    }


    /**
     * 小程序使用的上传文件接口 由入参指定租户id
     */
    @PostMapping(value = "/uploadWithoutLogin")
    public FileInfoDTO uploadWithoutLogin(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "businessCode") String businessCode,
                                          @RequestParam(value = "objectId", required = false) String objectId,
                                          @RequestParam(value = "objectType", required = false) String objectType,
                                          @RequestParam(value = "tenantId") Long tenantId,
                                          @RequestParam(value = "systemId") Long systemId,
                                          @RequestParam(value = "userId", required = false) Long userId,
                                          @RequestParam(value = "userName", required = false) String userName) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(userId)
            .username(userName)
            .tenantId(tenantId)
            .systemId(systemId)
            .build();
        return fileUploadSupportActionService.uploadFile(file, ssoUserAuthInfo, businessCode, objectId, objectType);
    }

    @PostMapping("/getFileInfoByKeyOfMini")
    public FileInfoDTO getFileInfoByKeyOfMini(@Valid @RequestBody FileQueryOfMiniCommand command) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        ssoUserAuthInfo.setTenantId(command.getTenantId());
        return fileUploadSupportActionService.getFileInfoByFileKey(command.getFileKey(), ssoUserAuthInfo);
    }

    @PostMapping("/getFileInfoByKey")
    public FileInfoDTO getFileInfoByKey(@RequestParam(value = "fileKey") String fileKey) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        return fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);
    }

    @PostMapping("/getFileInfoByKeyWithoutLogin")
    public FileInfoDTO getFileInfoByKeyWithoutLogin(@RequestBody FileDetailQuerySingleCommand command) {
        return fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(command);
    }

    @PostMapping("/getFileInfoByKeys")
    public List<FileInfoDTO> getFileInfoByKeys(@RequestBody FileDetailQueryCommand command) {
        List<String> fileKeys = command.getFileKeyList();
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (CollectionUtil.isEmpty(fileKeys)) {
            return Lists.newArrayList();
        }
        ExecutorService executorService = AsyncTaskExecutor.getExecutorService();
        List<Future<FileInfoDTO>> futures = new ArrayList<>();

        for (String fileKey : fileKeys) {
            Future<FileInfoDTO> future = executorService.submit(() -> {
                try {
                    // 假设getFileInfoByFileKey已经足够线程安全或者其内部进行了必要的同步
                    return fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);
                } catch (Exception e) {
                    // 在这里处理异常，例如记录日志等
                    // 注意：如果异常需要被外部处理，可以考虑抛出自定义异常或者使用其他机制通知调用者
                    return null;
                }
            });
            futures.add(future);
        }

        // 使用CompletableFuture来等待所有任务完成并合并结果
        CompletableFuture<FileInfoDTO>[] cfs = futures.stream()
            .map(future -> CompletableFuture.completedFuture(future))
            .toArray(CompletableFuture[]::new);

        return CompletableFuture
            .allOf(cfs)
            .thenApply(v -> {
                List<FileInfoDTO> result = new ArrayList<>();
                for (Future<FileInfoDTO> future : futures) {
                    try {
                        FileInfoDTO fileInfoDTO = future.get();
                        if (Objects.nonNull(fileInfoDTO)) {
                            result.add(fileInfoDTO);
                        }

                    } catch (Exception e) {
                        // 异常处理逻辑，例如记录日志
                    }
                }
                return result;
            }).join();
    }

    @GetMapping("/downloadFileByKey")
    @SneakyThrows
    public ResponseEntity<InputStreamResource> downloadFileByKey(@RequestParam(value = "fileKey") String fileKey) {
        // 假设这里有逻辑来获取文件的字节内容
        byte[] fileContent = {/* 文件的字节内容 */};
        ByteArrayInputStream bis = new ByteArrayInputStream(fileContent);
        InputStreamResource isr = new InputStreamResource(bis);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment; filename=example.txt");

        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(isr);
    }

    @PostMapping("/uploadBase64")
    public FileInfoDTO uploadBase64(@Valid @RequestBody FileUploadImageOfBase64Command command) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(command.getUserId())
                .username(command.getUsername())
                .tenantId(command.getTenantId())
                .systemId(command.getSystemId())
                .build();
            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(ssoUserAuthInfo);
        }
        return fileUploadSupportActionService.handleImageOfBase64(ssoUserAuthInfo, command);
    }

    /**
     * 外链转存服务
     */
    @PostMapping(value = "/externalLinkFileTransfer")
    public FileInfoDTO externalLinkFileTransfer(@Valid @RequestBody FileExternalLinkTransferCommand command) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(command.getUserId())
            .username(command.getUsername())
            .tenantId(command.getTenantId())
            .systemId(command.getSystemId())
            .build();
        return fileUploadSupportActionService.externalLinkFileTransfer(ssoUserAuthInfo, command);
    }

    /**
     * 上传word文件 转换为pdf
     */
    @PostMapping(value = "/uploadWordTransferPdf")
    public FileInfoDTO uploadWordTransferPdf(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "businessCode") String businessCode,
                                             @RequestParam(value = "objectId", required = false) String objectId,
                                             @RequestParam(value = "objectType", required = false) String objectType) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        return fileUploadSupportActionService.uploadWordTransferPdf(file, ssoUserAuthInfo, businessCode, objectId, objectType);
    }

    @ApiOperation("实时获取图片")
    @GetMapping("/image/{key}")
    @ResponseResultWrapper(ignore = true)
    @LogCost(result = false)
    public ResponseEntity<org.springframework.core.io.InputStreamResource> getImageByKey(@PathVariable("key") String fileKey) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = new SsoUserAuthInfoDTO();
        ssoUserAuthInfo.setTenantId(1L);
        ssoUserAuthInfo.setSystemId(2L);
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);
        // 根据fileKey获取图片文件的输入流
        FileInputStream fileInputStream = fileUploadSupportActionService.downloadFile(fileInfoDTO.getUrl(), fileInfoDTO.getExt());

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileKey);
        headers.setContentType(MediaType.IMAGE_JPEG); // 根据实际情况设置MIME类型

        // 返回图片数据
        return ResponseEntity.ok()
            .headers(headers)
            .body(new org.springframework.core.io.InputStreamResource(fileInputStream));

    }

    @ApiOperation("实时下载文件")
    @GetMapping("/file/{key}")
    @ResponseResultWrapper(ignore = true)
    @LogCost(result = false)
    public ResponseEntity<org.springframework.core.io.InputStreamResource> getFileByKey(@PathVariable("key") String fileKey) throws IOException {
        SsoUserAuthInfoDTO ssoUserAuthInfo = new SsoUserAuthInfoDTO();
        ssoUserAuthInfo.setTenantId(1L);
        ssoUserAuthInfo.setSystemId(2L);
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.getFileInfoByFileKey(fileKey, ssoUserAuthInfo);

        if (fileInfoDTO == null || fileInfoDTO.getUrl() == null) {
            return ResponseEntity.notFound().build();
        }

        URL url = new URL(fileInfoDTO.getUrl());
        InputStream inputStream = url.openStream();
        InputStreamResource resource = new InputStreamResource(inputStream);

        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileInfoDTO.getFilename() + "\"")
            .body(resource);
    }

    @ApiOperation("获取火山云TOS的STS临时配置")
    @PostMapping("/getTosTempStsConfig")
    public TosTempStsConfigDTO getTosStsConfig(@Valid @RequestBody TosTempStsConfigCreateCommand command) {
        return fileUploadSupportActionService.getTosStsConfig(command);
    }

}
