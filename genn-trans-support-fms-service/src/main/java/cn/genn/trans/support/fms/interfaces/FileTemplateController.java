package cn.genn.trans.support.fms.interfaces;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.support.fms.application.service.action.FileTemplateActionService;
import cn.genn.trans.support.fms.infrastructure.config.FileBusinessConfigProperties;
import cn.genn.trans.support.fms.infrastructure.exception.MessageCode;
import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 文件模板处理
 *
 * @Date: 2024/5/22
 * @Author: kangjian
 */
@Api(tags = "文件模板操作")
@RestController
@RequestMapping("/template")
@Slf4j
@RefreshScope
public class FileTemplateController {

    @Resource
    private FileTemplateActionService fileTemplateActionService;

    private FileBusinessConfigProperties fileBusinessConfigProperties;

    public FileTemplateController(FileBusinessConfigProperties fileBusinessConfigProperties) {
        this.fileBusinessConfigProperties = fileBusinessConfigProperties;
    }

    /**
     * 下载文件模板 可传值替换文档的占位符
     * 2024.12.24 增加了特定填充固定选项的支持
     *
     * @return
     */
    @PostMapping(value = "/simple")
    public FileInfoDTO simple(@RequestBody FileTemplateCreateCommand command) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(1L)
                .username("admin")
                .tenantId(1L)
                .systemId(1L)
                .telephone("")
                .build();
        }
        String fileKey = fileBusinessConfigProperties.getTemplate()
            .getConfig()
            .get(command.getBusinessCode());
        if (Objects.isNull(fileKey)) {
            throw new BusinessException(MessageCode.BUSINESS_CONFIG_NOT_EXIST);
        }
        return fileTemplateActionService.generateFileTemplateOfSimpleParam(ssoUserAuthInfo, fileKey, command.getSimpleParams(), command.getCellRangeAddressParam());
    }
    /**
     * TODO 带集合的占位符的支持
     */



    /**
     * 图片的模板替换
     *
     * @return
     */
    @PostMapping(value = "/pictureTemplate")
    public FileInfoDTO pictureTemplate(@RequestBody FileTemplateCreateCommand command) {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(command.getUserId())
                .username(command.getUserName())
                .tenantId(command.getTenantId())
                .systemId(command.getSystemId())
                .build();
        }
        return fileTemplateActionService.generateFileTemplateOfPictureTemplate(ssoUserAuthInfo, command.getBusinessCode(), command.getFileName(), command.getSimpleParams());
    }

}
