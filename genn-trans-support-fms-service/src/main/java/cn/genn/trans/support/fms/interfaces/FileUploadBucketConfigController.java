package cn.genn.trans.support.fms.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.support.fms.application.service.query.FileUploadBucketConfigQueryService;
import cn.genn.trans.support.fms.interfaces.command.FileUploadBucketConfigInitCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBucketConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBucketConfigQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "文件上传租户bucket配置")
@RestController
@RequestMapping("/fileUploadBucketConfig")
public class FileUploadBucketConfigController {

    @Resource
    private FileUploadBucketConfigQueryService queryService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<FileUploadBucketConfigDTO> page(@ApiParam(value = "查询类") @RequestBody FileUploadBucketConfigQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public FileUploadBucketConfigDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }

    @PostMapping("/getSelectInitConfig")
    @ApiOperation(value = "查询可供选择的初始化配置")
    public List<FileUploadBucketConfigDTO> getSelectInitConfig() {
        return queryService.getSelectInitConfig();
    }

    @PostMapping("/initConfig")
    @ApiOperation(value = "初始化bucket配置")
    public Boolean initConfig(@RequestBody FileUploadBucketConfigInitCommand command) {
        return queryService.initConfig(command);
    }
}

