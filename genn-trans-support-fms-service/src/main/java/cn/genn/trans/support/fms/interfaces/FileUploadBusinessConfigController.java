package cn.genn.trans.support.fms.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.support.fms.application.service.query.FileUploadBusinessConfigQueryService;
import cn.genn.trans.support.fms.interfaces.dto.FileUploadBusinessConfigDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadBusinessConfigQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@Api(tags = "文件上传租户业务配置")
@RestController
@RequestMapping("/fileUploadBusinessConfig")
public class FileUploadBusinessConfigController {

    @Resource
    private FileUploadBusinessConfigQueryService queryService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<FileUploadBusinessConfigDTO> page(@ApiParam(value = "查询类") @RequestBody FileUploadBusinessConfigQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public FileUploadBusinessConfigDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }
}

