package cn.genn.trans.support.fms.interfaces;

import cn.genn.trans.support.fms.interfaces.dto.FileUploadDetailDTO;
import cn.genn.trans.support.fms.interfaces.query.FileUploadDetailQuery;
import cn.genn.trans.support.fms.application.service.query.FileUploadDetailQueryService;
import cn.genn.trans.support.fms.application.service.action.FileUploadDetailSupportActionService;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@Api(tags = "文件上传记录表")
@RestController
@RequestMapping("/fileUploadDetail")
public class FileUploadDetailController {

    @Resource
    private FileUploadDetailQueryService queryService;
    @Resource
    private FileUploadDetailSupportActionService actionService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<FileUploadDetailDTO> page(@ApiParam(value = "查询类") @RequestBody FileUploadDetailQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public FileUploadDetailDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }
}

