package cn.genn.trans.support.fms.interfaces;

import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.ResUtils;
import cn.genn.trans.support.fms.application.command.Md2HtmlCommand;
import cn.genn.trans.support.fms.application.command.UploadHtmlCommand;
import cn.genn.trans.support.fms.application.service.ToolSupportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "工具支持")
@RestController
@RequestMapping("/tool")
@Slf4j
@RequiredArgsConstructor
public class ToolSupportController {

    private final ToolSupportService service;

    /**
     * md转html
     *
     * @param command
     * @return html链接, 对象存储可访问的链接
     */
    @PostMapping("/md2html")
    @ApiOperation(value = "md转html")
    public ResponseResult<String> md2html(@Validated @RequestBody Md2HtmlCommand command) {
        return ResUtils.ok(service.md2html(command));
    }

    /**
     * 上传html
     *
     * @param command
     * @return html链接, 对象存储可访问的链接
     */
    @PostMapping("/uploadHtml")
    @ApiOperation(value = "上传html")
    public ResponseResult<String> uploadHtml(@Validated @RequestBody UploadHtmlCommand command) {
        return ResUtils.ok(service.uploadHtml(command));
    }
}
