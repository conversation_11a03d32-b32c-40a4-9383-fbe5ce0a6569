package cn.genn.trans.support.fms.spring;

import cn.genn.trans.support.fms.core.FileStorageService;
import cn.genn.trans.support.fms.core.FileStorageServiceBuilder;
import cn.genn.trans.support.fms.core.aspect.FileStorageAspect;
import cn.genn.trans.support.fms.core.file.FileWrapperAdapter;
import cn.genn.trans.support.fms.core.platform.FileStorage;
import cn.genn.trans.support.fms.core.platform.FileStorageClientFactory;
import cn.genn.trans.support.fms.core.recorder.DefaultFileRecorder;
import cn.genn.trans.support.fms.core.recorder.FileRecorder;
import cn.genn.trans.support.fms.core.tika.ContentTypeDetect;
import cn.genn.trans.support.fms.core.tika.DefaultTikaFactory;
import cn.genn.trans.support.fms.core.tika.TikaContentTypeDetect;
import cn.genn.trans.support.fms.core.tika.TikaFactory;
import cn.genn.trans.support.fms.spring.SpringFileStorageProperties.SpringLocalConfig;
import cn.genn.trans.support.fms.spring.SpringFileStorageProperties.SpringLocalPlusConfig;
import cn.genn.trans.support.fms.spring.file.MultipartFileWrapperAdapter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

import static cn.genn.trans.support.fms.core.FileStorageServiceBuilder.doesNotExistClass;

@Slf4j
@Configuration
@ConditionalOnMissingBean(FileStorageService.class)
@Component
@DependsOn("springFileStorageProperties")
public class FileStorageAutoConfiguration {

    @Autowired
    private SpringFileStorageProperties properties;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 配置本地存储的访问地址
     */
    @Bean
    @ConditionalOnClass(name = "org.springframework.web.servlet.config.annotation.WebMvcConfigurer")
    public Object fileStorageWebMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
                for (SpringLocalConfig local : properties.getLocal()) {
                    if (local.getEnableStorage() && local.getEnableAccess()) {
                        registry.addResourceHandler(local.getPathPatterns())
                                .addResourceLocations("file:" + local.getBasePath());
                    }
                }
                for (SpringLocalPlusConfig local : properties.getLocalPlus()) {
                    if (local.getEnableStorage() && local.getEnableAccess()) {
                        registry.addResourceHandler(local.getPathPatterns())
                                .addResourceLocations("file:" + local.getStoragePath());
                    }
                }
            }
        };
    }

    /**
     * 当没有找到 FileRecorder 时使用默认的 FileRecorder
     */
    @Bean
    @ConditionalOnMissingBean(FileRecorder.class)
    public FileRecorder fileRecorder() {
        log.warn("没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！");
        return new DefaultFileRecorder();
    }

    /**
     * Tika 工厂类型，用于识别上传的文件的 MINE
     */
    @Bean
    @ConditionalOnMissingBean(TikaFactory.class)
    public TikaFactory tikaFactory() {
        return new DefaultTikaFactory();
    }

    /**
     * 识别文件的 MIME 类型
     */
    @Bean
    @ConditionalOnMissingBean(ContentTypeDetect.class)
    public ContentTypeDetect contentTypeDetect(TikaFactory tikaFactory) {
        return new TikaContentTypeDetect(tikaFactory);
    }

    /**
     * 文件存储服务
     */
    @Bean(destroyMethod = "destroy")
    public FileStorageService fileStorageService(
            FileRecorder fileRecorder,
            @Autowired(required = false) List<List<? extends FileStorage>> fileStorageLists,
            @Autowired(required = false) List<FileStorageAspect> aspectList,
            @Autowired(required = false) List<FileWrapperAdapter> fileWrapperAdapterList,
            ContentTypeDetect contentTypeDetect,
            @Autowired(required = false) List<List<FileStorageClientFactory<?>>> clientFactoryList) {

        if (fileStorageLists == null) fileStorageLists = new ArrayList<>();
        if (aspectList == null) aspectList = new ArrayList<>();
        if (fileWrapperAdapterList == null) fileWrapperAdapterList = new ArrayList<>();
        if (clientFactoryList == null) clientFactoryList = new ArrayList<>();

        FileStorageServiceBuilder builder = FileStorageServiceBuilder.create(properties.toFileStorageProperties())
                .setFileRecorder(fileRecorder)
                .setAspectList(aspectList)
                .setContentTypeDetect(contentTypeDetect)
                .setFileWrapperAdapterList(fileWrapperAdapterList)
                .setClientFactoryList(clientFactoryList);

        fileStorageLists.forEach(builder::addFileStorage);

        if (properties.getEnableByteFileWrapper()) {
            builder.addByteFileWrapperAdapter();
        }
        if (properties.getEnableUriFileWrapper()) {
            builder.addUriFileWrapperAdapter();
        }
        if (properties.getEnableInputStreamFileWrapper()) {
            builder.addInputStreamFileWrapperAdapter();
        }
        if (properties.getEnableLocalFileWrapper()) {
            builder.addLocalFileWrapperAdapter();
        }
        if (properties.getEnableHttpServletRequestFileWrapper()) {
            if (doesNotExistClass("javax.servlet.http.HttpServletRequest")
                    && doesNotExistClass("jakarta.servlet.http.HttpServletRequest")) {
                log.warn(
                        "当前未检测到 Servlet 环境，无法加载 HttpServletRequest 的文件包装适配器，请将参数【dromara.x-file-storage.enable-http-servlet-request-file-wrapper】设置为 【false】来消除此警告");
            } else {
                builder.addHttpServletRequestFileWrapperAdapter();
            }
        }
        if (properties.getEnableMultipartFileWrapper()) {
            if (doesNotExistClass("org.springframework.web.multipart.MultipartFile")) {
                log.warn(
                        "当前未检测到 SpringWeb 环境，无法加载 MultipartFile 的文件包装适配器，请将参数【dromara.x-file-storage.enable-multipart-file-wrapper】设置为 【false】来消除此警告");
            } else {
                builder.addFileWrapperAdapter(new MultipartFileWrapperAdapter());
            }
        }

        if (doesNotExistClass("org.springframework.web.servlet.config.annotation.WebMvcConfigurer")) {
            long localAccessNum = properties.getLocal().stream()
                    .filter(SpringLocalConfig::getEnableStorage)
                    .filter(SpringLocalConfig::getEnableAccess)
                    .count();
            long localPlusAccessNum = properties.getLocalPlus().stream()
                    .filter(SpringLocalPlusConfig::getEnableStorage)
                    .filter(SpringLocalPlusConfig::getEnableAccess)
                    .count();

            if (localAccessNum + localPlusAccessNum > 0) {
                log.warn("当前未检测到 SpringWeb 环境，无法开启本地存储平台的本地访问功能，请将关闭本地访问来消除此警告");
            }
        }

        return builder.build();
    }

    /**
     * 对 FileStorageService 注入自己的代理对象，不然会导致针对 FileStorageService 的代理方法不生效
     */
    @EventListener(ContextRefreshedEvent.class)
    public void onContextRefreshedEvent() {
        FileStorageService service = applicationContext.getBean(FileStorageService.class);
        service.setSelf(service);
    }
}
