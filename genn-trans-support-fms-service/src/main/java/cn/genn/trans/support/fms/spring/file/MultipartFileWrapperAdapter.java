package cn.genn.trans.support.fms.spring.file;

import lombok.Getter;
import lombok.Setter;
import cn.genn.trans.support.fms.core.file.FileWrapper;
import cn.genn.trans.support.fms.core.file.FileWrapperAdapter;
import cn.genn.trans.support.fms.spring.file.MultipartFileWrapper;
import org.springframework.web.multipart.MultipartFile;

/**
 * MultipartFile 文件包装适配器
 */
@Getter
@Setter
public class MultipartFileWrapperAdapter implements FileWrapperAdapter {

    @Override
    public boolean isSupport(Object source) {
        return source instanceof MultipartFile || source instanceof MultipartFileWrapper;
    }

    @Override
    public FileWrapper getFileWrapper(Object source, String name, String contentType, Long size) {
        if (source instanceof MultipartFileWrapper) {
            return updateFileWrapper((MultipartFileWrapper) source, name, contentType, size);
        } else {
            MultipartFile file = (MultipartFile) source;
            if (name == null) name = file.getOriginalFilename();
            if (contentType == null) contentType = file.getContentType();
            if (size == null) size = file.getSize();
            return new MultipartFileWrapper(file, name, contentType, size);
        }
    }
}
