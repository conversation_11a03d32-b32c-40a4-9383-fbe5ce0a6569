package cn.genn.trans.support.fms.application.service.script

import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO

import java.text.SimpleDateFormat

class GenerateString {
    // 接收一个User对象作为参数
    String generateWaterMaker(SsoUserAuthInfoDTO user) {
        def sdf = new SimpleDateFormat('yyyy-MM-dd HH:mm:ss')
        def timestamp = sdf.format(new Date())
        // 提取用户名和手机尾号\n" +
        def username = user.username
        def phoneTail = user.telephone[-4..-1]
        // 组合字符串\n" +
        "$timestamp $username $phoneTail"
    }
    /*String scriptText =
           "import java.text.SimpleDateFormat\n" +
               "// 使用SimpleDateFormat获取当前时间的字符串表示\n" +
               "def sdf = new SimpleDateFormat('yyyy-MM-dd HH:mm:ss')\n" +
               "def timestamp = sdf.format(new Date())\n" +
               "// 提取用户名和手机尾号\n" +
               "def username = user.username\n" +
               "def phoneTail = user.telephone[-4..-1]\n" +
               "// 组合字符串\n" +
               "\"$timestamp $username $phoneTail\"";*/
//        String scriptText = "def result = generateWaterMaker(user) \n result";
    // 执行Groovy脚本并获取结果
//        Object result = shell.evaluate(scriptText,"GenerateString.groovy");
}
