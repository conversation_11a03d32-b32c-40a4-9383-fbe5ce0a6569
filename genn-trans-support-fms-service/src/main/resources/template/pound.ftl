<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8"/>
    <title>磅单</title>
    <style>
        html {
            font-size: 16px;
            background: #005e29;
            /* 可以根据需要调整 */
        }

        .container {
            width: 32rem;
            height: 100%;
            /* 10px */
            padding: 20px;
            /* 20px */
            box-sizing: border-box;
            background: #005e29;
        }

        .watermark {
            width: 5rem;
            /* 64px */
            height: 5rem;
            /* 64px */
            position: absolute;
            top: 0.5px;
            right: 0.5px;
        }

        .content-wrap {
            border-radius: 0.625rem;
            position: relative;
            background: #fff;
            height: 90%;
            width: 90%;
            padding: 20px;
            /*box-sizing: border-box;*/
        }

        .content {
            height: 90%;
            width: 95%;
        }

        .content-item {
            align-items: center;
            margin-bottom: 1rem;
        }

        .flex {
            /*display: flex;*/
            /*padding: 10px;*/
            /*width: 40%;*/
            padding: 1px;
        }

        .key {
            width: 100px;
            /* 85px */
            font-size: 15px;
            /* 14px */
            color: #5A6B8F;
            margin-right: 2rem;
            /* 32px */
        }

        .value {
            color: #060B2D;
            font-size: 0.875rem;
            /* 14px */
            /*padding: 10px;*/
        }

        .num {
            font-weight: 700;
            font-size: 0.875rem;
            /* 14px */
            color: #0066FF;
            /*padding: 10px;*/
        }
    </style>
</head>

<body>
<div class="container">
    <div class="content-wrap">
        <#if iconUrl?? && iconUrl?has_content>
            <img src="${iconUrl}"
                 alt=""
                 class="watermark"/>
        </#if>
        <table class="content">
            <tr class="content-item flex">
                <td class="key">运单号</td>
                <td class="value">${orderNo!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">业务类型</td>
                <td class="value">${poundType!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">磅单来源</td>
                <td class="value">${picSource!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">磅单号</td>
                <td class="value">${poundNo!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">毛重(吨)</td>
                <td class="value num">${grossWeight!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">皮重(吨)</td>
                <td class="value num">${tareWeight!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">净重(吨)</td>
                <td class="value num">${netWeight!""}</td>
            </tr>
            <#if title == "卸车磅单">
                <tr class="content-item flex">
                    <td class="key">扣罚(吨)</td>
                    <td class="value num">${deductWeight!"0"}</td>
                </tr>
            </#if>
            <tr class="content-item flex">
                <td class="key">车辆</td>
                <td class="value">${vehicle!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">司机</td>
                <td class="value">${driverName!""} (${driverPhone!""})</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">首次过磅时间</td>
                <td class="value">${oneWeightTime!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">完成过磅时间</td>
                <td class="value">${twoWeightTime!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">货物信息</td>
                <td class="value">${cargo!""}</td>
            </tr>
            <!-- 根据 title 的值动态替换 -->
            <#if title == "提箱磅单">
                <tr class="content-item flex">
                    <td class="key">站台简称</td>
                    <div class="value">${locationSubName!""}</div>
                </tr>
            <#else>
                <tr class="content-item flex">
                    <td class="key">装车简称</td>
                    <div class="value">${loadsAddrShort!""}</div>
                </tr>
                <tr class="content-item flex">
                    <td class="key">卸车简称</td>
                    <div class="value">${unloadAddrShort!""}</div>
                </tr>
            </#if>
        </table>
    </div>

</div>

</body>

</html>
