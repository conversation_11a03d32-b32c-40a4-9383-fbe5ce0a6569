<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8"/>
    <title>磅单</title>
    <style>
        html {
            font-size: 16px;
            background: #005e29;
            /* 可以根据需要调整 */
        }

        .container {
            width: 32rem;
            height: 100%;
            /* 10px */
            /*padding: 20px;*/
            /* 20px */
            box-sizing: border-box;
            background: #005e29;
        }

        .watermark {
            width: 5rem;
            /* 64px */
            height: 5rem;
            /* 64px */
            position: absolute;
            top: 0.5px;
            right: 0.5px;
        }

        .content-wrap {
            border-radius: 0.625rem;
            position: relative;
            background: #fff;
            height: 90%;
            width: 90%;
            padding: 20px;
            /*box-sizing: border-box;*/
        }

        .content {
            height: 90%;
            width: 95%;
        }

        .content-item {
            align-items: center;
            margin-bottom: 1rem;
        }

        .flex {
            /*display: flex;*/
            /*padding: 10px;*/
            /*width: 40%;*/
            padding: 1px;
        }

        .key {
            width: 100px;
            /* 85px */
            font-size: 15px;
            /* 14px */
            color: #5A6B8F;
            margin-right: 2rem;
            /* 32px */
        }

        .value {
            color: #060B2D;
            font-size: 0.875rem;
            /* 14px */
            /*padding: 10px;*/
        }

        .num {
            font-weight: 700;
            font-size: 0.875rem;
            /* 14px */
            color: #0066FF;
            /*padding: 10px;*/
        }
    </style>
</head>

<body>
<div class="container">
    <div class="content-wrap">
        <img src="https://applet-driver.obs.cn-north-4.myhuaweicloud.com/images/pfjh.png" alt="" class="watermark"/>
        <table class="content">
            <tr class="content-item flex">
                <td class="key">运单号</td>
                <td class="value">1231231${orderNo!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">业务类型</td>
                <td class="value">sadasdas${poundType!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">磅单来源</td>
                <td class="value">1123zxas${picSource!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">磅单号</td>
                <td class="value">asdas${poundNo!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">毛重(吨)</td>
                <td class="value num">${grossWeight!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">皮重(吨)</td>
                <td class="value num">353${tareWeight!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">净重(吨)</td>
                <td class="value num">123${netWeight!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">扣罚(吨)</td>
                <td class="value num">12312.2${deductWeight!"0"}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">货物体积</td>
                <td class="value num">${volume!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">车辆</td>
                <td class="value">${vehicle!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">司机</td>
                <td class="value">${driverName!""} ${driverPhone!""}</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">首次过磅时间</td>
                <td class="value">2024-09-20 14:11:49</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">完成过磅时间</td>
                <td class="value">2024年09月20日14:11:44</td>
            </tr>
            <tr class="content-item flex">
                <td class="key">货物信息</td>
                <td class="value">${cargo!""}</td>
            </tr>

        </table>
    </div>

</div>

</body>

</html>
