package cn.genn.trans.support.fms;

import cn.hutool.core.net.URLEncodeUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

@Slf4j
public class AliossUtil {
    /**
     * 目标图片大小，至于高度，等比例即可,ps:width在2000以上，已看不出区别
     */
    public static final int TARGET_WIDTH = 2500;

    public static void main(String[] args) throws IOException {
        Long a = System.currentTimeMillis();
        //        String url = "https://file-qx.siriushealth.net/afterSales/e271cbf8-795b-447d-a087-c9cb791c5694.jpg";
        String url = "http://admin.siriushealth.net/api/attachment/getIDCard?fileName=张飞_身份证.jpg";
        //        resizeImgToLocal(url);
        resizeImg(url);
        Long b = System.currentTimeMillis();
        System.err.println(b - a);
    }

    /**
     * 压缩图片，存到本地
     */
    public static InputStream resizeImg(String url) {
        try {
            url = urlEncode(url);
            URL img = new URL(url);
            BufferedImage image = ImageIO.read(img);

            double rate = image.getWidth() * 1.0 / image.getHeight();
            int targetHeight = (int) (TARGET_WIDTH * 1.0 / rate);
            //调整图片大小为 3000 X 等比高度 尺寸
            BufferedImage newImage = resizeImage(image, TARGET_WIDTH, targetHeight);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(newImage, "jpg", outputStream);//这里写jpg，5.2M的图片是900kb,若写png,图片是12M
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (IOException e) {
            log.error("===resizeImg错误，fileUrl=" + url, e);
        }
        return null;
    }

    /**
     * 压缩图片，存到本地
     */
    public static void resizeImgToLocal(String url) {
        try {
            url = urlEncode(url);
            //通过url获取BufferedImage图像缓冲区
            URL img = new URL(url);
            BufferedImage image = ImageIO.read(img);
            //获取图片的宽、高
            double rate = image.getWidth() * 1.0 / image.getHeight();
            int targetHeight = (int) (TARGET_WIDTH * 1.0 / rate);
            //调整图片大小为 400X400尺寸
            BufferedImage newImage = resizeImage(image, TARGET_WIDTH, targetHeight);

            System.out.println("old:w,h= " + image.getWidth() + "," + image.getHeight() + "\nnew:w,h=" + TARGET_WIDTH + "," + targetHeight);

            //将缓冲区图片保存到 F:/test/pic1.jpg (文件不存在会自动创建文件保存，文件存在会覆盖原文件保存)
            ImageIO.write(newImage, "jpg", new File("d:/a.png"));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 没这个方法的话, 入参是：http://admin.siriushealth.net/api/attachment/getIDCard?fileName=张飞_身份证.jpg
     * 其实能读取到的地址是  ：http://admin.siriushealth.net/api/attachment/getIDCard?fileName=%E5%BC%A0%E9%A3%9E_%E8%BA%AB%E4%BB%BD%E8%AF%81.jpg
     * 含问号的，不能把问号一起转义了
     * 需要注意的是，若fileName=other/张飞_身份证.jpg 怎是否需要将“/”转义，目前例子不够，不搞了
     */
    private static String urlEncode(String url) {
        Boolean contrinsChinese = containsChinese(url);
        if (url.contains("?") && contrinsChinese) {
            int charAt = url.indexOf("?");
            String urlFront = url.substring(0, charAt + 1);
            String urlBack = url.substring(charAt + 1);
            urlBack = URLEncodeUtil.encode(urlBack);
            url = urlFront + urlBack;
            url = url.replace("%2F", "/");
            url = url.replace("%3D", "=");// 把/和=转义回来
        } else if (contrinsChinese) {
            url = URLEncodeUtil.encode(url);
        }
        return url;
    }

    public static long getFileSize(String fileUrl) throws IOException {
        fileUrl = urlEncode(fileUrl);
        URL url = new URL(fileUrl);
        byte[] array = new byte[1024];
        InputStream inputStream = url.openStream();
        int size = 0;
        int length = 0;
        // fourth Exception --IOException
        while ((length = inputStream.read(array)) != -1) {
            size += length;
            //不必要完全获取文件大小，只要大于 Constants.minFileNoZip， 即，需要压缩
            //            if(size>= Constants.minFileNoZip){
            return size;
            //            }
        }

        return size; // 返回-1表示获取文件大小失败
    }

    public static boolean containsChinese(String input) {
        return input.chars().anyMatch(c -> Character.toString((char) c).matches("[\\u4e00-\\u9fa5]"));
    }

    /**
     * 通过BufferedImage图片流调整图片大小
     */
    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) throws IOException {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_AREA_AVERAGING);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, null);
        return outputImage;
    }

    /**
     * img的base64转图片
     */
    public static Image base64ToImage(String base64String) {
        byte[] imageBytes = Base64.getDecoder().decode(base64String);
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bis);
            bis.close();
            return image;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * img的base64转图片
     */
    public static Image base64ToImage2(String base64String) {
        byte[] imageBytes = Base64.getDecoder().decode(base64String);
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bis);

            //获取图片的宽、高
            //            double rate = image.getWidth()*1.0/image.getHeight();
            //            int targetHeight = (int) (TARGET_WIDTH*1.0/rate);
            //调整图片大小为 400X400尺寸
            //            BufferedImage newImage = resizeImage(image,TARGET_WIDTH,targetHeight);
            //
            //            System.out.println("old:w,h= " + image.getWidth()+","+image.getHeight()+"\nnew:w,h="+TARGET_WIDTH+","+targetHeight);

            //将缓冲区图片保存到 F:/test/pic1.jpg (文件不存在会自动创建文件保存，文件存在会覆盖原文件保存)
            ImageIO.write(image, "jpg", new File("./b.jpg"));


            bis.close();
            return image;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
}
