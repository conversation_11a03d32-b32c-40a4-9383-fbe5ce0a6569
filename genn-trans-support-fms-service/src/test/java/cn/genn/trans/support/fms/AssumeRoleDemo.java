package cn.genn.trans.support.fms;

import com.alibaba.fastjson.JSON;
import com.volcengine.model.request.AssumeRoleRequest;
import com.volcengine.model.response.AssumeRoleResponse;
import com.volcengine.service.sts.ISTSService;
import com.volcengine.service.sts.impl.STSServiceImpl;

public class AssumeRoleDemo {

    public static void main(String[] args) throws Exception {
        /**
         * genn-frontend
         * ak：AKLTZDA4OTQ5OTRiY2UxNDBkNzk2NDFkNjM0OTdmZWMzMjk
         * sk：Tm1OaU9UUTBPR1F3TURreU5HSTRNamt3TW1ZMlpHTXpaV0ZrTmpWa09HRQ==
         *
         * 角色TRN: trn:iam::2102045950:role/genn-frontend-STS
         */
        ISTSService stsService = STSServiceImpl.getInstance();

        stsService.setAccessKey("AKLTZDA4OTQ5OTRiY2UxNDBkNzk2NDFkNjM0OTdmZWMzMjk");
        stsService.setSecretKey("Tm1OaU9UUTBPR1F3TURreU5HSTRNamt3TW1ZMlpHTXpaV0ZrTmpWa09HRQ==");

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleSessionName("test");
        request.setDurationSeconds(360000);
        request.setRoleTrn("trn:iam::2102045950:role/genn-frontend-STS");

        AssumeRoleResponse resp = stsService.assumeRole(request);
        System.out.println(JSON.toJSONString(resp));

    }

}
