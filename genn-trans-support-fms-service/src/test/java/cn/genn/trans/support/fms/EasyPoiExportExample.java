package cn.genn.trans.support.fms;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.List;

public class EasyPoiExportExample {

    public static void main(String[] args) {
        // 创建工作簿和表格
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 设置列标题
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("选择项");

        // 设置数据验证（下拉框）
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[]{"选项1", "选项2", "选项3"});
        CellRangeAddressList addressList = new CellRangeAddressList(1, 5000, 0, 0);
        DataValidation validation = helper.createValidation(constraint, addressList);
        sheet.addValidationData(validation);

        // 导出 Excel 文件（具体文件路径可修改）
        try (FileOutputStream fileOut = new FileOutputStream("output.xlsx")) {
            workbook.write(fileOut);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
