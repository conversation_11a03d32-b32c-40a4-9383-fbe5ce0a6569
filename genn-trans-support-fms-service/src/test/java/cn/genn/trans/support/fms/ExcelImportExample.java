package cn.genn.trans.support.fms;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.genn.core.utils.jackson.JsonUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelImportExample {

    public static void main(String[] args) throws Exception {
        // 准备Excel文件的输入流
        File fis = new File("/Users/<USER>/Downloads/批量导入实体卡号模版.xlsx");
        Workbook workbook = WorkbookFactory.create(fis);
        // 创建导入参数对象
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        params.setStartRows(0);

        // 通过工具类直接导入，不需要@Excel注解
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(fis, Map.class, params);
        // 在最后一列添加"导入结果"标题
        Sheet sheet = workbook.getSheetAt(0);
        Row titleRow = sheet.getRow(0);
        Cell titleCell = titleRow.createCell(titleRow.getLastCellNum());
        titleCell.setCellValue("导入结果");
        // 将每行数据转成带行号的Map,批量调用业务系统
        List<Map<String, Object>> batchData = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("rowIndex", 0);
        row1.put("cardNo", "1234567890");
        batchData.add(row1);
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> rowData = list.get(i);
            rowData.put("rowIndex", i); // 添加行号
            batchData.add(rowData);
            // 每100条数据批量处理一次
            if (batchData.size() >= 100 || i == list.size() - 1) {
                String jsonStr = JsonUtils.toJson(batchData);
                // 批量调用业务系统API
                List<Map<String, Object>> results = callBusinessSystemBatch(jsonStr);
                Row row = sheet.getRow(2 + params.getStartRows());
                Cell cell = row.createCell(row.getLastCellNum());
                cell.setCellValue("导入结果:success");
                // 根据行号匹配结果并写入Excel
                /*for (Map<String, Object> result : results) {
                    int rowIndex = (Integer)result.get("rowIndex");
                    Row row = sheet.getRow(rowIndex + params.getStartRows());
                    Cell cell = row.createCell(row.getLastCellNum());
//                    cell.setCellValue(result.get("result").toString());
                    cell.setCellValue(result.get("result").toString());
                }*/
                batchData.clear();
            }
        }
        // 保存Excel文件
        InputStream fileInputStream = toInputStream(workbook);
        // 写入到文件中
        try {
            FileUtils.copyInputStreamToFile(fileInputStream, new File("/Users/<USER>/Downloads/批量导入实体卡号模版1.xlsx"));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static List<Map<String, Object>> callBusinessSystemBatch(String jsonStr) {
        return new ArrayList<>();
    }

    protected static InputStream toInputStream(Workbook workbook) {
        InputStream in = null;
        ByteArrayOutputStream out = null;
        try {
            out = new ByteArrayOutputStream();
            workbook.write(out);
            byte[] bookByteAry = out.toByteArray();
            in = new ByteArrayInputStream(bookByteAry);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return in;
    }
}
