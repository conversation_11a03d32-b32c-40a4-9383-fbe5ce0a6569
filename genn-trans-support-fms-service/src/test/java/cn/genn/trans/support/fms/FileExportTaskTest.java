package cn.genn.trans.support.fms;


import cn.genn.trans.support.fms.application.service.action.FileTemplateActionService;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.file.InputStreamFileWrapper;
import cn.genn.trans.support.fms.interfaces.command.FileExportTaskSubmitCommand;
import cn.genn.trans.support.fms.interfaces.command.FileExternalLinkTransferCommand;
import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.command.FileUploadImageOfBase64Command;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.http.HttpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import cn.genn.trans.support.fms.application.service.action.FileExportTaskActionService;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static feign.Util.UTF_8;

/**
 * @Date: 2024/4/23
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class FileExportTaskTest {

    @Resource
    private FileExportTaskActionService fileExportTaskActionService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Resource
    private FileTemplateActionService fileTemplateActionService;

    public void setUser() {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            //            throw new AuthException(MessageCode.OPERATOR_USER_INFO_NOT_EXIST);
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(1L)
                .username("kangjian")
                .tenantId(3L)
                .systemId(1L)
                .telephone("***********")
                .build();
            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(ssoUserAuthInfo);
        }
    }

    @Test
    void initTask() throws InterruptedException {
        setUser();
        FileExportTaskSubmitCommand command = new FileExportTaskSubmitCommand();
        command.setBusinessCode(ExportTaskBusinessCodeEnum.RETAIL_COMMON_EXPORT);
        command.setSubBusinessCode("member-personal-export");
        ConfigGridDTO configGridDTO = new ConfigGridDTO();
        command.setTaskParam(configGridDTO);
        fileExportTaskActionService.submit(command);
        Thread.sleep(100000);
    }

    @Test
    void executeTask() throws InterruptedException {
        //        setUser();
        fileExportTaskActionService.executeTask(534L);
    }

    /**
     * 测试结算任务
     *
     * @throws InterruptedException
     */
    @Test
    void executeSifTask() throws InterruptedException {
        //        setUser();
//        fileExportTaskActionService.executeTask(53L);
//        fileExportTaskActionService.executeTask(61L);
//        fileExportTaskActionService.executeTask(62L);
//        fileExportTaskActionService.executeTask(63L);
//        fileExportTaskActionService.executeTask(113L);
//        fileExportTaskActionService.executeTask(128L);
//        fileExportTaskActionService.executeTask(1694L);
        fileExportTaskActionService.executeTask(1036L);
    }

    @Test
    void retry() throws InterruptedException {
        //        setUser();
        fileExportTaskActionService.retry(40L);
    }

    @Test
    void execute() throws InterruptedException {
        fileExportTaskActionService.execute();
        Thread.sleep(100000);
    }

    @Test
    void uploadBase64() {
        setUser();
        FileUploadImageOfBase64Command command = FileUploadImageOfBase64Command.builder()
            .businessCode("template-generate")
            .objectId("1")
            .objectType("DemandOrder")
            .originalFilename("test.jpg")
            .base64String("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")
            .build();
        fileUploadSupportActionService.handleImageOfBase64(CurrentUserHolder.getCurrentUser(), command);
    }


    @Test
    void testUrlStore() {
        String url = "http://***************:26834/fs/service/default/ufiles/6f76b6f9-a711-4523-817a-b1ddbedb21e0?isView=false";
        // 对url文件进行下载到临时目录
        long size = HttpUtil.downloadFile(url, FileUtil.file("/Users/<USER>/"));
        System.out.println("Download size: " + size);
        // 转存文件
        // 删除临时文件
    }

    @Test
    void externalLinkFileTransfer() {
        setUser();
        fileUploadSupportActionService.externalLinkFileTransfer(CurrentUserHolder.getCurrentUser(), FileExternalLinkTransferCommand.builder()
            .businessCode("transport-contract")
            .objectId("1")
            .objectType("demand-order")
            .tenantId(1L)
            .systemId(2L)
            .userId(1L)
            .username("admin")
            //            .url("http://***************:26834/fs/service/default/ufiles/6f76b6f9-a711-4523-817a-b1ddbedb21e0?isView=false")
            .url("https://huisuiyun.oss-cn-shanghai.aliyuncs.com/pro/permanent/default/20241012/70fe69ee-1bc2-4286-ab17-05afb599ccfb54120241012/北京至简能源有限公司-10000000000000043030-2024-10-12.pdf")
            .build());
    }

    public static void main(String[] args) {
        String fileURL = "http://***************:26834/fs/service/default/ufiles/6f76b6f9-a711-4523-817a-b1ddbedb21e0?isView=false"; // 示例URL
        String saveDir = "/Users/<USER>/Downloads"; // 保存文件的目录
        File saveFile = downloadFile(fileURL, saveDir);
        // 尝试从文件名中获取后缀名
        String fileName = saveFile.getName();
        int lastIndexOfDot = fileName.lastIndexOf('.');
        if (lastIndexOfDot > 0) {
            String extension = fileName.substring(lastIndexOfDot + 1);
            System.out.println("文件后缀名: " + extension);
        }
        System.out.println("文件已下载至: " + saveFile.getAbsolutePath());
    }

    public static File downloadFile(String fileURL, String saveDir) {
        File file = null;
        try {
            URL url = new URL(fileURL);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            // 设置请求方法
            httpURLConnection.setRequestMethod("GET");
            // 连接到服务器
            int responseCode = httpURLConnection.getResponseCode();
            // 检查响应码
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 获取文件大小
                String fileName = "";
                String disposition = httpURLConnection.getHeaderField("Content-Disposition");
                String contentType = httpURLConnection.getContentType();
                int contentLength = httpURLConnection.getContentLength();
                if (disposition != null) {
                    // 尝试从Content-Disposition获取文件名
                    String[] names = disposition.split(";");
                    for (String name : names) {
                        if (name.trim().startsWith("filename")) {
                            fileName = URLDecoder.decode(name.substring(name.indexOf('=') + 1).trim().replace("\"", ""), UTF_8);
                            break;
                        }
                    }
                }
                // 如果没有从Content-Disposition获取文件名，则尝试从URL中获取
                if (fileName.isEmpty()) {
                    fileName = UUID.randomUUID().toString();
                }
                // 创建文件
                file = new File(saveDir + File.separator + fileName);
                // 读取数据到文件
                InputStream inputStream = httpURLConnection.getInputStream();
                FileOutputStream fileOutputStream = new FileOutputStream(file);
                int bytesRead = -1;
                byte[] buffer = new byte[4096];
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fileOutputStream.write(buffer, 0, bytesRead);
                }
                fileOutputStream.close();
                inputStream.close();
                System.out.println("文件下载完成。");
            } else {
                System.out.println("文件下载失败。");
            }
            httpURLConnection.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    @Test
    void hanldeExcel() {
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(1L)
            .username("admin")
            .tenantId(1L)
            .systemId(1L)
            .telephone("")
            .build();
        String fileKey = "023fa1b0-5a73-4eb2-8d48-1f4d3c52b349";
        List<FileTemplateCreateCommand.SimpleParam> simpleParams = new ArrayList<>();
        FileTemplateCreateCommand.SimpleParam simpleParam1 = new FileTemplateCreateCommand.SimpleParam();
        simpleParam1.setPlaceholder("date");
        simpleParam1.setValue("2024-08-13 15:22:14");
        simpleParams.add(simpleParam1);
        FileTemplateCreateCommand.SimpleParam simpleParam2 = new FileTemplateCreateCommand.SimpleParam();
        simpleParam2.setPlaceholder("count");
        simpleParam2.setValue("总数123123");
        simpleParams.add(simpleParam2);

        //        FileInfoDTO fileInfoDTO = fileTemplateActionService.generateFileTemplateOfSimpleParam(ssoUserAuthInfo, fileKey, simpleParams);
        FileInfoDTO fileInfoDTO = fileTemplateActionService.generateFileTemplateOfMultipleParam(ssoUserAuthInfo, fileKey, getExportMap());
        log.info("fileInfoDTO: {}", fileInfoDTO);
    }


    @Test
    @SneakyThrows
    void uploadTemplate() {
        String name = "应收结算单模板V2.xlsx";
        String fileUrl = "/Users/<USER>/IdeaProjects/genn-trans-support-file/genn-trans-support-fms-service/src/test/resources/结算模板V2/" + name;
        InputStream inputStream = FileUtils.openInputStream(new File(fileUrl));
        InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
        inputStreamFileWrapper.setInputStream(inputStream);
        inputStreamFileWrapper.setName(name);
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(1L)
            .username("admin")
            .tenantId(1L)
            .systemId(1L)
            .build();
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "template-generate", null, null);
        log.info("fileInfoDTO: {}", fileInfoDTO);
    }

    private Map<Integer, Map<String, Object>> getExportMap() {
        Map<Integer, Map<String, Object>> map = new HashMap<>(3);
        // 普通字段sheet
        Map<String, Object> oneMap = new HashMap<>(15);
        oneMap.put("count", "118");
        oneMap.put("date", "2024-08-13 15:22:14");
        oneMap.put("carrier", "承运商名称");

        // 列表sheet
        Map<String, Object> twoMap = new HashMap<>(2);
        List<Map<String, Object>> twoList = new ArrayList<>(3);
        Map<String, Object> twoSubMap = new HashMap<>(4);
        twoSubMap.put("num", "1");
        twoSubMap.put("load", "20230001");
        twoSubMap.put("discharge", "张三");
        twoList.add(twoSubMap);
        Map<String, Object> twoSubMap2 = new HashMap<>(4);
        twoSubMap2.put("num", "2");
        twoSubMap2.put("discharge", "20230002");
        twoSubMap2.put("goods", "李四");
        twoList.add(twoSubMap2);
        Map<String, Object> twoSubMap3 = new HashMap<>(4);
        twoSubMap3.put("num", "3");
        twoSubMap3.put("load", "20230001");
        twoSubMap3.put("goods", "王五");
        twoList.add(twoSubMap3);
        oneMap.put("list", twoList);

        map.put(0, oneMap);
        map.put(1, oneMap);
        return map;
    }
}
