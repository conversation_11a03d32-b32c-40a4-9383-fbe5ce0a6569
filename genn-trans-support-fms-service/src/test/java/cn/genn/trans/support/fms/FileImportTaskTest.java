package cn.genn.trans.support.fms;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.service.action.FileExportTaskActionService;
import cn.genn.trans.support.fms.application.service.action.FileImportTaskActionService;
import cn.genn.trans.support.fms.application.service.action.FileTemplateActionService;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.core.file.InputStreamFileWrapper;
import cn.genn.trans.support.fms.interfaces.command.*;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.support.fms.interfaces.dto.config.ConfigGridDTO;
import cn.genn.trans.support.fms.interfaces.enums.ExportTaskBusinessCodeEnum;
import cn.genn.trans.support.fms.interfaces.enums.ImportTaskBusinessCodeEnum;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.http.HttpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

import static feign.Util.UTF_8;

/**
 * @Date: 2024/4/23
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class FileImportTaskTest {

    @Resource
    private FileImportTaskActionService fileImportTaskActionService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;

    public void setUser() {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            //            throw new AuthException(MessageCode.OPERATOR_USER_INFO_NOT_EXIST);
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(1L)
                .username("kangjian")
                .tenantId(1L)
                .systemId(1L)
                .telephone("***********")
                .build();
            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(ssoUserAuthInfo);
        }
    }

    @Test
    void initTask() throws InterruptedException {
        setUser();
        FileImportTaskSubmitCommand command = new FileImportTaskSubmitCommand();
        command.setBusinessCode(ImportTaskBusinessCodeEnum.COMMON_IMPORT);
//        command.setSubBusinessCode("member-personal-export");
        command.setCallbackParam("{}");
        command.setImportFileKey("31f2ff22-4c7d-4cd2-ae95-8b2bf6af188e");
        fileImportTaskActionService.submit(command);
//        Thread.sleep(100000);
    }

    @Test
    void executeTask() throws InterruptedException {
        //        setUser();
        fileImportTaskActionService.executeTask(1L);
    }

    @Test
    void retry() throws InterruptedException {
        //        setUser();
        fileImportTaskActionService.retry(3L);
    }


    @Test
    @SneakyThrows
    void uploadTemplate() {
        String name = "批量导入实体卡号模版.xlsx";
        String fileUrl = "/Users/<USER>/Downloads/" + name;
        InputStream inputStream = FileUtils.openInputStream(new File(fileUrl));
        InputStreamFileWrapper inputStreamFileWrapper = new InputStreamFileWrapper();
        inputStreamFileWrapper.setInputStream(inputStream);
        inputStreamFileWrapper.setName(name);
        SsoUserAuthInfoDTO ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
            .userId(1L)
            .username("admin")
            .tenantId(1L)
            .systemId(1L)
            .build();
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.uploadFile(inputStreamFileWrapper, ssoUserAuthInfo, "common-import", null, null);
        log.info("fileInfoDTO: {}", fileInfoDTO);
    }

    @Test
    void importExcel() {
        File saveFile = new File("/Users/<USER>/Downloads/总部单个商品导入模版_256_20250109.xlsx");
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        params.setStartRows(0);
        List<Map<String, Object>> excelDataList = ExcelImportUtil.importExcel(saveFile, Map.class, params);
        System.out.println(JsonUtils.toJson(excelDataList));
    }

}
