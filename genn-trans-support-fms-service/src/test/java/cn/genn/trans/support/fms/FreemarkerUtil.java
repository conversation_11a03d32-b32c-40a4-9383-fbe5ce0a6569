package cn.genn.trans.support.fms;

import cn.hutool.core.lang.Assert;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.xhtmlrenderer.layout.LayoutContext;
import org.xhtmlrenderer.layout.SharedContext;
import org.xhtmlrenderer.simple.Graphics2DRenderer;
import org.xhtmlrenderer.swing.Java2DRenderer;

import javax.imageio.ImageIO;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @description 工具类
 */
@Slf4j
public class FreemarkerUtil {
    public static void main(String[] args) throws Exception {
        List<Object> li = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            Map<String, Object> d = new HashMap<>();
            d.put("index", i+1);
            d.put("idCardNo", "110102196310213038");
            li.add(d);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("headName", "结算单个人明细");
        map.put("title", "结算单个人明细");
        map.put("mapList",li);
        ftiToHtmlToImg("pound.ftl", map, 16, 1400);
    }
    /**
     * templatename模板名称，如：test.ftl
     * */
    public static void ftiToHtmlToImg(String templatename, Map<String, Object> map, Integer minFontSize, Integer IMG_WIDTH_PX) throws Exception{
        Assert.notNull(minFontSize, "最小字体大小必传，若生成3000~4000的明细图片数据，需调字体大小");//范例：5px
        Assert.notNull(IMG_WIDTH_PX, "IMG_WIDTH_PX必传（生成的图片画布宽度越大，图片占像素越少，留白越多）");
        map.put("minFontSize", minFontSize+"px");

        String html = FreemarkerUtil.generate(templatename, map);
        String baseCode = FreemarkerUtil.html2ImgBase64(html, IMG_WIDTH_PX, -1);
        baseCode = baseCode.replace("data:image/jpg;base64,","");
        AliossUtil.base64ToImage2(baseCode);
    }

    private static Configuration config = null;

    /**
     * 初始化获取html模板
     */
    static {
        config = new Configuration(Configuration.VERSION_2_3_20);
        config.setDefaultEncoding("UTF-8");
        try {
            config.setClassForTemplateLoading(FreemarkerUtil.class, "/template");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.toString());
        }
    }

    /**
     * 把BufferedImage 图片转base64
     *
     * @param bufferedImage
     * @return
     * @throws Exception
     */
    private static String bufferedImageToBase64(BufferedImage bufferedImage) throws Exception {
        String png_base64;//转换成base64串
        //io流
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(bufferedImage, "png", baos);//写入流中
            byte[] bytes = baos.toByteArray();//转换成字节
            png_base64 = Base64.getEncoder().encodeToString(bytes);
            png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n
        }
        return "data:image/jpg;base64," + png_base64;
    }

    /**
     * 将html转成base64字节
     *
     * @param html
     * @param width
     * @param height
     * @return
     * @throws Exception
     */
    public static String html2ImgBase64(String html, int width, int height) throws Exception {
        byte[] bytes = html.getBytes();
        BufferedImage img;
        try (ByteArrayInputStream bin = new ByteArrayInputStream(bytes)) {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(bin);
            Java2DRenderer renderer = new Java2DRenderer(document, width, height);
            SharedContext sharedContext = renderer.getSharedContext();
            sharedContext.setDotsPerPixel(3);
            sharedContext.setDPI(523);
            img = renderer.getImage();
        }
        return bufferedImageToBase64(img);
    }

    /**
     * 将html转成 图片
     *
     * @param html
     * @param width
     * @param height
     * @return
     * @throws Exception
     */
    public static BufferedImage html2Img(String html, int width, int height) throws Exception {
        byte[] bytes = html.getBytes();
        BufferedImage img;
        //转BufferedImage
        try (ByteArrayInputStream bin = new ByteArrayInputStream(bytes)) {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(bin);
            Java2DRenderer renderer = new Java2DRenderer(document, width, height);
            SharedContext sharedContext = renderer.getSharedContext();
            sharedContext.setDotsPerPixel(3);
            sharedContext.setDPI(523);
            //字体
            Font simsun = getSIMSUN(Font.BOLD, 24);
            sharedContext.setFontMapping("simsun", simsun);//这样设置字体无效
            Map map = new HashMap<>();//设置参数
            map.put(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            map.put(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            map.put(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
            map.put(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            map.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            map.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            map.put(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            map.put(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
            map.put(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
            renderer.setRenderingHints(map);
            img = renderer.getImage();
        }
        return img;
    }

    /**
     * 获取模板数据
     *
     * @param template
     * @param params
     * @return
     * @throws Exception
     */
    public static String generate(String template, Map params) throws Exception {
        Template tp = config.getTemplate(template);
        tp.setEncoding("UTF-8");
        StringWriter stringWriter = new StringWriter();
        String htmlStr;
        try {
            tp.process(params, stringWriter);
            htmlStr = stringWriter.toString();
            stringWriter.flush();
        } finally {
            stringWriter.close();
        }
        return htmlStr;
    }

    /**
     * 宋体
     *
     * @param style Font.BOLD
     * @param size  24
     */
    public static Font getSIMSUN(int style, float size) {
        Font font = null;
        //获取字体流
        InputStream simsunFontFile = FreemarkerUtil.class.getResourceAsStream("/fonts/simsun.ttc");
        try {
            //创建字体
            font = Font.createFont(Font.PLAIN, simsunFontFile).deriveFont(style, size);
        } catch (FontFormatException e) {
            log.error("", e);
        } catch (IOException e) {
            font = new Font("STSong-Light", Font.PLAIN, 6);
            log.error("", e);
        }
        return font;
    }
}
