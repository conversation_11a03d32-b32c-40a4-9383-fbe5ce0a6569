package cn.genn.trans.support.fms;


import cn.hutool.core.img.Img;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class TableImageGenerator {

    public static void main(String[] args) throws IOException {
        // 模拟数据
        List<String[]> rows = Arrays.asList(
            new String[]{"Header1", "Header2", "Header3"},
            new String[]{"Row1-Cell1", "Row1-Cell2", "Row1-Cell3"},
            new String[]{"Row2-Cell1", "Row2-Cell2", "Row2-Cell3"}
        );

        // 创建图片
        BufferedImage image = new BufferedImage(400, 200, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
        g2d.setColor(Color.BLACK);

        // 绘制表格
        int x = 10;
        int y = 20;
        int rowHeight = 30;
        int cellWidth = (image.getWidth() - 2 * x) / 3;
        for (int i = 0; i < rows.size(); i++) {
            String[] cells = rows.get(i);
            for (int j = 0; j < cells.length; j++) {
                g2d.drawRect(x + j * cellWidth, y + i * rowHeight, cellWidth, rowHeight);
                g2d.drawString(cells[j], x + j * cellWidth + 5, y + i * rowHeight + 20);
            }
        }

        g2d.dispose();

        // 保存图片
        Img.from(image).write(new File("./table.png"));
    }
}
