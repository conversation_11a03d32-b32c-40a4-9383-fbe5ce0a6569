package cn.genn.trans.support.fms;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.support.fms.application.command.Md2HtmlCommand;
import cn.genn.trans.support.fms.application.command.UploadHtmlCommand;
import cn.genn.trans.support.fms.application.service.ToolSupportService;
import cn.genn.trans.support.fms.application.service.action.FileTemplateActionService;
import cn.genn.trans.support.fms.application.service.action.FileUploadSupportActionService;
import cn.genn.trans.support.fms.interfaces.command.FileDetailQuerySingleCommand;
import cn.genn.trans.support.fms.interfaces.command.FileTemplateCreateCommand;
import cn.genn.trans.support.fms.interfaces.dto.FileInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Date: 2024/5/22
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class TemplateTest {

    @Resource
    private FileTemplateActionService fileTemplateActionService;
    @Resource
    private FileUploadSupportActionService fileUploadSupportActionService;
    @Resource
    private ToolSupportService toolSupportService;

    @Test
    public void testHasTotal() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> map1 = new HashMap<String, Object>();
        map1.put("companya", "客户名称a");
        map1.put("companyb", "客户名称b");
        try {
            XWPFDocument doc = WordExportUtil
                .exportWord07("合同模板.docx", map1);
            FileOutputStream fos = new FileOutputStream("./合同模板_test.docx");
            doc.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void test2() {
        EasyExcel.write("", DemoData.class).includeColumnFieldNames(Arrays.asList()).sheet("模板")
            .doWrite(new ArrayList<DemoData>());
    }

    @Test
    public void test() {
        String base64String = "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";
        byte[] decodedBytes = Base64.getDecoder().decode(base64String);
        ByteArrayInputStream b = new ByteArrayInputStream(decodedBytes);
    }

    @Test
    public void poundImageGen() {
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(1L)
                .username("admin")
                .tenantId(1L)
                .systemId(3L)
                .telephone("")
                .build();
        }
        List<FileTemplateCreateCommand.SimpleParam> simpleParams = new ArrayList<>();
        FileTemplateCreateCommand.SimpleParam simpleParam = new FileTemplateCreateCommand.SimpleParam();
        simpleParam.setPlaceholder("key");
        simpleParam.setValue("value");
        simpleParams.add(simpleParam);
        fileTemplateActionService.generateFileTemplateOfPictureTemplate(ssoUserAuthInfo, "pfdz-pound-image", "磅单xxx图片.png", simpleParams);
    }


    @Test
    public void testExcelTemplateGen() {
        /**
         * {"businessCode":"carrier-fee-detail-tms","simpleParams":[],"cellRangeAddressParam":[{"rowName":"费用类型","options":["通行费","定制类型","年检费","维修费"],"defaultValue":"通行费","firstRow":1,"lastRow":10000},{"rowName":"费用属性","options":["一次性费用","周期性费用"],"defaultValue":"一次性费用","firstRow":1,"lastRow":10000}],"fileName":null,"tenantId":null,"systemId":null,"userName":null,"userId":null}, result={"id":15062,"fileKey":"6698daab-6982-4e48-86e8-d130c445172a","systemId":3,"tenantId":223,"businessCode":"template-generate","url":"https://genn-sit-bucket-tenant-1.obs.cn-north-4.myhuaweicloud.com:443/tenant_223/template/generate/676a8a8ce4b0fcbd4185afb2.xlsx?AccessKeyId=2AHYQERVOB1UBKAKUM90&Expires=1829643706&Signature=pee7pNBx9yqX9xZDb2bwB%2Bpd%2FiU%3D","size":9853,"filename":"676a8a8ce4b0fcbd4185afb2.xlsx","originalFilename":"经营开支上传模板_tms 2.xlsx","path":"tenant_223/template/generate/","ext":"xlsx","contentType":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","platform":"huawei-obs-tenant-223","metadata":{},"userMetadata":{},"thMetadata":{},"thUserMetadata":{},"attr":{},"createTime":"2024-12-24 18:18:53","createUserId":358,"createUserName":"xmd"}
         */
        SsoUserAuthInfoDTO ssoUserAuthInfo = CurrentUserHolder.getCurrentUser();
        if (Objects.isNull(ssoUserAuthInfo)) {
            ssoUserAuthInfo = SsoUserAuthInfoDTO.builder()
                .userId(1L)
                .username("admin")
                .tenantId(1L)
                .systemId(3L)
                .telephone("")
                .build();
        }
        List<FileTemplateCreateCommand.SimpleParam> simpleParams = new ArrayList<>();
        FileTemplateCreateCommand.SimpleParam simpleParam = new FileTemplateCreateCommand.SimpleParam();
        simpleParam.setPlaceholder("key");
        simpleParam.setValue("value");
        simpleParams.add(simpleParam);
        Map<String, Object> map = new HashMap<>();
        //        simpleParams.forEach(param -> map.put(param.getPlaceholder(), param.getValue()));
        List<FileTemplateCreateCommand.CellRangeAddress> cellRangeAddressParam = new ArrayList<>();
        FileTemplateCreateCommand.CellRangeAddress address1 = new FileTemplateCreateCommand.CellRangeAddress();
        address1.setRowName("费用类型");
        address1.setOptions(Arrays.asList("通行费", "定制类型", "年检费", "维修费"));
        address1.setDefaultValue("通行费");
        cellRangeAddressParam.add(address1);
        FileTemplateCreateCommand.CellRangeAddress address2 = new FileTemplateCreateCommand.CellRangeAddress();
        address2.setRowName("费用属性");
        address2.setOptions(Arrays.asList("一次性费用", "周期性费用"));
        address2.setDefaultValue("一次性费用");
        cellRangeAddressParam.add(address2);
        FileDetailQuerySingleCommand command = FileDetailQuerySingleCommand.builder()
            .fileKey("ce8cf566-316d-48a8-8a32-9ed1664cda82")
            .build();
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(command);
        fileTemplateActionService.templateGenerate(fileInfoDTO, ssoUserAuthInfo, map, cellRangeAddressParam);
    }

    @Test
    public void getFileInfoByKeyWithoutLogin() {
        FileDetailQuerySingleCommand command = FileDetailQuerySingleCommand.builder()
            .fileKey("659d5e29-bfa5-44be-80a0-fa16d3347c81")
            .build();
        FileInfoDTO fileInfoDTO = fileUploadSupportActionService.getFileInfoByKeyWithoutLogin(command);
        System.out.println(JsonUtils.toJson(fileInfoDTO));
    }

    @Test
    public void md2html() {
        Md2HtmlCommand command = Md2HtmlCommand.builder()
            .htmlTemplate(cn.hutool.core.codec.Base64.encode("<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>至简能源 - 工业数据分析中心</title>\n" +
                "    <!-- Tailwind CSS via CDN -->\n" +
                "    <script src=\"https://cdn.tailwindcss.com\"></script>\n" +
                "    <script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\"></script>\n" +
                "    <script>\n" +
                "        tailwind.config = {\n" +
                "            theme: {\n" +
                "                extend: {\n" +
                "                    colors: {\n" +
                "                        neon: {\n" +
                "                            cyan: '#00faff',     // 优化：更鲜艳的青色\n" +
                "                            violet: '#bf5cff',   // 优化：更柔和的紫色\n" +
                "                            green: '#4dffb8',    // 优化：更亮的绿色\n" +
                "                            pink: '#ff3eac'      // 优化：更醒目的粉色\n" +
                "                        },\n" +
                "                        cyber: {\n" +
                "                            dark: '#060c17',     // 优化：更深的背景色\n" +
                "                            darker: '#030812',   // 优化：更深的暗色\n" +
                "                            medium: '#131d2d',   // 优化：更丰富的中间色\n" +
                "                            light: '#1b2940'     // 优化：更明亮的亮色\n" +
                "                        }\n" +
                "                    },\n" +
                "                    fontFamily: {\n" +
                "                        'display': ['Orbitron', 'sans-serif'],\n" +
                "                        'tech': ['Rajdhani', 'sans-serif'],\n" +
                "                        'code': ['JetBrains Mono', 'monospace']\n" +
                "                    },\n" +
                "                    animation: {\n" +
                "                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',\n" +
                "                        'glow': 'glow 2s ease-in-out infinite alternate',\n" +
                "                        'scan-line': 'scanLine 4s linear infinite',\n" +
                "                        'data-flow': 'dataFlow 15s linear infinite',\n" +
                "                        'number-pulse': 'numberPulse 2s ease-in-out infinite alternate'\n" +
                "                    },\n" +
                "                    keyframes: {\n" +
                "                        glow: {\n" +
                "                            '0%': { boxShadow: '0 0 5px rgba(0, 250, 255, 0.5), 0 0 10px rgba(0, 250, 255, 0.2)' },\n" +
                "                            '100%': { boxShadow: '0 0 10px rgba(0, 250, 255, 0.8), 0 0 20px rgba(0, 250, 255, 0.5), 0 0 30px rgba(0, 250, 255, 0.3)' }\n" +
                "                        },\n" +
                "                        scanLine: {\n" +
                "                            '0%': { transform: 'translateY(-100%)' },\n" +
                "                            '50%': { transform: 'translateY(100%)' },\n" +
                "                            '100%': { transform: 'translateY(-100%)' }\n" +
                "                        },\n" +
                "                        dataFlow: {\n" +
                "                            '0%': { backgroundPosition: '0% 0%' },\n" +
                "                            '100%': { backgroundPosition: '200% 0%' }\n" +
                "                        },\n" +
                "                        numberPulse: {\n" +
                "                            '0%': { textShadow: '0 0 4px rgba(0, 250, 255, 0.5)' },\n" +
                "                            '100%': { textShadow: '0 0 8px rgba(0, 250, 255, 0.8), 0 0 12px rgba(0, 250, 255, 0.4)' }\n" +
                "                        }\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    </script>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            background-color: #060c17;\n" +
                "            color: #e0e0e0;\n" +
                "            font-family: 'Rajdhani', sans-serif;\n" +
                "            overflow-x: hidden;\n" +
                "        }\n" +
                "        \n" +
                "        /* 滚动条样式 */\n" +
                "        ::-webkit-scrollbar {\n" +
                "            width: 6px;\n" +
                "            height: 6px;\n" +
                "        }\n" +
                "        ::-webkit-scrollbar-track {\n" +
                "            background: #131d2d;\n" +
                "        }\n" +
                "        ::-webkit-scrollbar-thumb {\n" +
                "            background: rgba(0, 250, 255, 0.5);\n" +
                "            border-radius: 3px;\n" +
                "        }\n" +
                "        ::-webkit-scrollbar-thumb:hover {\n" +
                "            background: rgba(0, 250, 255, 0.8);\n" +
                "        }\n" +
                "        \n" +
                "        /* 全息效果 */\n" +
                "        .holographic {\n" +
                "            position: relative;\n" +
                "            overflow: hidden;\n" +
                "        }\n" +
                "        .holographic::before {\n" +
                "            content: \"\";\n" +
                "            position: absolute;\n" +
                "            top: 0;\n" +
                "            left: 0;\n" +
                "            right: 0;\n" +
                "            height: 1px;\n" +
                "            background: linear-gradient(to right, transparent, rgba(0, 250, 255, 0.5), transparent);\n" +
                "            animation: scan-line 4s linear infinite;\n" +
                "        }\n" +
                "        \n" +
                "        /* 数据流效果 */\n" +
                "        .data-flow {\n" +
                "            background: linear-gradient(90deg, \n" +
                "                rgba(19, 29, 45, 0), \n" +
                "                rgba(0, 250, 255, 0.1), \n" +
                "                rgba(19, 29, 45, 0), \n" +
                "                rgba(191, 92, 255, 0.1), \n" +
                "                rgba(19, 29, 45, 0)\n" +
                "            );\n" +
                "            background-size: 200% 100%;\n" +
                "            animation: data-flow 15s linear infinite;\n" +
                "        }\n" +
                "        \n" +
                "        /* 霓虹边框 */\n" +
                "        .neon-border {\n" +
                "            position: relative;\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.15);\n" +
                "            animation: glow 2s ease-in-out infinite alternate;\n" +
                "        }\n" +
                "        \n" +
                "        /* Grid背景 */\n" +
                "        .grid-bg {\n" +
                "            background-image: radial-gradient(rgba(0, 250, 255, 0.08) 1px, transparent 1px);\n" +
                "            background-size: 20px 20px;\n" +
                "        }\n" +
                "        \n" +
                "        /* 数字高亮效果 */\n" +
                "        .highlight-number {\n" +
                "            color: #00faff;\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "            font-weight: 700;\n" +
                "            animation: numberPulse 2s ease-in-out infinite alternate;\n" +
                "            position: relative;\n" +
                "            padding: 0 3px;\n" +
                "        }\n" +
                "        \n" +
                "        /* 数据指标卡片 */\n" +
                "        .metric-card {\n" +
                "            background-color: rgba(3, 8, 18, 0.8);\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.2);\n" +
                "            border-radius: 8px;\n" +
                "            padding: 1rem;\n" +
                "            margin: 1rem 0;\n" +
                "            position: relative;\n" +
                "            overflow: hidden;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-card::before {\n" +
                "            content: \"\";\n" +
                "            position: absolute;\n" +
                "            top: 0;\n" +
                "            left: -100%;\n" +
                "            width: 100%;\n" +
                "            height: 3px;\n" +
                "            background: linear-gradient(to right, transparent, #00faff, transparent);\n" +
                "            animation: slideRight 3s linear infinite;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-title {\n" +
                "            color: rgba(255, 255, 255, 0.8);\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            font-size: 0.85rem;\n" +
                "            text-transform: uppercase;\n" +
                "            letter-spacing: 1px;\n" +
                "            margin-bottom: 0.5rem;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-value {\n" +
                "            color: #00faff;\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "            font-size: 1.75rem;\n" +
                "            font-weight: 700;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-unit {\n" +
                "            color: rgba(0, 250, 255, 0.7);\n" +
                "            font-size: 0.9rem;\n" +
                "            margin-left: 3px;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-change {\n" +
                "            display: inline-flex;\n" +
                "            align-items: center;\n" +
                "            font-size: 0.9rem;\n" +
                "            margin-left: 10px;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-change.positive {\n" +
                "            color: #4dffb8;\n" +
                "        }\n" +
                "        \n" +
                "        .metric-change.negative {\n" +
                "            color: #ff3eac;\n" +
                "        }\n" +
                "        \n" +
                "        @keyframes slideRight {\n" +
                "            0% { left: -100%; }\n" +
                "            100% { left: 100%; }\n" +
                "        }\n" +
                "        \n" +
                "        /* Markdown内容样式 */\n" +
                "        .markdown-content h1 {\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            font-size: 2rem;\n" +
                "            font-weight: 700;\n" +
                "            color: white;\n" +
                "            margin: 1.5rem 0 1rem;\n" +
                "            padding-bottom: 0.5rem;\n" +
                "            border-bottom: 1px solid rgba(0, 250, 255, 0.3);\n" +
                "            text-shadow: 0 0 10px rgba(0, 250, 255, 0.5);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content h2 {\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            font-size: 1.5rem;\n" +
                "            font-weight: 600;\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "            margin: 1.5rem 0 0.75rem;\n" +
                "            text-shadow: 0 0 5px rgba(0, 250, 255, 0.3);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content h3 {\n" +
                "            font-family: 'Rajdhani', sans-serif;\n" +
                "            font-size: 1.25rem;\n" +
                "            font-weight: 600;\n" +
                "            color: rgba(191, 92, 255, 0.9);\n" +
                "            margin: 1.25rem 0 0.75rem;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content p {\n" +
                "            margin-bottom: 1rem;\n" +
                "            line-height: 1.6;\n" +
                "            color: #b0b0b0;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content ul, .markdown-content ol {\n" +
                "            margin: 0.5rem 0 1rem;\n" +
                "            padding-left: 1.5rem;\n" +
                "            color: #b0b0b0;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content li {\n" +
                "            margin-bottom: 0.25rem;\n" +
                "            position: relative;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content li::before {\n" +
                "            content: \"⦿\";\n" +
                "            color: rgba(0, 250, 255, 0.7);\n" +
                "            position: absolute;\n" +
                "            left: -1.2rem;\n" +
                "            top: 0;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content code {\n" +
                "            background-color: rgba(3, 8, 18, 0.8);\n" +
                "            border-left: 2px solid rgba(0, 250, 255, 0.7);\n" +
                "            padding: 0.125rem 0.4rem;\n" +
                "            border-radius: 0.25rem;\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content pre {\n" +
                "            background-color: rgba(3, 8, 18, 0.9);\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.2);\n" +
                "            padding: 1rem;\n" +
                "            border-radius: 0.5rem;\n" +
                "            overflow-x: auto;\n" +
                "            margin: 1rem 0;\n" +
                "            position: relative;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content pre::before {\n" +
                "            content: \"DATA_MATRIX\";\n" +
                "            position: absolute;\n" +
                "            top: -10px;\n" +
                "            left: 15px;\n" +
                "            background-color: #060c17;\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "            padding: 0 10px;\n" +
                "            font-size: 0.7rem;\n" +
                "            border-radius: 3px;\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            letter-spacing: 1px;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content pre code {\n" +
                "            border-left: none;\n" +
                "            padding: 0;\n" +
                "            background-color: transparent;\n" +
                "            color: #b0b0b0;\n" +
                "            display: block;\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content blockquote {\n" +
                "            border-left: 4px solid rgba(0, 250, 255, 0.7);\n" +
                "            padding: 0.8rem 1rem;\n" +
                "            margin: 1rem 0;\n" +
                "            background-color: rgba(0, 250, 255, 0.05);\n" +
                "            border-radius: 0.25rem;\n" +
                "            position: relative;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content blockquote p {\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "        }\n" +
                "        \n" +
                "        .info-box {\n" +
                "            border: 1px solid rgba(77, 255, 184, 0.3);\n" +
                "            background-color: rgba(77, 255, 184, 0.05);\n" +
                "            padding: 1rem;\n" +
                "            margin: 1.5rem 0;\n" +
                "            border-radius: 0.25rem;\n" +
                "            position: relative;\n" +
                "            box-shadow: 0 0 15px rgba(77, 255, 184, 0.15);\n" +
                "        }\n" +
                "        \n" +
                "        .info-box::before {\n" +
                "            content: \"i\";\n" +
                "            position: absolute;\n" +
                "            top: -12px;\n" +
                "            left: 10px;\n" +
                "            width: 24px;\n" +
                "            height: 24px;\n" +
                "            background-color: rgba(77, 255, 184, 0.9);\n" +
                "            color: #030812;\n" +
                "            border-radius: 50%;\n" +
                "            display: flex;\n" +
                "            align-items: center;\n" +
                "            justify-content: center;\n" +
                "            font-weight: bold;\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "        }\n" +
                "        \n" +
                "        .warning-box {\n" +
                "            border: 1px solid rgba(255, 62, 172, 0.3);\n" +
                "            background-color: rgba(255, 62, 172, 0.05);\n" +
                "            padding: 1rem;\n" +
                "            margin: 1.5rem 0;\n" +
                "            border-radius: 0.25rem;\n" +
                "            position: relative;\n" +
                "            box-shadow: 0 0 15px rgba(255, 62, 172, 0.15);\n" +
                "        }\n" +
                "        \n" +
                "        .warning-box::before {\n" +
                "            content: \"!\";\n" +
                "            position: absolute;\n" +
                "            top: -12px;\n" +
                "            left: 10px;\n" +
                "            width: 24px;\n" +
                "            height: 24px;\n" +
                "            background-color: rgba(255, 62, 172, 0.9);\n" +
                "            color: #030812;\n" +
                "            border-radius: 50%;\n" +
                "            display: flex;\n" +
                "            align-items: center;\n" +
                "            justify-content: center;\n" +
                "            font-weight: bold;\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content table {\n" +
                "            width: 100%;\n" +
                "            border-collapse: separate;\n" +
                "            border-spacing: 0;\n" +
                "            margin: 1.5rem 0;\n" +
                "            border-radius: 0.5rem;\n" +
                "            overflow: hidden;\n" +
                "            box-shadow: 0 0 15px rgba(0, 250, 255, 0.1);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content th {\n" +
                "            background-color: rgba(0, 250, 255, 0.1);\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "            padding: 0.75rem 1rem;\n" +
                "            text-align: left;\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            font-weight: 500;\n" +
                "            border-bottom: 1px solid rgba(0, 250, 255, 0.2);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content td {\n" +
                "            padding: 0.75rem 1rem;\n" +
                "            border-bottom: 1px solid rgba(0, 250, 255, 0.1);\n" +
                "            color: #b0b0b0;\n" +
                "        }\n" +
                "        \n" +
                "        /* 表格中的数字特殊高亮 */\n" +
                "        .markdown-content td:nth-child(n+2):not(:last-child) {\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content tr:last-child td {\n" +
                "            border-bottom: none;\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content tr:nth-child(odd) {\n" +
                "            background-color: rgba(0, 250, 255, 0.02);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content tr:hover {\n" +
                "            background-color: rgba(0, 250, 255, 0.05);\n" +
                "        }\n" +
                "        \n" +
                "        .markdown-content strong {\n" +
                "            font-weight: 700;\n" +
                "            color: white;\n" +
                "        }\n" +
                "        \n" +
                "        .terminal-cursor::after {\n" +
                "            content: \"_\";\n" +
                "            animation: blink 1s step-start infinite;\n" +
                "        }\n" +
                "        \n" +
                "        @keyframes blink {\n" +
                "            50% { opacity: 0; }\n" +
                "        }\n" +
                "\n" +
                "        /* HUD风格图表 */\n" +
                "        .hud-chart {\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.3);\n" +
                "            border-radius: 8px;\n" +
                "            padding: 20px;\n" +
                "            background-color: rgba(0, 250, 255, 0.03);\n" +
                "            position: relative;\n" +
                "            margin: 35px 0;\n" +
                "        }\n" +
                "        \n" +
                "        .hud-chart::before {\n" +
                "            content: \"GRAPH_DATA\";\n" +
                "            position: absolute;\n" +
                "            top: -12px;\n" +
                "            left: 15px;\n" +
                "            background-color: #060c17;\n" +
                "            color: rgba(0, 250, 255, 0.9);\n" +
                "            padding: 0 10px;\n" +
                "            font-size: 0.8rem;\n" +
                "            font-family: 'Orbitron', sans-serif;\n" +
                "            letter-spacing: 1px;\n" +
                "        }\n" +
                "        \n" +
                "        /* 进度指示器 */\n" +
                "        .progress-bar {\n" +
                "            height: 8px;\n" +
                "            background-color: rgba(3, 8, 18, 0.7);\n" +
                "            border-radius: 4px;\n" +
                "            overflow: hidden;\n" +
                "            margin: 15px 0;\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.2);\n" +
                "        }\n" +
                "        \n" +
                "        .progress-bar-fill {\n" +
                "            height: 100%;\n" +
                "            background: linear-gradient(90deg, #00faff, #bf5cff);\n" +
                "            border-radius: 3px;\n" +
                "            position: relative;\n" +
                "            overflow: hidden;\n" +
                "        }\n" +
                "        \n" +
                "        .progress-bar-fill::after {\n" +
                "            content: \"\";\n" +
                "            position: absolute;\n" +
                "            top: 0;\n" +
                "            left: 0;\n" +
                "            right: 0;\n" +
                "            bottom: 0;\n" +
                "            background: linear-gradient(90deg, \n" +
                "                          transparent 0%, \n" +
                "                          rgba(255, 255, 255, 0.3) 50%, \n" +
                "                          transparent 100%);\n" +
                "            animation: shimmer 2s infinite;\n" +
                "            transform: skewX(-20deg);\n" +
                "        }\n" +
                "        \n" +
                "        @keyframes shimmer {\n" +
                "            0% { transform: translateX(-100%) skewX(-20deg); }\n" +
                "            100% { transform: translateX(100%) skewX(-20deg); }\n" +
                "        }\n" +
                "        \n" +
                "        .progress-text {\n" +
                "            display: flex;\n" +
                "            justify-content: space-between;\n" +
                "            color: rgba(255, 255, 255, 0.7);\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "            font-size: 0.8rem;\n" +
                "            margin-top: 5px;\n" +
                "        }\n" +
                "        \n" +
                "        .progress-percent {\n" +
                "            color: #00faff;\n" +
                "            font-weight: 700;\n" +
                "        }\n" +
                "        \n" +
                "        /* 数据统计卡片组 */\n" +
                "        .stat-grid {\n" +
                "            display: grid;\n" +
                "            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n" +
                "            gap: 1rem;\n" +
                "            margin: 1.5rem 0;\n" +
                "        }\n" +
                "        \n" +
                "        .stat-card {\n" +
                "            background-color: rgba(3, 8, 18, 0.8);\n" +
                "            border: 1px solid rgba(0, 250, 255, 0.2);\n" +
                "            border-radius: 8px;\n" +
                "            padding: 1rem;\n" +
                "            display: flex;\n" +
                "            flex-direction: column;\n" +
                "        }\n" +
                "        \n" +
                "        .stat-label {\n" +
                "            font-size: 0.75rem;\n" +
                "            text-transform: uppercase;\n" +
                "            color: rgba(255, 255, 255, 0.6);\n" +
                "            letter-spacing: 1px;\n" +
                "            margin-bottom: 0.5rem;\n" +
                "        }\n" +
                "        \n" +
                "        .stat-value {\n" +
                "            font-size: 1.75rem;\n" +
                "            font-weight: 700;\n" +
                "            font-family: 'JetBrains Mono', monospace;\n" +
                "            color: #00faff;\n" +
                "            text-shadow: 0 0 8px rgba(0, 250, 255, 0.4);\n" +
                "        }\n" +
                "        \n" +
                "        .stat-subtext {\n" +
                "            color: rgba(255, 255, 255, 0.5);\n" +
                "            font-size: 0.75rem;\n" +
                "            margin-top: 0.5rem;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "\n" +
                "<body class=\"grid-bg\">\n" +
                "    <div class=\"max-w-5xl mx-auto px-4 py-6\">\n" +
                "        <!-- 顶部导航栏 -->\n" +
                "        <div class=\"flex justify-between items-center mb-6 px-2\">\n" +
                "            <div class=\"flex items-center space-x-3\">\n" +
                "                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"36\" height=\"36\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"text-neon-cyan\">\n" +
                "                    <path d=\"M12 2v20M2 12h20M12 2C6.5 12 6.5 12 12 22M12 2c5.5 10 5.5 10 0 20M2 12c10 5.5 10 5.5 20 0M2 12c10-5.5 10-5.5 20 0\"/>\n" +
                "                </svg>\n" +
                "                <span class=\"font-display font-bold text-xl text-white tracking-wider\">QUANTUM<span class=\"text-neon-cyan\">NEXUS</span></span>\n" +
                "            </div>\n" +
                "            <div class=\"text-neon-cyan font-code text-sm\">\n" +
                "                <span class=\"terminal-cursor\">SYSTEM.ACTIVE // ANALYZING_DATA</span>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "\n" +
                "        <div class=\"neon-border rounded-xl overflow-hidden mb-8 relative\">\n" +
                "            <!-- 顶部装饰横条 -->\n" +
                "            <div class=\"h-1 w-full bg-gradient-to-r from-neon-cyan via-neon-violet to-neon-green\"></div>\n" +
                "            \n" +
                "            <!-- 页眉 -->\n" +
                "            <div class=\"holographic bg-cyber-dark p-6 relative\">\n" +
                "                <div class=\"absolute inset-0 opacity-10\">\n" +
                "                    <div class=\"data-flow absolute inset-0\"></div>\n" +
                "                </div>\n" +
                "                <div class=\"flex flex-col md:flex-row justify-between items-start md:items-center\">\n" +
                "                    <div>\n" +
                "                        <h1 class=\"font-display text-3xl text-white mb-1 tracking-wide\">鹏飞工业<span class=\"text-neon-cyan\">分析引擎</span></h1>\n" +
                "                        <p class=\"font-tech text-gray-400\">超维数据分析 · 性能优化评估</p>\n" +
                "                    </div>\n" +
                "                    <div class=\"mt-4 md:mt-0 bg-cyber-medium bg-opacity-70 rounded-lg p-3 border border-neon-cyan border-opacity-20\">\n" +
                "                        <div class=\"flex items-center space-x-2 text-sm\">\n" +
                "                            <div class=\"h-2 w-2 rounded-full bg-neon-green animate-pulse\"></div>\n" +
                "                            <span class=\"text-neon-green font-code\">LIVE_ANALYSIS</span>\n" +
                "                        </div>\n" +
                "                        <div class=\"text-right text-gray-400 text-sm mt-1 font-code\">\n" +
                "                            <span id=\"current-date\"></span> · <span id=\"current-time\"></span>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "            \n" +
                "            <!-- 主内容区 -->\n" +
                "            <div class=\"bg-cyber-darker p-6 md:p-8 relative\">\n" +
                "                <!-- 半透明数据流动画背景 -->\n" +
                "                <div class=\"absolute inset-0 opacity-5\">\n" +
                "                    <div class=\"data-flow absolute inset-0\"></div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <div class=\"relative\">\n" +
                "                    <!-- 扫描线效果 -->\n" +
                "                    <div class=\"absolute inset-0 pointer-events-none overflow-hidden\">\n" +
                "                        <div class=\"absolute left-0 right-0 h-[1px] bg-neon-cyan opacity-20 animate-scan-line\"></div>\n" +
                "                    </div>\n" +
                "                    \n" +
                "                    <!-- 核心指标摘要卡片 -->\n" +
                "                    <div id=\"summary-metrics\" class=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n" +
                "                        <div class=\"metric-card\">\n" +
                "                            <div class=\"metric-title\">系统效率</div>\n" +
                "                            <div class=\"flex items-baseline\">\n" +
                "                                <span class=\"metric-value\">78.3</span>\n" +
                "                                <span class=\"metric-unit\">%</span>\n" +
                "                                <span class=\"metric-change positive\">\n" +
                "                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n" +
                "                                        <polyline points=\"18 15 12 9 6 15\"></polyline>\n" +
                "                                    </svg>\n" +
                "                                    2.7%\n" +
                "                                </span>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                        <div class=\"metric-card\">\n" +
                "                            <div class=\"metric-title\">运行时间</div>\n" +
                "                            <div class=\"flex items-baseline\">\n" +
                "                                <span class=\"metric-value\">2,160</span>\n" +
                "                                <span class=\"metric-unit\">小时</span>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                        <div class=\"metric-card\">\n" +
                "                            <div class=\"metric-title\">能耗比</div>\n" +
                "                            <div class=\"flex items-baseline\">\n" +
                "                                <span class=\"metric-value\">84.8</span>\n" +
                "                                <span class=\"metric-unit\">%</span>\n" +
                "                                <span class=\"metric-change positive\">\n" +
                "                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n" +
                "                                        <polyline points=\"18 15 12 9 6 15\"></polyline>\n" +
                "                                    </svg>\n" +
                "                                    15.2%\n" +
                "                                </span>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                        <div class=\"metric-card\">\n" +
                "                            <div class=\"metric-title\">预测寿命</div>\n" +
                "                            <div class=\"flex items-baseline\">\n" +
                "                                <span class=\"metric-value\">18.9</span>\n" +
                "                                <span class=\"metric-unit\">年</span>\n" +
                "                                <span class=\"metric-change positive\">\n" +
                "                                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n" +
                "                                        <polyline points=\"18 15 12 9 6 15\"></polyline>\n" +
                "                                    </svg>\n" +
                "                                    23.7%\n" +
                "                                </span>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                    \n" +
                "                    <div class=\"markdown-content\" id=\"content\">\n" +
                "                        <!-- Markdown内容将在这里渲染 -->\n" +
                "                        <div class=\"flex justify-center items-center py-16\">\n" +
                "                            <div class=\"animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-neon-cyan\"></div>\n" +
                "                            <div class=\"ml-4 font-tech text-neon-cyan\">正在分析数据，请稍候...</div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "            \n" +
                "            <!-- 页脚 -->\n" +
                "            <div class=\"bg-cyber-dark p-4 border-t border-gray-800\">\n" +
                "                <div class=\"flex flex-col md:flex-row justify-between items-center\">\n" +
                "                    <div class=\"flex items-center text-sm text-gray-500 font-code\">\n" +
                "                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"mr-2\">\n" +
                "                            <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\"></rect>\n" +
                "                            <path d=\"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\"></path>\n" +
                "                        </svg>\n" +
                "                        <span><EMAIL></span>\n" +
                "                    </div>\n" +
                "                    <div class=\"mt-2 md:mt-0 font-display text-xs text-gray-600 tracking-wider\">\n" +
                "                        NEXUS QUANTUM ANALYTICS // v3.7.2 // 授权级别: ALPHA\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "\n" +
                "    <!-- 引入Markdown解析器 -->\n" +
                "    <script>\n" +
                "        // 设置当前日期和时间\n" +
                "        function updateDateTime() {\n" +
                "            const now = new Date();\n" +
                "            const dateOptions = { year: 'numeric', month: 'long', day: 'numeric' };\n" +
                "            document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', dateOptions);\n" +
                "            \n" +
                "            const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };\n" +
                "            document.getElementById('current-time').textContent = now.toLocaleTimeString('zh-CN', timeOptions);\n" +
                "        }\n" +
                "        \n" +
                "        updateDateTime();\n" +
                "        setInterval(updateDateTime, 1000);\n" +
                "        \n" +
                "        // 将Markdown转换为HTML\n" +
                "        function renderMarkdown(markdown) {\n" +
                "            const contentElement = document.getElementById('content');\n" +
                "            \n" +
                "            // 配置marked选项\n" +
                "            marked.setOptions({\n" +
                "                breaks: true,\n" +
                "                gfm: true,\n" +
                "                headerIds: true,\n" +
                "                langPrefix: 'language-',\n" +
                "                highlight: function(code) {\n" +
                "                    // 高亮数字和单位\n" +
                "                    return code.replace(/(\\d+\\.?\\d*)/g, '<span style=\"color: #00faff; font-weight: bold;\">$1</span>')\n" +
                "                          .replace(/(\\|)/g, '<span style=\"color: rgba(255,255,255,0.4);\">$1</span>')\n" +
                "                          .replace(/(kW|kPa|%)/g, '<span style=\"color: #4dffb8;\">$1</span>');\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            // 渲染markdown\n" +
                "            contentElement.innerHTML = marked.parse(markdown);\n" +
                "            \n" +
                "            // 处理特殊元素\n" +
                "            processSpecialElements();\n" +
                "        }\n" +
                "        \n" +
                "        // 处理特殊内容块和数字高亮\n" +
                "        function processSpecialElements() {\n" +
                "            const content = document.getElementById('content');\n" +
                "            \n" +
                "            // 处理信息和警告框\n" +
                "            const blockquotes = content.querySelectorAll('blockquote');\n" +
                "            blockquotes.forEach(blockquote => {\n" +
                "                const text = blockquote.textContent.trim();\n" +
                "                \n" +
                "                if (text.startsWith('信息:')) {\n" +
                "                    blockquote.className = 'info-box';\n" +
                "                    blockquote.innerHTML = blockquote.innerHTML.replace('信息:', '<strong style=\"color: #4dffb8;\">信息:</strong>');\n" +
                "                }\n" +
                "                else if (text.startsWith('警告:')) {\n" +
                "                    blockquote.className = 'warning-box';\n" +
                "                    blockquote.innerHTML = blockquote.innerHTML.replace('警告:', '<strong style=\"color: #ff3eac;\">警告:</strong>');\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            // 处理进度条\n" +
                "            const progressRegex = /(\\d+)%\\s*进度/g;\n" +
                "            const paragraphs = content.querySelectorAll('p');\n" +
                "            paragraphs.forEach(p => {\n" +
                "                const html = p.innerHTML;\n" +
                "                const newHtml = html.replace(progressRegex, (match, percent) => {\n" +
                "                    return `<div class=\"progress-bar\">\n" +
                "                              <div class=\"progress-bar-fill\" style=\"width: ${percent}%\"></div>\n" +
                "                            </div>\n" +
                "                            <div class=\"progress-text\">\n" +
                "                              <span>0%</span>\n" +
                "                              <span class=\"progress-percent\">${percent}%</span>\n" +
                "                              <span>100%</span>\n" +
                "                            </div>`;\n" +
                "                });\n" +
                "                if (html !== newHtml) {\n" +
                "                    p.innerHTML = newHtml;\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            // 处理数据图表区域\n" +
                "            const charts = content.querySelectorAll('div[data-chart]');\n" +
                "            charts.forEach(chart => {\n" +
                "                chart.className = 'hud-chart';\n" +
                "            });\n" +
                "            \n" +
                "            // 高亮数字\n" +
                "            highlightNumbers(content);\n" +
                "            \n" +
                "            // 创建数据统计卡片\n" +
                "            createStatCards(content);\n" +
                "        }\n" +
                "        \n" +
                "        // 高亮文本中的数字\n" +
                "        function highlightNumbers(content) {\n" +
                "            // 找到所有文本节点\n" +
                "            const textNodes = [];\n" +
                "            const walk = document.createTreeWalker(content, NodeFilter.SHOW_TEXT, null, false);\n" +
                "            let node;\n" +
                "            while (node = walk.nextNode()) {\n" +
                "                // 排除代码块、表格等特殊区域\n" +
                "                if (!isInSpecialTag(node)) {\n" +
                "                    textNodes.push(node);\n" +
                "                }\n" +
                "            }\n" +
                "            \n" +
                "            // 数字匹配正则 (带有百分号、单位等)\n" +
                "            const numberRegex = /(\\b\\d+\\.?\\d*\\s*(?:%|kPa|kW|小时|天|年)\\b|\\b\\d{2,}\\.?\\d*\\b)/g;\n" +
                "            \n" +
                "            // 替换每个文本节点中的数字\n" +
                "            textNodes.forEach(textNode => {\n" +
                "                const text = textNode.nodeValue;\n" +
                "                if (numberRegex.test(text)) {\n" +
                "                    const fragment = document.createDocumentFragment();\n" +
                "                    let lastIndex = 0;\n" +
                "                    let match;\n" +
                "                    \n" +
                "                    // 重置正则表达式的lastIndex\n" +
                "                    numberRegex.lastIndex = 0;\n" +
                "                    \n" +
                "                    while ((match = numberRegex.exec(text)) !== null) {\n" +
                "                        // 添加匹配前的文本\n" +
                "                        if (match.index > lastIndex) {\n" +
                "                            fragment.appendChild(document.createTextNode(text.substring(lastIndex, match.index)));\n" +
                "                        }\n" +
                "                        \n" +
                "                        // 创建高亮数字的span\n" +
                "                        const span = document.createElement('span');\n" +
                "                        span.className = 'highlight-number';\n" +
                "                        span.textContent = match[0];\n" +
                "                        fragment.appendChild(span);\n" +
                "                        \n" +
                "                        lastIndex = match.index + match[0].length;\n" +
                "                    }\n" +
                "                    \n" +
                "                    // 添加剩余的文本\n" +
                "                    if (lastIndex < text.length) {\n" +
                "                        fragment.appendChild(document.createTextNode(text.substring(lastIndex)));\n" +
                "                    }\n" +
                "                    \n" +
                "                    // 替换原始节点\n" +
                "                    textNode.parentNode.replaceChild(fragment, textNode);\n" +
                "                }\n" +
                "            });\n" +
                "        }\n" +
                "        \n" +
                "        // 检查节点是否在特殊标签内\n" +
                "        function isInSpecialTag(node) {\n" +
                "            let parent = node.parentNode;\n" +
                "            while (parent && parent !== document) {\n" +
                "                const tagName = parent.tagName && parent.tagName.toLowerCase();\n" +
                "                if (tagName === 'code' || tagName === 'pre' || tagName === 'table' || \n" +
                "                    parent.classList.contains('highlight-number') || \n" +
                "                    parent.classList.contains('metric-value')) {\n" +
                "                    return true;\n" +
                "                }\n" +
                "                parent = parent.parentNode;\n" +
                "            }\n" +
                "            return false;\n" +
                "        }\n" +
                "        \n" +
                "        // 创建数据统计卡片组\n" +
                "        function createStatCards(content) {\n" +
                "            // 查找带有特殊标记的部分\n" +
                "            const statSections = content.querySelectorAll('[data-stats]');\n" +
                "            statSections.forEach(section => {\n" +
                "                const items = section.getAttribute('data-stats').split(',');\n" +
                "                const grid = document.createElement('div');\n" +
                "                grid.className = 'stat-grid';\n" +
                "                \n" +
                "                items.forEach(item => {\n" +
                "                    const [label, value, unit, info] = item.split(':');\n" +
                "                    \n" +
                "                    const card = document.createElement('div');\n" +
                "                    card.className = 'stat-card';\n" +
                "                    \n" +
                "                    card.innerHTML = `\n" +
                "                        <div class=\"stat-label\">${label}</div>\n" +
                "                        <div class=\"stat-value\">${value}<span style=\"font-size:0.6em;opacity:0.8;\">${unit}</span></div>\n" +
                "                        ${info ? `<div class=\"stat-subtext\">${info}</div>` : ''}\n" +
                "                    `;\n" +
                "                    \n" +
                "                    grid.appendChild(card);\n" +
                "                });\n" +
                "                \n" +
                "                // 替换原始节点\n" +
                "                section.parentNode.replaceChild(grid, section);\n" +
                "            });\n" +
                "        }\n" +
                "        \n" +
                "        // 示例Markdown内容，科幻风格格式\n" +
                "        const exampleMarkdown = `{mdContent}`;\n" +
                "\n" +
                "        // 页面加载时渲染markdown\n" +
                "        document.addEventListener('DOMContentLoaded', function() {\n" +
                "            renderMarkdown(exampleMarkdown);\n" +
                "        });\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>"))
            .mdContent(cn.hutool.core.codec.Base64.encode("### 一期1号煤气鼓风机电机定子温度专项分析报告（2025-03-20 19:52-20:17）\n" +
                "\n" +
                "#### 一、数据特征分析\n" +
                "1. **测点分布特征**  \n" +
                "   - TE1_41505A（电机定子温度1）：数值区间19.78-19.90℃\n" +
                "   - TE1_41505B（电机定子温度2）：数值区间19.78-19.90℃  \n" +
                "   - TE1_41505C（电机定子温度3）：数值区间19.29-19.41℃\n" +
                "   - 三者温差最大达0.61℃（20:17时A/B点19.90℃ vs C点19.41℃）\n" +
                "\n" +
                "2. **时序波动特征**  \n" +
                "   所有测点呈现稳定波动，最大振幅仅0.6℃。温度曲线呈周期性小幅度起伏（每5分钟波动约±0.2℃），符合电机空载或低负载运行特征。\n" +
                "\n" +
                "#### 二、关联参数对比\n" +
                "1. **同类设备对比**  \n" +
                "   二期1号鼓风机电机定子温度设计上限为95℃（一期为110℃），当前一期设备温度仅为设计值的17%-18%，存在显著运行裕度。\n" +
                "\n" +
                "2. **系统关联性**  \n" +
                "   冷却器后油温（TE1-41501）设计范围25-35℃，实测同期数据未提供，需确认冷却系统是否过度运行导致电机温度偏低。\n" +
                "\n" +
                "#### 三、风险预警分析\n" +
                "1. **异常事件回溯**  \n" +
                "   未发现温度超限记录（标准值：报警下限95℃/跳闸值110℃）。当前温度值处于安全区间的17.6%-18.1%，远低于预警阈值。\n" +
                "\n" +
                "2. **潜在风险提示**  \n" +
                "   - 长期低温运行可能导致润滑剂黏度异常（设计要求运行温度＞25℃）\n" +
                "   - 三相温度偏差持续存在（C点较A/B点低约0.5℃），需排查测温元件校准状态\n" +
                "\n" +
                "#### 四、运维建议\n" +
                "1. **参数优化**  \n" +
                "   建议核查负载率设置，若设备长期处于低负荷状态（当前温度仅为设计上限的18%），可评估降容运行的可行性。\n" +
                "\n" +
                "2. **检测维护**  \n" +
                "   - 对TE1_41505C测温点进行校准校验（近1小时数据持续低于A/B点0.5℃）\n" +
                "   - 检查润滑油系统加热装置，确保油温维持在28-45℃设计范围\n" +
                "\n" +
                "3. **监测强化**  \n" +
                "   增加振动监测数据关联分析（未提供），建立温度-振动联合预警模型（当温度＜20℃且振动＞4.5mm/s时触发系统检查）\n" +
                "\n" +
                "#### 五、技术备注\n" +
                "该设备设计冷却系统配置：\n" +
                "- 冷却水流量：120m³/h（设计值）\n" +
                "- 油冷器换热面积：85㎡  \n" +
                "建议核查实际冷却水流量是否超出工艺需求，导致电机过度冷却。\n" +
                "\n" +
                "---\n" +
                "\n" +
                "> 注：本报告数据来源于2025-03-20时段性监测数据，完整分析需结合振动、电流等关联参数。设备说明书参见西安胜唐鼓风机有限公司《D系列鼓风机维护手册》第7章温度控制系统（手册编号：ST-FJ-2011-07）。"))
            .params(new HashMap<>())
            .build();
        System.out.println(toolSupportService.md2html(command));
    }

    @Test
    public void uploadHtml() {
        String html = "<html>\n" +
            "    <head>\n" +
            "        <meta charset=\"utf-8\">\n" +
            "        \n" +
            "            <script src=\"lib/bindings/utils.js\"></script>\n" +
            "            <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css\" integrity=\"sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==\" crossorigin=\"anonymous\" referrerpolicy=\"no-referrer\" />\n" +
            "            <script src=\"https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js\" integrity=\"sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==\" crossorigin=\"anonymous\" referrerpolicy=\"no-referrer\"></script>\n" +
            "            \n" +
            "        \n" +
            "<center>\n" +
            "<h1></h1>\n" +
            "</center>\n" +
            "\n" +
            "<!-- <link rel=\"stylesheet\" href=\"../node_modules/vis/dist/vis.min.css\" type=\"text/css\" />\n" +
            "<script type=\"text/javascript\" src=\"../node_modules/vis/dist/vis.js\"> </script>-->\n" +
            "        <link\n" +
            "          href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css\"\n" +
            "          rel=\"stylesheet\"\n" +
            "          integrity=\"sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6\"\n" +
            "          crossorigin=\"anonymous\"\n" +
            "        />\n" +
            "        <script\n" +
            "          src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js\"\n" +
            "          integrity=\"sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf\"\n" +
            "          crossorigin=\"anonymous\"\n" +
            "        ></script>\n" +
            "\n" +
            "\n" +
            "        <center>\n" +
            "          <h1></h1>\n" +
            "        </center>\n" +
            "        <style type=\"text/css\">\n" +
            "\n" +
            "             #mynetwork {\n" +
            "                 width: 100%;\n" +
            "                 height: 900px;\n" +
            "                 background-color: #ffffff;\n" +
            "                 border: 1px solid lightgray;\n" +
            "                 position: relative;\n" +
            "                 float: left;\n" +
            "             }\n" +
            "\n" +
            "             \n" +
            "\n" +
            "             \n" +
            "\n" +
            "             \n" +
            "        </style>\n" +
            "    </head>\n" +
            "\n" +
            "\n" +
            "    <body>\n" +
            "        <div class=\"card\" style=\"width: 100%\">\n" +
            "            \n" +
            "            \n" +
            "            <div id=\"mynetwork\" class=\"card-body\"></div>\n" +
            "        </div>\n" +
            "\n" +
            "        \n" +
            "        \n" +
            "\n" +
            "        <script type=\"text/javascript\">\n" +
            "\n" +
            "              // initialize global variables.\n" +
            "              var edges;\n" +
            "              var nodes;\n" +
            "              var allNodes;\n" +
            "              var allEdges;\n" +
            "              var nodeColors;\n" +
            "              var originalNodes;\n" +
            "              var network;\n" +
            "              var container;\n" +
            "              var options, data;\n" +
            "              var filter = {\n" +
            "                  item : '',\n" +
            "                  property : '',\n" +
            "                  value : []\n" +
            "              };\n" +
            "\n" +
            "              \n" +
            "\n" +
            "              \n" +
            "\n" +
            "              // This method is responsible for drawing the graph, returns the drawn network\n" +
            "              function drawGraph() {\n" +
            "                  var container = document.getElementById('mynetwork');\n" +
            "\n" +
            "                  \n" +
            "\n" +
            "                  // parsing and collecting nodes and edges from the python\n" +
            "                  nodes = new vis.DataSet([{\"group\": \"object\", \"id\": \"c63e9d6b-170a-4612-ab62-5074803b106a\", \"label\": \"\\n            \\u5b9e\\u4f53: \\u515a\\u7eaa\\u5b66\\u4e60\\u6559\\u80b2\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u5185\\u6559\\u80b2\\u6d3b\\u52a8\\n            \\u63cf\\u8ff0: \\u7eaa\\u68c0\\u76d1\\u5bdf\\u673a\\u5173\\u9700\\u91cd\\u70b9\\u843d\\u5b9e\\u7684\\u7eaa\\u5f8b\\u6559\\u80b2\\u4e13\\u9879\\u5de5\\u4f5c\\n            \", \"shape\": \"dot\", \"size\": 30, \"title\": \"\\u003cb\\u003e\\n            \\u5b9e\\u4f53: \\u515a\\u7eaa\\u5b66\\u4e60\\u6559\\u80b2\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u5185\\u6559\\u80b2\\u6d3b\\u52a8\\n            \\u63cf\\u8ff0: \\u7eaa\\u68c0\\u76d1\\u5bdf\\u673a\\u5173\\u9700\\u91cd\\u70b9\\u843d\\u5b9e\\u7684\\u7eaa\\u5f8b\\u6559\\u80b2\\u4e13\\u9879\\u5de5\\u4f5c\\n            \\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"object\", \"id\": \"995f09da-5e04-46f1-85c7-8069e9c5f435\", \"label\": \"\\n            \\u5b9e\\u4f53: \\u7eaa\\u68c0\\u76d1\\u5bdf\\u673a\\u5173\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u653f\\u76d1\\u7763\\u673a\\u6784\\n            \\u63cf\\u8ff0: \\u627f\\u62c5\\u515a\\u7eaa\\u5b66\\u4e60\\u6559\\u80b2\\u4e3b\\u4f53\\u8d23\\u4efb\\u7684\\u76d1\\u7763\\u90e8\\u95e8\\n            \", \"shape\": \"dot\", \"size\": 30, \"title\": \"\\u003cb\\u003e\\n            \\u5b9e\\u4f53: \\u7eaa\\u68c0\\u76d1\\u5bdf\\u673a\\u5173\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u653f\\u76d1\\u7763\\u673a\\u6784\\n            \\u63cf\\u8ff0: \\u627f\\u62c5\\u515a\\u7eaa\\u5b66\\u4e60\\u6559\\u80b2\\u4e3b\\u4f53\\u8d23\\u4efb\\u7684\\u76d1\\u7763\\u90e8\\u95e8\\n            \\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"object\", \"id\": \"fd41073f-731c-484c-8936-2416c7fdc25b\", \"label\": \"\\n            \\u5b9e\\u4f53: \\u7eaa\\u5f8b\\u5904\\u5206\\u6761\\u4f8b\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u5185\\u6cd5\\u89c4\\n            \\u63cf\\u8ff0: \\u65b0\\u4fee\\u8ba2\\u7684\\u516d\\u9879\\u7eaa\\u5f8b\\u6838\\u5fc3\\u6307\\u5bfc\\u6587\\u4ef6\\n            \", \"shape\": \"dot\", \"size\": 30, \"title\": \"\\u003cb\\u003e\\n            \\u5b9e\\u4f53: \\u7eaa\\u5f8b\\u5904\\u5206\\u6761\\u4f8b\\n            \\u4e1a\\u52a1\\u7c7b\\u578b: \\u515a\\u5185\\u6cd5\\u89c4\\n            \\u63cf\\u8ff0: \\u65b0\\u4fee\\u8ba2\\u7684\\u516d\\u9879\\u7eaa\\u5f8b\\u6838\\u5fc3\\u6307\\u5bfc\\u6587\\u4ef6\\n            \\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"property\", \"id\": \"prop_c63e9d6b-170a-4612-ab62-5074803b106a_d40f404d-191f-4b0d-9322-506193044090_\\u66f4\\u9ad8\\u6807\\u51c6\\u3001\\u66f4\\u4e25\\u8981\\u6c42\", \"label\": \"\\u5b9e\\u65bd\\u6807\\u51c6: \\u66f4\\u9ad8\\u6807\\u51c6\\u3001\\u66f4\\u4e25\\u8981\\u6c42 \\u7eaa\\u68c0\\u76d1\\u5bdf\\u673a\\u5173\\u9700\\u5148\\u5b66\\u4e00\\u6b65\\u3001\\u5b66\\u6df1\\u4e00\\u5c42\\u7684\\u8981\\u6c42\", \"shape\": \"box\", \"title\": \"\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"property\", \"id\": \"prop_995f09da-5e04-46f1-85c7-8069e9c5f435_f17e94ea-e212-46a7-ac61-c9e6126cce81_\\u7ef4\\u62a4\\u515a\\u7684\\u7eaa\\u5f8b\\u3001\\u63a8\\u8fdb\\u5168\\u9762\\u4ece\\u4e25\\u6cbb\\u515a\", \"label\": \"\\u804c\\u8d23\\u5b9a\\u4f4d: \\u7ef4\\u62a4\\u515a\\u7684\\u7eaa\\u5f8b\\u3001\\u63a8\\u8fdb\\u5168\\u9762\\u4ece\\u4e25\\u6cbb\\u515a \\u5de9\\u56fa\\u4e3b\\u9898\\u6559\\u80b2\\u6210\\u679c\\u3001\\u89e3\\u51b3\\u961f\\u4f0d\\u7eaa\\u5f8b\\u8ba4\\u77e5\\u95ee\\u9898\\u7684\\u804c\\u80fd\\u5b9a\\u4f4d\", \"shape\": \"box\", \"title\": \"\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"property\", \"id\": \"prop_fd41073f-731c-484c-8936-2416c7fdc25b_252b9468-f391-4aaf-98ee-8d5debf01071_\\u9010\\u7ae0\\u9010\\u6761\\u5b66\\u4e60\\u3001\\u878d\\u4f1a\\u8d2f\\u901a\", \"label\": \"\\u5b66\\u4e60\\u8981\\u6c42: \\u9010\\u7ae0\\u9010\\u6761\\u5b66\\u4e60\\u3001\\u878d\\u4f1a\\u8d2f\\u901a \\u51c6\\u786e\\u628a\\u63e1\\u516d\\u9879\\u7eaa\\u5f8b\\u4e3b\\u65e8\\u8981\\u4e49\\u7684\\u5b66\\u4e60\\u65b9\\u6cd5\", \"shape\": \"box\", \"title\": \"\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"action\", \"id\": \"act_8b5ff827-c042-4454-8af6-21beff5710e1\", \"label\": \"\\u90e8\\u7f72\\u6559\\u80b2\\u5de5\\u4f5c\", \"shape\": \"diamond\", \"title\": \"\\u003cb\\u003e\\u90e8\\u7f72\\u6559\\u80b2\\u5de5\\u4f5c\\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}, {\"group\": \"action_parameter\", \"id\": \"act_param_31\", \"label\": \"\\u5b9e\\u65bd\\u65b9\\u6848 (string)\", \"shape\": \"polygon\", \"title\": \"\\u003cb\\u003e\\u5b9e\\u65bd\\u65b9\\u6848 (string)\\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eData Type: \\u003c/span\\u003e\"}, {\"group\": \"function\", \"id\": \"func_581aa7b8-0792-4f10-ab62-2d158f45618f\", \"label\": \"\\u5b66\\u4e60\\u6210\\u6548\\u8bc4\\u4f30\", \"shape\": \"polygon\", \"title\": \"\\u003cb\\u003e\\u5b66\\u4e60\\u6210\\u6548\\u8bc4\\u4f30\\u003c/b\\u003e\\u003cbr\\u003e\\u003cspan style=\\u0027font-size:12px;\\u0027\\u003eSummary: \\u003c/span\\u003e\"}]);\n" +
            "                  edges = new vis.DataSet([{\"arrows\": \"to\", \"color\": \"red\", \"from\": \"c63e9d6b-170a-4612-ab62-5074803b106a\", \"label\": \"\\u4f9d\\u636e\\u6587\\u4ef6\", \"title\": \"\\u4f9d\\u636e\\u6587\\u4ef6\", \"to\": \"fd41073f-731c-484c-8936-2416c7fdc25b\"}, {\"arrows\": \"to\", \"color\": \"red\", \"from\": \"c63e9d6b-170a-4612-ab62-5074803b106a\", \"label\": \"\\u5c5e\\u6027\", \"title\": \"\\u5c5e\\u6027\", \"to\": \"prop_c63e9d6b-170a-4612-ab62-5074803b106a_d40f404d-191f-4b0d-9322-506193044090_\\u66f4\\u9ad8\\u6807\\u51c6\\u3001\\u66f4\\u4e25\\u8981\\u6c42\"}, {\"arrows\": \"to\", \"color\": \"red\", \"from\": \"995f09da-5e04-46f1-85c7-8069e9c5f435\", \"label\": \"\\u7ec4\\u7ec7\\u5f00\\u5c55\", \"title\": \"\\u7ec4\\u7ec7\\u5f00\\u5c55\", \"to\": \"c63e9d6b-170a-4612-ab62-5074803b106a\"}, {\"arrows\": \"to\", \"color\": \"red\", \"from\": \"995f09da-5e04-46f1-85c7-8069e9c5f435\", \"label\": \"\\u5c5e\\u6027\", \"title\": \"\\u5c5e\\u6027\", \"to\": \"prop_995f09da-5e04-46f1-85c7-8069e9c5f435_f17e94ea-e212-46a7-ac61-c9e6126cce81_\\u7ef4\\u62a4\\u515a\\u7684\\u7eaa\\u5f8b\\u3001\\u63a8\\u8fdb\\u5168\\u9762\\u4ece\\u4e25\\u6cbb\\u515a\"}, {\"arrows\": \"to\", \"color\": \"red\", \"from\": \"995f09da-5e04-46f1-85c7-8069e9c5f435\", \"label\": \"\\u7ed1\\u5b9a\\u52a8\\u4f5c\", \"title\": \"\\u7ed1\\u5b9a\\u52a8\\u4f5c\", \"to\": \"act_8b5ff827-c042-4454-8af6-21beff5710e1\"}, {\"arrows\": \"to\", \"color\": \"red\", \"from\": \"fd41073f-731c-484c-8936-2416c7fdc25b\", \"label\": \"\\u5c5e\\u6027\", \"title\": \"\\u5c5e\\u6027\", \"to\": \"prop_fd41073f-731c-484c-8936-2416c7fdc25b_252b9468-f391-4aaf-98ee-8d5debf01071_\\u9010\\u7ae0\\u9010\\u6761\\u5b66\\u4e60\\u3001\\u878d\\u4f1a\\u8d2f\\u901a\"}, {\"arrows\": \"to\", \"color\": \"blue\", \"from\": \"act_8b5ff827-c042-4454-8af6-21beff5710e1\", \"label\": \"\\u53c2\\u6570\", \"title\": \"\\u53c2\\u6570\", \"to\": \"act_param_31\"}, {\"arrows\": \"to\", \"color\": \"purple\", \"from\": \"act_8b5ff827-c042-4454-8af6-21beff5710e1\", \"label\": \"\\u7ed1\\u5b9a\\u51fd\\u6570\", \"title\": \"\\u7ed1\\u5b9a\\u51fd\\u6570\", \"to\": \"func_581aa7b8-0792-4f10-ab62-2d158f45618f\"}]);\n" +
            "\n" +
            "                  nodeColors = {};\n" +
            "                  allNodes = nodes.get({ returnType: \"Object\" });\n" +
            "                  for (nodeId in allNodes) {\n" +
            "                    nodeColors[nodeId] = allNodes[nodeId].color;\n" +
            "                  }\n" +
            "                  allEdges = edges.get({ returnType: \"Object\" });\n" +
            "                  // adding nodes and edges to the graph\n" +
            "                  data = {nodes: nodes, edges: edges};\n" +
            "\n" +
            "                  var options = {\n" +
            "    \"configure\": {\n" +
            "        \"enabled\": false\n" +
            "    },\n" +
            "    \"edges\": {\n" +
            "        \"color\": {\n" +
            "            \"inherit\": true\n" +
            "        },\n" +
            "        \"smooth\": {\n" +
            "            \"enabled\": true,\n" +
            "            \"type\": \"dynamic\"\n" +
            "        }\n" +
            "    },\n" +
            "    \"interaction\": {\n" +
            "        \"dragNodes\": true,\n" +
            "        \"hideEdgesOnDrag\": false,\n" +
            "        \"hideNodesOnDrag\": false\n" +
            "    },\n" +
            "    \"physics\": {\n" +
            "        \"barnesHut\": {\n" +
            "            \"avoidOverlap\": 1,\n" +
            "            \"centralGravity\": 0.3,\n" +
            "            \"damping\": 0.09,\n" +
            "            \"gravitationalConstant\": -2000,\n" +
            "            \"springConstant\": 0.001,\n" +
            "            \"springLength\": 200\n" +
            "        },\n" +
            "        \"enabled\": true,\n" +
            "        \"stabilization\": {\n" +
            "            \"enabled\": true,\n" +
            "            \"fit\": true,\n" +
            "            \"iterations\": 1000,\n" +
            "            \"onlyDynamicEdges\": false,\n" +
            "            \"updateInterval\": 50\n" +
            "        }\n" +
            "    }\n" +
            "};\n" +
            "\n" +
            "                  \n" +
            "\n" +
            "\n" +
            "                  \n" +
            "\n" +
            "                  network = new vis.Network(container, data, options);\n" +
            "\n" +
            "                  \n" +
            "\n" +
            "                  \n" +
            "\n" +
            "                  \n" +
            "\n" +
            "\n" +
            "                  \n" +
            "\n" +
            "                  return network;\n" +
            "\n" +
            "              }\n" +
            "              drawGraph();\n" +
            "        </script>\n" +
            "    </body>\n" +
            "</html>";
        UploadHtmlCommand command = new UploadHtmlCommand();
        command.setContent(html);
        String s = toolSupportService.uploadHtml(command);
        System.out.println(s);
    }
}
