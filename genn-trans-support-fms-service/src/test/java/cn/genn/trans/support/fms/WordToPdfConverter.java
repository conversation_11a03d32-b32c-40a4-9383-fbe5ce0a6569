package cn.genn.trans.support.fms;

import cn.wisewe.docx4j.convert.builder.document.DocumentConvertType;
import cn.wisewe.docx4j.convert.builder.document.DocumentConverter;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;

public class WordToPdfConverter {
    @Test
    public void test() {
        // 输入文件路径（Word文档）
        String inputFilePath = "/Users/<USER>/IdeaProjects/genn-trans-support-file/genn-trans-support-fms-service/src/test/resources/物泊焦炭补充合同（恩曹-含税）.docx";
        // 输出文件路径（PDF文档）
        String outputFilePath = "/Users/<USER>/IdeaProjects/genn-trans-support-file/genn-trans-support-fms-service/src/test/resources/output.pdf";

        try {
            DocumentConverter.create()
//                .input(new FileInputStream(FileUtil.brotherPath(this.getClass(), inputFilePath)))
//                .output(new FileOutputStream(FileUtil.brotherPath(this.getClass(), outputFilePath)))
                .input(new FileInputStream(inputFilePath))
                .output(new FileOutputStream(outputFilePath))
                .convert(DocumentConvertType.PDF);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
